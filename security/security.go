package security

import (
	"net/http"
	"sync"

	httppb "google.golang.org/genproto/googleapis/rpc/http"
)

type (
	Config struct {
		Enabled bool              `json:",default=true"`
		Headers map[string]string `json:",optional,omitempty"`
	}

	handler struct {
		enabled bool
		headers map[string]string
		mu      sync.RWMutex
	}
)

var (
	once sync.Once
	h    *handler
)

// InitSecurityHandler init the security handler by config
func InitSecurityHandler(config Config) {
	once.Do(
		func() {
			h = &handler{
				enabled: config.Enabled,
				headers: config.Headers,
			}
		},
	)
}

// HandleHeaders add the security header to the request headers
func HandleHeaders(headers map[string]string) {
	if h == nil {
		return
	}

	h.mu.RLock()
	defer h.mu.RUnlock()

	if !h.enabled {
		return
	}

	for k, v := range h.headers {
		if k == "" {
			continue
		}

		headers[k] = v
	}
}

// HandleHTTPHeaders add the security header to the request headers
func HandleHTTPHeaders(headers http.Header) {
	if h == nil {
		return
	}

	h.mu.RLock()
	defer h.mu.RUnlock()

	if !h.enabled {
		return
	}

	for k, v := range h.headers {
		if k == "" {
			continue
		}

		headers.Add(k, v)
	}
}

// HandleHTTPPBHeaders add the security header to the request headers
func HandleHTTPPBHeaders(headers []*httppb.HttpHeader) []*httppb.HttpHeader {
	if h == nil {
		return headers
	}

	h.mu.RLock()
	defer h.mu.RUnlock()

	if !h.enabled {
		return headers
	}

	for k, v := range h.headers {
		if k == "" {
			continue
		}

		headers = append(
			headers, &httppb.HttpHeader{
				Key:   k,
				Value: v,
			},
		)
	}

	return headers
}

// UpdateConfig update the security handler config
func UpdateConfig(config Config) {
	if h == nil {
		InitSecurityHandler(config)
		return
	}

	h.mu.Lock()
	defer h.mu.Unlock()

	h.enabled = config.Enabled
	h.headers = config.Headers
}
