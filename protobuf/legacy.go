package protobuf

import (
	"reflect"
	"strings"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/utils"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/structpb"
)

type (
	// Deprecated: use ProtoJSONMarshalOptions instead.
	MarshalOptions struct {
		protojson.MarshalOptions

		NilSliceToEmptySlice bool
	}

	// Deprecated: use ProtoJSONUnmarshalOptions instead.
	UnmarshalOptions = protojson.UnmarshalOptions
)

var (
	errUnsupportedType = errors.New("only slice of protobuf message is supported")
	errNotSettable     = errors.New("passed in variable is not settable")

	protoMessage = new(proto.Message)

	// Deprecated: use DefaultProtoJSONMarshalOptions instead.
	DefaultMarshalOptions = MarshalOptions{
		MarshalOptions: protojson.MarshalOptions{
			AllowPartial:    true,
			UseProtoNames:   true,
			UseEnumNumbers:  true,
			EmitUnpopulated: true,
		},
		NilSliceToEmptySlice: true,
	}

	// Deprecated: use DefaultProtoJSONUnmarshalOptions instead.
	DefaultUnmarshalOptions = protojson.UnmarshalOptions{
		AllowPartial:   true,
		DiscardUnknown: true,
	}

	// Deprecated: use ValidateProtoJSONUnmarshalOptions instead.
	ValidateUnmarshalOptions = protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
)

// Marshal writes the given proto.Message in JSON format using default options (DefaultMarshalOptions).
// Deprecated: use MarshalJSON instead.
func Marshal(m proto.Message) ([]byte, error) {
	return MarshalWithOptions(m, DefaultMarshalOptions)
}

// MarshalToString writes the given proto.Message in JSON format using default options (DefaultMarshalOptions).
// Deprecated: use MarshalJSONToString instead.
func MarshalToString(m proto.Message) (string, error) {
	return MarshalToStringWithOptions(m, DefaultMarshalOptions)
}

// MarshalIgnoreError writes the given proto.Message in JSON format using default options (DefaultMarshalOptions).
// Deprecated: use MarshalJSONIgnoreError instead.
func MarshalIgnoreError(m proto.Message) []byte {
	return MarshalIgnoreErrorWithOptions(m, DefaultMarshalOptions)
}

// MarshalToStringIgnoreError writes the given proto.Message in JSON format using default options (DefaultMarshalOptions).
// Deprecated: use MarshalJSONToStringIgnoreError instead.
func MarshalToStringIgnoreError(m proto.Message) string {
	return MarshalToStringIgnoreErrorWithOptions(m, DefaultMarshalOptions)
}

// Unmarshal reads the given []byte into the given proto.Message using default options (DefaultUnmarshalOptions).
// Deprecated: use UnmarshalJSON instead.
func Unmarshal(bytes []byte, m proto.Message) error {
	return UnmarshalWithOptions(bytes, m, DefaultUnmarshalOptions)
}

// UnmarshalFromString reads the given string into the given proto.Message using default options (DefaultUnmarshalOptions).
// Deprecated: use UnmarshalJSONFromString instead.
func UnmarshalFromString(str string, m proto.Message) error {
	return UnmarshalFromStringWithOptions(str, m, DefaultUnmarshalOptions)
}

// Deprecated: use MarshalJSONWithOptions instead.
func MarshalWithOptions(m proto.Message, o MarshalOptions) ([]byte, error) {
	return o.MarshalOptions.Marshal(m)
}

// Deprecated: use MarshalJSONWithOptions instead (convert []byte to string by manual).
func MarshalToStringWithOptions(m proto.Message, o MarshalOptions) (string, error) {
	b, err := MarshalWithOptions(m, o)
	return utils.ByteSliceToString(b), err
}

// Deprecated: use MarshalJSONWithOptions instead (ignore error by manual).
func MarshalIgnoreErrorWithOptions(m proto.Message, o MarshalOptions) []byte {
	b, _ := MarshalWithOptions(m, o)
	return b
}

// Deprecated: use MarshalJSONWithOptions instead (convert []byte to string and ignore error by manual).
func MarshalToStringIgnoreErrorWithOptions(m proto.Message, o MarshalOptions) string {
	s, _ := MarshalToStringWithOptions(m, o)
	return s
}

// Deprecated: use UnmarshalJSONWithOptions instead.
func UnmarshalWithOptions(bytes []byte, m proto.Message, o UnmarshalOptions) error {
	return o.Unmarshal(bytes, m)
}

// Deprecated: use UnmarshalJSONWithOptions instead (convert []byte to string by manual).
func UnmarshalFromStringWithOptions(str string, m proto.Message, o UnmarshalOptions) error {
	return UnmarshalWithOptions(utils.StringToByteSlice(str), m, o)
}

// Deprecated: use MarshalJSONWithMessagesToString instead.
func MarshalSlicePBMessageToString(ms any) (string, error) {
	return MarshalSlicePBMessageToStringWithOptions(ms, DefaultMarshalOptions)
}

// Deprecated: use MarshalJSONWithMessagesToStringIgnoreError instead.
func MarshalSlicePBMessageToStringIgnoreError(ms any) string {
	return MarshalSlicePBMessageToStringIgnoreErrorWithOptions(ms, DefaultMarshalOptions)
}

// Deprecated: use MarshalJSONWithMessagesToStringWithOptions instead.
func MarshalSlicePBMessageToStringWithOptions(ms any, o MarshalOptions) (string, error) {
	rv := reflect.ValueOf(ms)
	for rv.Kind() == reflect.Pointer {
		rv = rv.Elem()
	}

	if !rv.IsValid() || rv.IsNil() {
		if o.NilSliceToEmptySlice {
			return "[]", nil
		}
		return "null", nil
	} else if rv.Kind() != reflect.Slice && rv.Kind() != reflect.Array {
		return "", errUnsupportedType
	}

	sb := strings.Builder{}

	for i := 0; i < rv.Len(); i++ {
		m, ok := rv.Index(i).Interface().(proto.Message)
		if !ok {
			return "", errUnsupportedType
		}

		s, err := MarshalToStringWithOptions(m, o)
		if err != nil {
			return "", err
		}

		if i != 0 {
			sb.WriteString(",")
		}
		sb.WriteString(s)
	}

	return "[" + sb.String() + "]", nil
}

// Deprecated: use MarshalJSONWithMessagesToStringWithOptions instead (ignore error by manual)
func MarshalSlicePBMessageToStringIgnoreErrorWithOptions(ms any, o MarshalOptions) string {
	s, _ := MarshalSlicePBMessageToStringWithOptions(ms, o)
	return s
}

// Deprecated: use UnmarshalJSONWithMessagesFromString instead.
func UnmarshalSlicePBMessageFromString(str string, ms any) error {
	return UnmarshalSlicePBMessageFromStringWithOptions(str, ms, DefaultUnmarshalOptions)
}

// Deprecated: use UnmarshalJSONWithMessagesFromStringWithOptions instead.
func UnmarshalSlicePBMessageFromStringWithOptions(str string, ms any, o UnmarshalOptions) error {
	rv := reflect.ValueOf(ms)
	if !rv.IsValid() || rv.Kind() != reflect.Pointer || rv.IsNil() {
		return errors.Errorf("not a valid pointer: %v", ms)
	}

	rt := reflect.TypeOf(ms)
	rte := rt.Elem()
	rve := rv.Elem()

	// 检查是否切片
	if rte.Kind() != reflect.Slice {
		return errUnsupportedType
	}

	// 检查是否`proto.Message`
	e := rte.Elem()
	if !e.Implements(reflect.TypeOf(protoMessage).Elem()) {
		return errUnsupportedType
	}
	if e.Kind() == reflect.Pointer {
		e = e.Elem()
	}

	// 检查是否可以设置
	if !rve.CanSet() {
		return errNotSettable
	}

	v := &structpb.Value{}
	if err := UnmarshalFromString(str, v); err != nil {
		return err
	}
	lv := v.GetListValue()

	ptr := rte.Elem().Kind() == reflect.Pointer
	values := lv.GetValues()
	rve.Set(reflect.MakeSlice(rte, 0, len(values)))
	for _, value := range values {
		b, err := value.MarshalJSON()
		if err != nil {
			return err
		}

		val := reflect.New(e)
		if err = UnmarshalWithOptions(b, val.Interface().(proto.Message), o); err != nil {
			return err
		}

		if ptr {
			rve.Set(reflect.Append(rve, val))
		} else {
			rve.Set(reflect.Append(rve, reflect.Indirect(val)))
		}
	}

	return nil
}
