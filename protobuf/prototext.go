package protobuf

import (
	"bytes"

	protoV1 "github.com/golang/protobuf/proto"
	"github.com/jhump/protoreflect/dynamic"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/prototext"
	protoV2 "google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/dynamicpb"
)

var (
	defaultProtoTextMarshalOptions   = prototext.MarshalOptions{}
	defaultProtoTextUnmarshalOptions = prototext.UnmarshalOptions{}
	defaultTextPBMarshalOptions      = protoV1.TextMarshaler{}
)

// MarshalText writes the given proto.Message in text-proto format.
func MarshalText(a any) ([]byte, error) {
	switch m := a.(type) {
	case dynamicpb.Message:
		return defaultProtoTextMarshalOptions.Marshal((&m).Interface())
	case *dynamicpb.Message:
		return defaultProtoTextMarshalOptions.Marshal(m.Interface())
	case protoV2.Message:
		return defaultProtoTextMarshalOptions.Marshal(m)
	case dynamic.Message:
		return (&m).MarshalText()
	case *dynamic.Message:
		return m.MarshalText()
	case protoV1.Message:
		var b bytes.Buffer
		if err := defaultTextPBMarshalOptions.Marshal(&b, m); err != nil {
			return nil, err
		}

		return b.Bytes(), nil
	default:
		return nil, errors.Errorf(
			"%T is neither dynamicpb.Message nor protoV2.Message nor dynamic.Message nor protoV1.Message", a,
		)
	}
}

// UnmarshalText reads the given []byte and populates the given proto.Message.
// The provided message must be mutable (e.g., a non-nil pointer to a message).
func UnmarshalText(data []byte, a any) error {
	switch m := a.(type) {
	case dynamicpb.Message:
		return defaultProtoTextUnmarshalOptions.Unmarshal(data, (&m).Interface())
	case *dynamicpb.Message:
		return defaultProtoTextUnmarshalOptions.Unmarshal(data, m.Interface())
	case protoV2.Message:
		return defaultProtoTextUnmarshalOptions.Unmarshal(data, m)
	case dynamic.Message:
		return (&m).UnmarshalText(data)
	case *dynamic.Message:
		return m.UnmarshalText(data)
	case protoV1.Message:
		return protoV1.UnmarshalText(string(data), m)
	default:
		return errors.Errorf(
			"%T is neither a dynamicpb.Message nor protoV2.Message nor dynamic.Message nor protoV1.Message", a,
		)
	}
}
