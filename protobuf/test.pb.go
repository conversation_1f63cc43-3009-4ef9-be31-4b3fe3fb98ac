// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: protobuf/test.proto

package protobuf

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TestEnum int32

const (
	TestEnum_TE_NULL   TestEnum = 0
	TestEnum_TE_RED    TestEnum = 1
	TestEnum_TE_BLUE   TestEnum = 2
	TestEnum_TE_YELLOW TestEnum = 3
	TestEnum_TE_GREEN  TestEnum = 4
)

// Enum value maps for TestEnum.
var (
	TestEnum_name = map[int32]string{
		0: "TE_NULL",
		1: "TE_RED",
		2: "TE_BLUE",
		3: "TE_YELLOW",
		4: "TE_GREEN",
	}
	TestEnum_value = map[string]int32{
		"TE_NULL":   0,
		"TE_RED":    1,
		"TE_BLUE":   2,
		"TE_YELLOW": 3,
		"TE_GREEN":  4,
	}
)

func (x TestEnum) Enum() *TestEnum {
	p := new(TestEnum)
	*p = x
	return p
}

func (x TestEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_protobuf_test_proto_enumTypes[0].Descriptor()
}

func (TestEnum) Type() protoreflect.EnumType {
	return &file_protobuf_test_proto_enumTypes[0]
}

func (x TestEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestEnum.Descriptor instead.
func (TestEnum) EnumDescriptor() ([]byte, []int) {
	return file_protobuf_test_proto_rawDescGZIP(), []int{0}
}

var File_protobuf_test_proto protoreflect.FileDescriptor

var file_protobuf_test_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x74, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2a, 0x76, 0x0a, 0x08, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x0b, 0x0a, 0x07, 0x54, 0x45, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x06,
	0x54, 0x45, 0x5f, 0x52, 0x45, 0x44, 0x10, 0x01, 0x1a, 0x07, 0x82, 0xb5, 0x18, 0x03, 0x72, 0x65,
	0x64, 0x12, 0x15, 0x0a, 0x07, 0x54, 0x45, 0x5f, 0x42, 0x4c, 0x55, 0x45, 0x10, 0x02, 0x1a, 0x08,
	0x82, 0xb5, 0x18, 0x04, 0x42, 0x6c, 0x75, 0x65, 0x12, 0x19, 0x0a, 0x09, 0x54, 0x45, 0x5f, 0x59,
	0x45, 0x4c, 0x4c, 0x4f, 0x57, 0x10, 0x03, 0x1a, 0x0a, 0x82, 0xb5, 0x18, 0x06, 0x59, 0x45, 0x4c,
	0x4c, 0x4f, 0x57, 0x12, 0x16, 0x0a, 0x08, 0x54, 0x45, 0x5f, 0x47, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0x04, 0x1a, 0x08, 0x82, 0xb5, 0x18, 0x04, 0x50, 0x41, 0x53, 0x53, 0x42, 0x40, 0x5a, 0x3e, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x71, 0x65, 0x74, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2d, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protobuf_test_proto_rawDescOnce sync.Once
	file_protobuf_test_proto_rawDescData = file_protobuf_test_proto_rawDesc
)

func file_protobuf_test_proto_rawDescGZIP() []byte {
	file_protobuf_test_proto_rawDescOnce.Do(func() {
		file_protobuf_test_proto_rawDescData = protoimpl.X.CompressGZIP(file_protobuf_test_proto_rawDescData)
	})
	return file_protobuf_test_proto_rawDescData
}

var file_protobuf_test_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_protobuf_test_proto_goTypes = []any{
	(TestEnum)(0), // 0: test.TestEnum
}
var file_protobuf_test_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_protobuf_test_proto_init() }
func file_protobuf_test_proto_init() {
	if File_protobuf_test_proto != nil {
		return
	}
	file_protobuf_options_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protobuf_test_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_protobuf_test_proto_goTypes,
		DependencyIndexes: file_protobuf_test_proto_depIdxs,
		EnumInfos:         file_protobuf_test_proto_enumTypes,
	}.Build()
	File_protobuf_test_proto = out.File
	file_protobuf_test_proto_rawDesc = nil
	file_protobuf_test_proto_goTypes = nil
	file_protobuf_test_proto_depIdxs = nil
}
