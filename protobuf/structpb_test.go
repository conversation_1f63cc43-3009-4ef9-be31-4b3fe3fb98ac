package protobuf

import (
	"fmt"
	"math"
	"strconv"
	"testing"

	"github.com/jmespath/go-jmespath"
)

func TestOverflow(t *testing.T) {
	var intValue int64 = 7140289139090698240
	uintValue := uint64(intValue)
	floatValue := float64(intValue) * 1
	floatToString1 := fmt.Sprintf("%.0f", floatValue)

	floatToInt := int64(floatValue)
	intToString := strconv.FormatInt(intValue, 10)
	floatToString2 := strconv.FormatFloat(floatValue, 'f', -1, 64)
	floatBits := math.Float64bits(floatValue)

	t.Logf(
		"\nintValue: %d\nfloatValue: %.0f\nfloatToInt: %d\nintToString: %s\nfloatToString1: %s\nfloatToString2: %s\nintValue: %064b\nuintValue: %064b\nfloatValue: %064b",
		intValue, floatValue, floatToInt, intToString, floatToString1, floatToString2, intValue, uintValue, floatBits,
	)

	if intToString != floatToString2 {
		t.Logf("precision loss: %d, %s", intValue, floatToString2)
	} else {
		t.Logf("no precision loss: %d, %s", intValue, floatToString2)
	}
}

func TestJMESPath(t *testing.T) {
	data := map[string]any{
		"index_id": 7143525532160012288,
	}
	result, err := jmespath.Search("index_id", data)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("result: %T, %v", result, result)
}
