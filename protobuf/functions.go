package protobuf

import (
	"strconv"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
)

func clearMap[M ~map[K]V, K comparable, V any](m M) {
	// Relies on the compiler optimization introduced in Go v1.11
	// https://go.dev/doc/go1.11#performance-compiler
	for k := range m {
		delete(m, k)
	}
}

// ParseProtoReflectMessage parse the given protoreflect.Message to a map[string]any
// Deprecated: Use ParseMessage instead.
func ParseProtoReflectMessage(in protoreflect.Message) (out map[string]any) {
	out = make(map[string]any, in.Descriptor().Fields().Len())
	in.Range(
		func(fd protoreflect.FieldDescriptor, val protoreflect.Value) bool {
			out[string(fd.Name())] = parseProtoReflectValue(val)
			return true
		},
	)

	return out
}

func parseProtoReflectMap(in protoreflect.Map) (out map[any]any) {
	out = make(map[any]any, in.Len())
	in.Range(
		func(key protoreflect.MapKey, val protoreflect.Value) bool {
			out[key.Interface()] = parseProtoReflectValue(val)
			return true
		},
	)

	return out
}

func parseProtoReflectList(in protoreflect.List) (out []any) {
	out = make([]any, 0, in.Len())
	for i := 0; i < in.Len(); i++ {
		out = append(out, parseProtoReflectValue(in.Get(i)))
	}

	return out
}

func parseProtoReflectValue(in protoreflect.Value) (out any) {
	switch v := in.Interface().(type) {
	case protoreflect.Message:
		return ParseProtoReflectMessage(v)
	case protoreflect.Map:
		return parseProtoReflectMap(v)
	case protoreflect.List:
		return parseProtoReflectList(v)
	case protoreflect.EnumNumber:
		return int32(v)
	default:
		return v
	}
}

func GetEnumStringOf(enum protoreflect.Enum) string {
	n := enum.Number()
	ev := enum.Descriptor().Values().ByNumber(n)
	if ev != nil {
		opts := ev.Options()
		if proto.HasExtension(opts, E_EnumValueAlias) {
			val := proto.GetExtension(opts, E_EnumValueAlias)
			if v, ok := val.(string); ok {
				return v
			}
		}

		return string(ev.Name())
	}

	return strconv.Itoa(int(n))
}

func GetEnumValueAlias(enum protoreflect.Enum) (string, bool) {
	ev := enum.Descriptor().Values().ByNumber(enum.Number())
	if ev == nil {
		return "", false
	}

	opts := ev.Options()
	if !proto.HasExtension(opts, E_EnumValueAlias) {
		return "", false
	}

	val := proto.GetExtension(opts, E_EnumValueAlias)
	v, ok := val.(string)
	return v, ok
}

func GetEnumByString(enum protoreflect.Enum, s string) (protoreflect.Enum, error) {
	values := enum.Descriptor().Values()
	ev := values.ByName(protoreflect.Name(s))
	if ev != nil {
		return enum.Type().New(ev.Number()), nil
	}

	for i := 0; i < values.Len(); i++ {
		ev = values.Get(i)
		opts := ev.Options()
		if !proto.HasExtension(opts, E_EnumValueAlias) {
			continue
		}

		v := proto.GetExtension(opts, E_EnumValueAlias)
		if sv, ok := v.(string); ok && s == sv {
			return enum.Type().New(ev.Number()), nil
		}
	}

	return enum.Type().New(0), errors.Errorf("source value[%s] is not an valid enumeration value of %T", s, enum)
}
