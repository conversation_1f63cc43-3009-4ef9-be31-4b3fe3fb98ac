package protobuf

import (
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"testing"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/protobuf/reflect/protoreflect"
)

func TestProtoManager_RangeServiceOptions(t *testing.T) {
	pm, err := NewProtoManager(
		WithProducts(
			Product{
				Projects: []Project{
					{
						Name:   "app",
						Branch: "master",
						Path:   "../../../TTProjects/app",
					},
				},
			},
		),
	)
	if !assert.NoError(t, err) {
		t.Fatal(err)
	}

	type args struct {
		name    string
		options []FindOption
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "ga.api.channel_core.ChannelCoreLogic",
			args: args{
				name:    "ga.api.channel_core.ChannelCoreLogic",
				options: []FindOption{WithProductBranch("")},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := pm.FindServiceDescriptorByName(tt.args.name, tt.args.options...)
				if err != nil {
					t.Fatal(err)
				}

				rangeWithMessage(t, got.Options().ProtoReflect())
			},
		)
	}
}

func TestProtoManager_RangeMethodOptions(t *testing.T) {
	pm, err := NewProtoManager(
		WithProducts(
			Product{
				Projects: []Project{
					{
						Name:   "app",
						Branch: "master",
						Path:   "../../../TTProjects/app",
					},
				},
			},
		),
	)
	if !assert.NoError(t, err) {
		t.Fatal(err)
	}

	type args struct {
		name    string
		options []FindOption
	}
	tests := []struct {
		name string
		args args
		want uint32
	}{
		{
			name: "deprecated ga.api.auth.AuthCppLogic.Auth",
			args: args{
				name:    "ga.api.auth.AuthCppLogic.Auth",
				options: []FindOption{WithProductBranch("")},
			},
			want: 10,
		},
		{
			name: "ga.api.auth.AuthLogic.Auth",
			args: args{
				name:    "ga.api.auth.AuthLogic.Auth",
				options: []FindOption{WithProductBranch(defaultBranch)},
			},
			want: 10,
		},
		{
			name: "ga.api.channel_core.ChannelCoreLogic.ChannelEnter",
			args: args{
				name: "ga.api.channel_core.ChannelCoreLogic.ChannelEnter",
			},
			want: 423,
		},
		{
			name: "ga.api.channel_core.ChannelCoreLogic.ChannelQuit",
			args: args{
				name: "ga.api.channel_core.ChannelCoreLogic.ChannelQuit",
			},
			want: 424,
		},
		{
			name: "ga.api.channel_core.ChannelCoreLogic.ChannelMicTakeHold",
			args: args{
				name: "ga.api.channel_core.ChannelCoreLogic.ChannelMicTakeHold",
			},
			want: 2069,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := pm.FindMethodDescriptorByName(tt.args.name, tt.args.options...)
				if err != nil {
					t.Fatal(err)
				}

				rangeWithMessage(t, got.Options().ProtoReflect())
			},
		)
	}
}

func rangeWithMessage(t *testing.T, m protoreflect.Message) {
	m.Range(visitWithField(t))
}

func visitWithField(t *testing.T) func(fd protoreflect.FieldDescriptor, v protoreflect.Value) bool {
	return func(fd protoreflect.FieldDescriptor, v protoreflect.Value) bool {
		switch {
		case fd.IsList():
			if _, ok := v.Interface().(protoreflect.List); ok {
				lv := v.List()
				for i := 0; i < lv.Len(); i++ {
					t.Logf("Extension List: %d", i)
					if !visitWithField(t)(fd, lv.Get(i)) {
						return false
					}
				}

				return true
			}
		case fd.IsMap():
			mv := v.Map()
			mv.Range(
				func(key protoreflect.MapKey, value protoreflect.Value) bool {
					t.Logf(
						"Extension Map: %v(%v) => %v(%v)", fd.MapKey().FullName(), key.Interface(),
						fd.MapValue().FullName(), value.Interface(),
					)

					return true
				},
			)

			return true
		}

		switch kind := fd.Kind(); kind {
		case protoreflect.MessageKind, protoreflect.GroupKind:
			t.Logf("Extension, Kind: %v, FullName: %v", kind, fd.FullName())
			rangeWithMessage(t, v.Message())
		case protoreflect.BoolKind:
			t.Logf("Extension, Kind: %v, FullName: %v, Value: %t", kind, fd.FullName(), v.Bool())
		case protoreflect.EnumKind:
			t.Logf("Extension, Kind: %v, FullName: %v, Value: %v", kind, fd.FullName(), v.Enum())
		case protoreflect.DoubleKind, protoreflect.FloatKind:
			t.Logf("Extension, Kind: %v, FullName: %v, Value: %f", kind, fd.FullName(), v.Float())
		case protoreflect.Int32Kind, protoreflect.Int64Kind:
			t.Logf("Extension, Kind: %v, FullName: %v, Value: %d", kind, fd.FullName(), v.Int())
		case protoreflect.Uint32Kind, protoreflect.Uint64Kind:
			t.Logf("Extension, Kind: %v, FullName: %v, Value: %d", kind, fd.FullName(), v.Uint())
		case protoreflect.StringKind:
			t.Logf("Extension, Kind: %v, FullName: %v, Value: %s", kind, fd.FullName(), v.String())
		default:
			t.Logf("Extension, Kind: %v, FullName: %v, Value: %v", kind, fd.FullName(), v.Interface())
		}

		return true
	}
}

func TestProtoManager_GetURIRewrite(t *testing.T) {
	pm, err := NewProtoManager(
		WithProducts(
			Product{
				Projects: []Project{
					{
						Name:   "app",
						Branch: "master",
						Path:   "../../../TTProjects/app",
					},
				},
			},
		),
	)
	if !assert.NoError(t, err) {
		t.Fatal(err)
	}

	type args struct {
		name    string
		options []FindOption
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "with service uri rewrite",
			args: args{
				name: "ga.api.im.ImLogic",
			},
			want: "/logic.ImLogic/",
		},
		{
			name: "without service uri rewrite",
			args: args{
				name: "ga.api.channel.ChannelLogic",
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := pm.FindServiceDescriptorByName(tt.args.name, tt.args.options...)
				if err != nil {
					t.Fatal(err)
				}

				var uri string
				got.Options().ProtoReflect().Range(
					func(fd protoreflect.FieldDescriptor, v protoreflect.Value) bool {
						t.Logf("name: %s, value: %v", fd.FullName(), v.Interface())

						if fd.IsExtension() && fd.Kind() == protoreflect.StringKind && fd.FullName() == "ga.api.extension.logic_service_uri_rewrite" {
							uri = v.String()
						}

						return true
					},
				)

				if !strings.EqualFold(uri, tt.want) {
					t.Errorf("service uri rewrite got = %v, want %v", uri, tt.want)
				}
			},
		)
	}
}

func TestProtoManager_GetLogicCommand(t *testing.T) {
	pm, err := NewProtoManager(
		WithProducts(
			Product{
				Projects: []Project{
					{
						Name:   "app",
						Branch: "master",
						Path:   "../../../TTProjects/app",
					},
				},
			},
		),
	)
	if !assert.NoError(t, err) {
		t.Fatal(err)
	}

	type args struct {
		name    string
		options []FindOption
	}
	type want struct {
		ID              uint32 `json:"id"`
		Deprecated      bool   `json:"deprecated"`
		RewriteFullPath string `json:"rewrite_full_path"`
	}
	tests := []struct {
		name string
		args args
		want want
	}{
		{
			name: "deprecated ga.api.auth.AuthCppLogic.Auth",
			args: args{
				name: "ga.api.auth.AuthCppLogic.Auth",
			},
			want: want{
				ID:         10,
				Deprecated: true,
			},
		},
		{
			name: "ga.api.auth.AuthLogic.Auth",
			args: args{
				name: "ga.api.auth.AuthLogic.Auth",
			},
			want: want{ID: 10},
		},
		{
			name: "ga.api.channel_core.ChannelCoreLogic.ChannelEnter",
			args: args{
				name: "ga.api.channel_core.ChannelCoreLogic.ChannelEnter",
			},
			want: want{ID: 423},
		},
		{
			name: "ga.api.channel_core.ChannelCoreLogic.ChannelQuit",
			args: args{
				name: "ga.api.channel_core.ChannelCoreLogic.ChannelQuit",
			},
			want: want{ID: 424},
		},
		{
			name: "ga.api.channel_core.ChannelCoreLogic.ChannelMicTakeHold",
			args: args{
				name: "ga.api.channel_core.ChannelCoreLogic.ChannelMicTakeHold",
			},
			want: want{ID: 2069},
		},
		{
			name: "ga.api.channel.ChannelLogic.BatchGetChannelList",
			args: args{
				name: "ga.api.channel.ChannelLogic.BatchGetChannelList",
			},
			want: want{
				ID:              449,
				Deprecated:      true,
				RewriteFullPath: "/ga.api.channel_logic_go.ChannelLogicGo/BatchGetChannelList",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := pm.FindMethodDescriptorByName(tt.args.name, tt.args.options...)
				if err != nil {
					t.Fatal(err)
				}

				var want want
				got.Options().ProtoReflect().Range(
					func(fd protoreflect.FieldDescriptor, v protoreflect.Value) bool {
						t.Logf("name: %s, value: %v", fd.FullName(), v.Interface())

						if fd.IsExtension() && fd.Kind() == protoreflect.MessageKind && fd.FullName() == "ga.api.extension.command" {
							bs := MarshalJSONIgnoreError(v.Message().Interface())
							t.Logf("Extension Command got = %s", bs)

							_ = jsonx.Unmarshal(bs, &want)
							return false
						}

						return true
					},
				)

				if !reflect.DeepEqual(want, tt.want) {
					t.Errorf("extension command got = %v, want %v", want, tt.want)
				}
			},
		)
	}
}

func TestProtoManager_FindEnumDescriptorByName(t *testing.T) {
	pm, err := NewProtoManager(
		WithProducts(
			Product{
				Projects: []Project{
					{
						Name:   "app",
						Branch: "master",
						Path:   "../../../TTProjects/app",
					},
				},
			},
		),
	)
	if !assert.NoError(t, err) {
		t.Fatal(err)
	}

	type args struct {
		name    string
		options []FindOption
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "",
			args: args{
				name: "ga.auth.THIRD_PARTY_TYPE",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := pm.FindEnumDescriptorByName(tt.args.name, tt.args.options...)
				if err != nil {
					t.Fatal(err)
				}

				enumValues := got.Values()
				for i := 0; i < enumValues.Len(); i++ {
					enumValue := enumValues.Get(i)
					t.Logf(
						"Enum, FullName: %v, Name: %v, Number: %v",
						enumValue.FullName(), enumValue.Name(), enumValue.Number(),
					)
				}
			},
		)
	}
}

func TestProtoManager_RangeMessageFields(t *testing.T) {
	pm, err := NewProtoManager(
		WithProducts(
			Product{
				Projects: []Project{
					{
						Name:   "app",
						Branch: "master",
						Path:   "../../../TTProjects/app",
					},
				},
			},
		),
	)
	if !assert.NoError(t, err) {
		t.Fatal(err)
	}

	type args struct {
		name    string
		options []FindOption
	}
	tests := []struct {
		name string
		args args
		want uint32
	}{
		{
			name: "ga.auth.AuthReq",
			args: args{
				name: "ga.auth.AuthReq",
			},
			want: 10,
		},
		{
			name: "ga.auth.AuthResp",
			args: args{
				name: "ga.auth.AuthResp",
			},
			want: 10,
		},
		{
			name: "ga.channel.ChannelEnterReq",
			args: args{
				name: "ga.channel.ChannelEnterReq",
			},
			want: 423,
		},
		{
			name: "ga.channel.ChannelEnterResp",
			args: args{
				name: "ga.channel.ChannelEnterResp",
			},
			want: 424,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := pm.FindMessageDescriptorByName(tt.args.name, tt.args.options...)
				if err != nil {
					t.Fatal(err)
				}

				rangeMessageFields(t, got)
			},
		)
	}
}

func rangeMessageFields(t *testing.T, m protoreflect.MessageDescriptor) {
	fields := m.Fields()
	for i := 0; i < fields.Len(); i++ {
		field := fields.Get(i)

		t.Logf(
			"Field, Parent: %v, Kind: %v, FullName: %v, Name: %v", m.FullName(), field.Kind(), field.FullName(),
			field.Name(),
		)

		if field.Kind() == protoreflect.MessageKind {
			rangeMessageFields(t, field.Message())
		}
	}
}

func TestUnmarshalToOneOfMessage(t *testing.T) {
	pm, err := NewProtoManager(
		WithProducts(
			Product{
				Projects: []Project{
					{
						Name:   "app",
						Branch: "master",
						Path:   "../../../TTProjects/app",
					},
				},
			},
		),
	)
	if !assert.NoError(t, err) {
		t.Fatal(err)
	}

	data := `{"channel_subscribe_push_req": {"channel_id": 23456, "type": "UNSUBSCRIBE"}}`
	bs, err := pm.UnmarshalJSONPB("ga.PushReq", []byte(data), WithProductBranch(""))
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("%s", bs)
}

func TestProtoManager_ReloadByBranch(t *testing.T) {
	name := "app"
	path := "../../../TTProjects/app"

	type args struct {
		branch  string
		message string
		commit1 string
		commit2 string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "master - ga.PresentEnterBlacklist",
			args: args{
				branch:  "master",
				message: "ga.PresentEnterBlacklist",
				commit1: "9e68f6caecd6d61862a7e460cd4d5b6ac60c2c02",
				commit2: "585746079adcf16a38f0aff1b9a9c4ed6001e7c0",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r, err := git.PlainOpenWithOptions(
					path, &git.PlainOpenOptions{
						DetectDotGit: true,
					},
				)
				if !assert.NoError(t, err) {
					t.Fatal(err)
				}

				w, err := r.Worktree()
				if !assert.NoError(t, err) {
					t.Fatal(err)
				}

				if err = r.Fetch(&git.FetchOptions{}); !errors.Is(err, git.NoErrAlreadyUpToDate) && !assert.NoError(
					t, err,
				) {
					t.Fatal(err)
				}

				if err = w.Checkout(&git.CheckoutOptions{Hash: plumbing.NewHash(tt.args.commit1)}); !errors.Is(
					err, git.NoErrAlreadyUpToDate,
				) && !assert.NoError(
					t, err,
				) {
					t.Fatal(err)
				}

				pm, err := NewProtoManager(
					WithProducts(
						Product{
							Name:   "",
							Branch: tt.args.branch,
							Projects: []Project{
								{
									Name:   name,
									Branch: tt.args.branch,
									Path:   path,
								},
							},
						},
					),
				)
				if !assert.NoError(t, err) {
					t.Fatal(err)
				}

				md, err := pm.FindMessageDescriptorByName(
					tt.args.message, WithProjectNameAndBranch(name, tt.args.branch),
				)
				if !assert.Error(t, err) {
					t.Fatalf("expected: not found message descriptor[%s], but found: %v", tt.args.message, md)
				}
				t.Logf(
					"branch: %s, message: %s, commit: %s, error: %v",
					tt.args.branch, tt.args.message, tt.args.commit1, err,
				)

				if err = w.Checkout(&git.CheckoutOptions{Hash: plumbing.NewHash(tt.args.commit2)}); !assert.NoError(
					t, err,
				) {
					t.Fatal(err)
				}

				if err = pm.ReloadByBranch(tt.args.branch); !assert.NoError(t, err) {
					t.Fatal(err)
				}

				md, err = pm.FindMessageDescriptorByName(
					tt.args.message, WithProjectNameAndBranch(name, tt.args.branch),
				)
				if !assert.NoError(t, err) {
					t.Fatal(err)
				}
				t.Logf(
					"branch: %s, message: %s, commit: %s, message descriptor: %v",
					tt.args.branch, tt.args.message, tt.args.commit2, md,
				)
			},
		)
	}
}

func TestProtoManager_ParseWithDependencies(t *testing.T) {
	goPath := os.Getenv("GOPATH")

	pm, err := NewProtoManager(
		WithProducts(
			Product{
				Name:   "",
				Branch: "",
				Projects: []Project{
					{
						Name:   "probe-backend",
						Branch: "main",
						Path:   "../../probe-backend/protos",
						ImportPaths: []string{
							filepath.Join(
								goPath, "/pkg/mod/github.com/envoyproxy/protoc-gen-validate@v1.0.4",
							),
							"../../probe-backend/dep_protos/protovalidate/proto/protovalidate",
							"../../qet-backend-common/protos",
							"../../qet-backend-middleware/protos",
						},
					},
				},
			},
		),
	)
	if !assert.NoError(t, err) {
		t.Fatal(err)
	}

	got, err := pm.FindEnumDescriptorByName("common.PlatformType")
	if err != nil {
		t.Fatal(err)
	}

	for i := 0; i < got.Values().Len(); i++ {
		evd := got.Values().Get(i)
		t.Logf("name: %s", evd.Name())

		evd.Options().ProtoReflect().Range(
			func(fieldDescriptor protoreflect.FieldDescriptor, value protoreflect.Value) bool {
				if fieldDescriptor.IsExtension() && fieldDescriptor.Kind() == protoreflect.StringKind && fieldDescriptor.FullName() == "options.enum_value_alias" {
					t.Logf("enum alias: %s", value.String())
				}
				return true
			},
		)
	}
}

func TestProtoManager_ParseStellarisApi(t *testing.T) {
	pm, err := NewProtoManager(
		WithProducts(
			Product{
				Name:   "",
				Branch: "",
				Projects: []Project{
					{
						Name:   "stellaris-api",
						Branch: "main",
						Path:   "../../../TTProjects/stellaris-api",
						ImportPaths: []string{
							"../../../GitHubProjects/googleapis",
						},
					},
				},
			},
		),
	)
	if !assert.NoError(t, err) {
		t.Fatal(err)
	}

	type args struct {
		name    string
		options []FindOption
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "quwan.stellaris.authentication.v1.AuthenticationService.SignIn",
			args: args{
				name: "quwan.stellaris.authentication.v1.AuthenticationService.SignIn",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := pm.FindMethodDescriptorByName(tt.args.name, tt.args.options...)
				if err != nil {
					t.Fatal(err)
				}

				t.Logf(
					"method: %s\ninput: %s\noutput:%s\n",
					got.FullName(), got.Input().FullName(), got.Output().FullName(),
				)
			},
		)
	}
}

func TestProtoManager_MultipleProjects(t *testing.T) {
	branch := "develop"
	pm, err := NewProtoManager(
		WithProducts(
			Product{
				Branch: branch,
				Projects: []Project{
					{
						Name:   "app",
						Branch: branch,
						Path:   "../../../TTProjects/app",
					},
					{
						Name:   "stellaris-api",
						Branch: "main",
						Path:   "../../../TTProjects/stellaris-api",
						ImportPaths: []string{
							"../../../GitHubProjects/googleapis",
						},
					},
				},
			},
		),
	)
	if !assert.NoError(t, err) {
		t.Fatal(err)
	}

	type args struct {
		name    string
		options []FindOption
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "ga.api.auth.AuthLogic.Auth",
			args: args{
				name:    "ga.api.auth.AuthLogic.Auth",
				options: []FindOption{WithProductBranch(branch)},
			},
		},
		{
			name: "quwan.stellaris.authentication.v1.AuthenticationService.SignIn",
			args: args{
				name: "quwan.stellaris.authentication.v1.AuthenticationService.SignIn",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := pm.FindMethodDescriptorByName(tt.args.name, tt.args.options...)
				if err != nil {
					t.Fatal(err)
				}

				t.Logf(
					"method: %s\ninput: %s\noutput:%s\n",
					got.FullName(), got.Input().FullName(), got.Output().FullName(),
				)
			},
		)
	}
}
