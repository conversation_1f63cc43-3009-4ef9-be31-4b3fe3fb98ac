package protobuf

import (
	"sync"

	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/dynamicpb"
)

const (
	defaultMapCapacity uint64 = 64
)

type Pool struct {
	cache *hashmap.Map[protoreflect.FullName, *sync.Pool]
}

func NewPool(capacities ...uint64) Pool {
	capacity := defaultMapCapacity
	if len(capacities) > 0 {
		capacity = capacities[0]
	}

	return Pool{
		cache: hashmap.New[protoreflect.FullName, *sync.Pool](capacity, generic.Equals[protoreflect.FullName], HashFn),
	}
}

func HashFn(s protoreflect.FullName) uint64 {
	return generic.HashString(string(s))
}

func (p Pool) Get(md protoreflect.MessageDescriptor) *dynamicpb.Message {
	pool, ok := p.cache.Get(md.FullName())
	if !ok {
		pool = &sync.Pool{
			New: func() any {
				return dynamicpb.NewMessage(md)
			},
		}
		p.cache.Put(md.FullName(), pool)
	}

	m, ok := pool.Get().(*dynamicpb.Message)
	if !ok {
		return dynamicpb.NewMessage(md)
	}

	return m
}

func (p Pool) Put(m *dynamicpb.Message) {
	if m == nil {
		return
	}

	pool, ok := p.cache.Get(m.Descriptor().FullName())
	if !ok {
		return
	}

	m.Reset()
	pool.Put(m)
}

func (p Pool) Clear() {
	p.cache.Clear()
}
