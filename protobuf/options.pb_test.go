package protobuf

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
)

func TestEnumValueOptions(t *testing.T) {
	for _, test := range []struct {
		enum   TestEnum
		ext    protoreflect.ExtensionType
		hasExt bool
		value  string
	}{
		{
			enum:   TestEnum_TE_NULL,
			ext:    E_EnumValueAlias,
			hasExt: false,
		},
		{
			enum:   TestEnum_TE_RED,
			ext:    E_EnumValueAlias,
			hasExt: true,
			value:  "red",
		},
		{
			enum:   TestEnum_TE_BLUE,
			ext:    E_EnumValueAlias,
			hasExt: true,
			value:  "Blue",
		},
		{
			enum:   TestEnum_TE_YELLOW,
			ext:    E_EnumValueAlias,
			hasExt: true,
			value:  "YELLOW",
		},
		{
			enum:   TestEnum_TE_GREEN,
			ext:    E_EnumValueAlias,
			hasExt: true,
			value:  "PASS",
		},
	} {
		evd := test.enum.Descriptor().Values().ByNumber(test.enum.Number())
		options := evd.Options()
		hasExt := proto.HasExtension(options, test.ext)
		assert.Equal(t, test.hasExt, hasExt)

		if hasExt {
			value := proto.GetExtension(options, test.ext)
			t.Logf("1: number: %d, name: %s, fullname: %s, alias: %s", evd.Number(), evd.Name(), evd.FullName(), value)
			assert.Equal(t, test.value, value)
		} else {
			t.Logf("2: number: %d, name: %s, fullname: %s", evd.Number(), evd.Name(), evd.FullName())
		}
	}
}
