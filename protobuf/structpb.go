package protobuf

import (
	"encoding/base64"
	"encoding/json"
	"reflect"
	"strconv"
	"unicode/utf8"

	"google.golang.org/protobuf/runtime/protoimpl"
	"google.golang.org/protobuf/types/known/structpb"
)

type (
	SPBOption func(*spbOptions)

	spbOptions struct {
		jsonNumberToNumber bool
	}
)

func WithJSONNumberToNumber() SPBOption {
	return func(o *spbOptions) {
		o.jsonNumberToNumber = true
	}
}

// NewValue constructs a Value from a general-purpose Go interface.
// Rewrite the method `google.golang.org/protobuf/types/known/structpb.NewValue`
// in order to support more types.
func NewValue(v any, opts ...SPBOption) (*structpb.Value, error) {
	rv := reflect.ValueOf(v)
	rv = reflect.Indirect(rv)

	switch t := v.(type) {
	case nil:
		return structpb.NewNullValue(), nil
	case json.Number:
		o := spbOptions{}
		for _, opt := range opts {
			opt(&o)
		}

		if o.jsonNumberToNumber {
			if i, e := t.Int64(); e == nil {
				return structpb.NewNumberValue(float64(i)), nil
			} else if f, e := t.Float64(); e == nil {
				return structpb.NewNumberValue(f), nil
			}
		}

		return structpb.NewStringValue(t.String()), nil
	case []byte:
		s := base64.StdEncoding.EncodeToString(t)
		return structpb.NewStringValue(s), nil
	}

	switch rv.Kind() {
	case reflect.Bool:
		return structpb.NewBoolValue(rv.Bool()), nil
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32:
		return structpb.NewNumberValue(float64(rv.Int())), nil
	case reflect.Int64:
		// When converting an int64 or uint64 to a NumberValue,
		// numeric precision loss is possible since they are stored as a float64.

		//i := rv.Int()
		//f := float64(i)
		//is := strconv.FormatInt(i, 10)
		//fs := strconv.FormatFloat(f, 'f', -1, 64)
		//
		//if is != fs {
		//	// numeric precision loss, therefore return *structpb.Value_StringValue
		//	return structpb.NewStringValue(is), nil
		//}
		//
		//return structpb.NewNumberValue(f), nil

		return structpb.NewStringValue(strconv.FormatInt(rv.Int(), 10)), nil
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32:
		return structpb.NewNumberValue(float64(rv.Uint())), nil
	case reflect.Uint64:
		// When converting an int64 or uint64 to a NumberValue,
		// numeric precision loss is possible since they are stored as a float64.

		//i := rv.Uint()
		//f := float64(i)
		//is := strconv.FormatUint(i, 10)
		//fs := strconv.FormatFloat(f, 'f', -1, 64)
		//
		//if is != fs {
		//	// numeric precision loss, therefore return *structpb.Value_StringValue
		//	return structpb.NewStringValue(is), nil
		//}
		//
		//return structpb.NewNumberValue(f), nil

		return structpb.NewStringValue(strconv.FormatUint(rv.Uint(), 10)), nil
	case reflect.Float32, reflect.Float64:
		return structpb.NewNumberValue(rv.Float()), nil
	case reflect.String:
		s := rv.String()
		if !utf8.ValidString(s) {
			return nil, protoimpl.X.NewError("invalid UTF-8 in string: %q", s)
		}

		return structpb.NewStringValue(s), nil
	case reflect.Map:
		m, err := NewMapByValue(rv, opts...)
		if err != nil {
			return nil, err
		}

		return structpb.NewStructValue(m), nil
	case reflect.Slice:
		l, err := NewListByValue(rv, opts...)
		if err != nil {
			return nil, err
		}

		return structpb.NewListValue(l), nil
	default:
		return nil, protoimpl.X.NewError("invalid type: %T", v)
	}
}

// NewStruct constructs a *structpb.Struct from a general-purpose Go map.
// The map keys must be valid UTF-8.
// The map values are converted using NewValue.
func NewStruct(v map[string]any, opts ...SPBOption) (*structpb.Struct, error) {
	x := &structpb.Struct{Fields: make(map[string]*structpb.Value, len(v))}
	for k, v := range v {
		if !utf8.ValidString(k) {
			return nil, protoimpl.X.NewError("invalid UTF-8 in string: %q", k)
		}

		var err error
		x.Fields[k], err = NewValue(v, opts...)
		if err != nil {
			return nil, err
		}
	}

	return x, nil
}

// NewList constructs a *structpb.ListValue from a general-purpose Go slice.
// The slice elements are converted using NewValue.
func NewList(v []any, opts ...SPBOption) (*structpb.ListValue, error) {
	x := &structpb.ListValue{Values: make([]*structpb.Value, len(v))}
	for i, v := range v {
		var err error
		x.Values[i], err = NewValue(v, opts...)
		if err != nil {
			return nil, err
		}
	}

	return x, nil
}

// NewMapByValue constructs a *structpb.Struct from a Map Kind of reflect.Value.
// The map keys must be valid UTF-8.
// The map values are converted using NewValue.
func NewMapByValue(rv reflect.Value, opts ...SPBOption) (x *structpb.Struct, err error) {
	kind := rv.Kind()
	if kind != reflect.Map {
		return nil, protoimpl.X.NewError(
			"invalid kind, expected %q, but got: %q", reflect.Map.String(), kind.String(),
		)
	}

	x = &structpb.Struct{Fields: make(map[string]*structpb.Value, rv.Len())}
	iter := rv.MapRange()
	for iter.Next() {
		if iter.Key().Kind() != reflect.String {
			continue
		}

		key := iter.Key().String()
		val := iter.Value().Interface()
		if !utf8.ValidString(key) {
			return nil, protoimpl.X.NewError("invalid UTF-8 in string: %q", key)
		}

		x.Fields[key], err = NewValue(val, opts...)
		if err != nil {
			return nil, err
		}
	}

	return x, nil
}

// NewListByValue constructs a *structpb.ListValue from a Slice or Array Kind of reflect.Value.
// The slice elements are converted using NewValue.
func NewListByValue(rv reflect.Value, opts ...SPBOption) (x *structpb.ListValue, err error) {
	kind := rv.Kind()
	if kind != reflect.Slice && kind != reflect.Array {
		return nil, protoimpl.X.NewError(
			"invalid kind, expected %q or %q, but got: %q",
			reflect.Slice.String(), reflect.Array.String(), kind.String(),
		)
	}

	x = &structpb.ListValue{Values: make([]*structpb.Value, rv.Len())}
	for i := 0; i < rv.Len(); i++ {
		x.Values[i], err = NewValue(rv.Index(i).Interface(), opts...)
		if err != nil {
			return nil, err
		}
	}

	return x, nil
}
