package protobuf

import (
	"bytes"
	"reflect"
	"strings"

	"github.com/gogo/protobuf/proto"
	"github.com/golang/protobuf/jsonpb"
	protoV1 "github.com/golang/protobuf/proto"
	"github.com/jhump/protoreflect/dynamic"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/utils"
	"google.golang.org/protobuf/encoding/protojson"
	protoV2 "google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/dynamicpb"
	"google.golang.org/protobuf/types/known/structpb"
)

type (
	ProtoJSONMarshalOptions struct {
		protojson.MarshalOptions

		NilSliceToEmptySlice bool
	}
	ProtoJSONUnmarshalOptions struct {
		protojson.UnmarshalOptions
	}
)

var (
	DefaultProtoJSONMarshalOptions = ProtoJSONMarshalOptions{
		MarshalOptions: protojson.MarshalOptions{
			AllowPartial:    true,
			UseProtoNames:   true,
			UseEnumNumbers:  true,
			EmitUnpopulated: true,
		},
		NilSliceToEmptySlice: true,
	}

	DefaultProtoJSONUnmarshalOptions = ProtoJSONUnmarshalOptions{
		protojson.UnmarshalOptions{
			AllowPartial:   true,
			DiscardUnknown: true,
		},
	}

	ValidateProtoJSONUnmarshalOptions = ProtoJSONUnmarshalOptions{
		protojson.UnmarshalOptions{
			DiscardUnknown: true,
		},
	}
)

const (
	constLeftBracket  = "["
	constRightBracket = "]"
	constEmptySlice   = constLeftBracket + constRightBracket
	constNullSlice    = "null"
)

func (o ProtoJSONMarshalOptions) convertToProtoV2MarshalOptions() protojson.MarshalOptions {
	return o.MarshalOptions
}

func (o ProtoJSONMarshalOptions) convertToProtoV1MarshalOptions() *jsonpb.Marshaler {
	os := &jsonpb.Marshaler{}
	if o.Indent != "" {
		os.Indent = o.Indent
	}
	if o.UseProtoNames {
		os.OrigName = true
	}
	if o.UseEnumNumbers {
		os.EnumsAsInts = true
	}
	if o.EmitUnpopulated {
		os.EmitDefaults = true
	}

	return os
}

func (o ProtoJSONUnmarshalOptions) convertToProtoV2UnmarshalOptions() protojson.UnmarshalOptions {
	return o.UnmarshalOptions
}

func (o ProtoJSONUnmarshalOptions) convertToProtoV1UnmarshalOptions() *jsonpb.Unmarshaler {
	os := &jsonpb.Unmarshaler{}
	if o.DiscardUnknown {
		os.AllowUnknownFields = true
	}

	return os
}

// MarshalJSON writes the given proto.Message in JSON format using
// default options (DefaultProtoJSONMarshalOptions), returns a []byte and an error.
func MarshalJSON(a any) ([]byte, error) {
	return MarshalJSONWithOptions(a, DefaultProtoJSONMarshalOptions)
}

// MarshalJSONToString writes the given proto.Message in JSON format using
// default options (DefaultProtoJSONMarshalOptions), returns a string and an error.
func MarshalJSONToString(a any) (string, error) {
	bs, err := MarshalJSONWithOptions(a, DefaultProtoJSONMarshalOptions)
	return utils.ByteSliceToString(bs), err
}

// MarshalJSONIgnoreError writes the given proto.Message in JSON format using
// default options (DefaultProtoJSONMarshalOptions), returns a []byte.
func MarshalJSONIgnoreError(a any) []byte {
	bs, _ := MarshalJSONWithOptions(a, DefaultProtoJSONMarshalOptions)
	return bs
}

// MarshalJSONToStringIgnoreError writes the given proto.Message in JSON format using
// default options (DefaultProtoJSONMarshalOptions), returns a string.
func MarshalJSONToStringIgnoreError(a any) string {
	bs, _ := MarshalJSONWithOptions(a, DefaultProtoJSONMarshalOptions)
	return utils.ByteSliceToString(bs)
}

// MarshalJSONWithOptions writes the given proto.Message in the JSON format using
// options in ProtoJSONMarshalOptions.
func MarshalJSONWithOptions(a any, o ProtoJSONMarshalOptions) ([]byte, error) {
	switch m := a.(type) {
	case dynamicpb.Message:
		return o.convertToProtoV2MarshalOptions().Marshal((&m).Interface())
	case *dynamicpb.Message:
		return o.convertToProtoV2MarshalOptions().Marshal(m.Interface())
	case protoV2.Message:
		return o.convertToProtoV2MarshalOptions().Marshal(m)
	case dynamic.Message:
		return (&m).MarshalJSONPB(o.convertToProtoV1MarshalOptions())
	case *dynamic.Message:
		return m.MarshalJSONPB(o.convertToProtoV1MarshalOptions())
	case protoV1.Message:
		var b bytes.Buffer
		if err := o.convertToProtoV1MarshalOptions().Marshal(&b, m); err != nil {
			return nil, err
		}

		return b.Bytes(), nil
	default:
		return nil, errors.Errorf(
			"%T is neither dynamicpb.Message nor protoV2.Message nor dynamic.Message nor protoV1.Message", a,
		)
	}
}

// UnmarshalJSON reads the given []byte and populates the given proto.Message using
// default options (DefaultProtoJSONUnmarshalOptions).
// The provided message must be mutable (e.g., a non-nil pointer to a message).
func UnmarshalJSON(data []byte, a any) error {
	return UnmarshalJSONWithOptions(data, a, DefaultProtoJSONUnmarshalOptions)
}

// UnmarshalJSONFromString reads the given string and populates the given proto.Message using
// default options (DefaultProtoJSONUnmarshalOptions).
func UnmarshalJSONFromString(data string, a any) error {
	return UnmarshalJSONWithOptions(utils.StringToByteSlice(data), a, DefaultProtoJSONUnmarshalOptions)
}

// UnmarshalJSONWithOptions reads the given []byte and populates the given proto.Message
// using options in ProtoJSONUnmarshalOptions.
// The provided message must be mutable (e.g., a non-nil pointer to a message).
func UnmarshalJSONWithOptions(data []byte, a any, o ProtoJSONUnmarshalOptions) error {
	switch m := a.(type) {
	case dynamicpb.Message:
		return o.convertToProtoV2UnmarshalOptions().Unmarshal(data, (&m).Interface())
	case *dynamicpb.Message:
		return o.convertToProtoV2UnmarshalOptions().Unmarshal(data, m.Interface())
	case protoV2.Message:
		return o.convertToProtoV2UnmarshalOptions().Unmarshal(data, m)
	case dynamic.Message:
		return (&m).UnmarshalJSONPB(o.convertToProtoV1UnmarshalOptions(), data)
	case *dynamic.Message:
		return m.UnmarshalJSONPB(o.convertToProtoV1UnmarshalOptions(), data)
	case protoV1.Message:
		return o.convertToProtoV1UnmarshalOptions().Unmarshal(bytes.NewReader(data), m)
	default:
		return errors.Errorf(
			"%T is neither a dynamicpb.Message nor protoV2.Message nor dynamic.Message nor protoV1.Message", a,
		)
	}
}

// MarshalJSONWithMessagesToString writes the given []proto.Message in the JSON format
// using default options (DefaultProtoJSONMarshalOptions).
func MarshalJSONWithMessagesToString(ms any) (string, error) {
	return MarshalJSONWithMessagesToStringWithOptions(ms, DefaultProtoJSONMarshalOptions)
}

// MarshalJSONWithMessagesToStringIgnoreError writes the given []proto.Message in the JSON
// format using default options (DefaultProtoJSONMarshalOptions).
func MarshalJSONWithMessagesToStringIgnoreError(ms any) string {
	s, _ := MarshalJSONWithMessagesToStringWithOptions(ms, DefaultProtoJSONMarshalOptions)
	return s
}

// MarshalJSONWithMessagesToStringWithOptions writes the given []proto.Message in the JSON
// format using options in ProtoJSONMarshalOptions.
func MarshalJSONWithMessagesToStringWithOptions(ms any, o ProtoJSONMarshalOptions) (string, error) {
	rv := reflect.ValueOf(ms)
	for rv.Kind() == reflect.Pointer {
		rv = rv.Elem()
	}

	if !rv.IsValid() || rv.IsNil() {
		if o.NilSliceToEmptySlice {
			return constEmptySlice, nil
		}

		return constNullSlice, nil
	} else if rv.Kind() != reflect.Slice && rv.Kind() != reflect.Array {
		return "", errUnsupportedType
	}

	sb := strings.Builder{}

	for i := 0; i < rv.Len(); i++ {
		m, ok := rv.Index(i).Interface().(proto.Message)
		if !ok {
			return "", errUnsupportedType
		}

		bs, err := MarshalJSONWithOptions(m, o)
		if err != nil {
			return "", err
		}

		if i != 0 {
			sb.WriteString(",")
		}
		sb.Write(bs)
	}

	return constLeftBracket + sb.String() + constRightBracket, nil
}

// UnmarshalJSONWithMessagesFromString reads the given string and populates the given
// []proto.Message using default options (DefaultProtoJSONUnmarshalOptions).
func UnmarshalJSONWithMessagesFromString(str string, ms any) error {
	return UnmarshalJSONWithMessagesFromStringWithOptions(str, ms, DefaultProtoJSONUnmarshalOptions)
}

// UnmarshalJSONWithMessagesFromStringWithOptions reads the given string and populates
// the given []proto.Message using options in ProtoJSONUnmarshalOptions.
func UnmarshalJSONWithMessagesFromStringWithOptions(str string, ms any, o ProtoJSONUnmarshalOptions) error {
	rv := reflect.ValueOf(ms)
	if !rv.IsValid() || rv.Kind() != reflect.Pointer || rv.IsNil() {
		return errors.Errorf("not a valid pointer: %v", ms)
	}

	rt := reflect.TypeOf(ms)
	rte := rt.Elem()
	rve := rv.Elem()

	// 检查是否切片
	if rte.Kind() != reflect.Slice {
		return errUnsupportedType
	}

	// 检查是否`proto.Message`
	e := rte.Elem()
	if !e.Implements(reflect.TypeOf(protoMessage).Elem()) {
		return errUnsupportedType
	}
	if e.Kind() == reflect.Pointer {
		e = e.Elem()
	}

	// 检查是否可以设置
	if !rve.CanSet() {
		return errNotSettable
	}

	v := &structpb.Value{}
	if err := UnmarshalJSONFromString(str, v); err != nil {
		return err
	}
	lv := v.GetListValue()

	ptr := rte.Elem().Kind() == reflect.Pointer
	values := lv.GetValues()
	rve.Set(reflect.MakeSlice(rte, 0, len(values)))
	for _, value := range values {
		bs, err := value.MarshalJSON()
		if err != nil {
			return err
		}

		val := reflect.New(e)
		if err = UnmarshalJSONWithOptions(bs, val.Interface().(proto.Message), o); err != nil {
			return err
		}

		if ptr {
			rve.Set(reflect.Append(rve, val))
		} else {
			rve.Set(reflect.Append(rve, reflect.Indirect(val)))
		}
	}

	return nil
}
