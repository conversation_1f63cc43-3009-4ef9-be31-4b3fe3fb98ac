package protobuf

import (
	"encoding/hex"
	"io/fs"
	"math"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"sync"

	"github.com/jhump/protoreflect/desc"
	"github.com/jhump/protoreflect/desc/protoparse"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/syncx"
	"github.com/zyedidia/generic/set"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/reflect/protoregistry"
	"google.golang.org/protobuf/types/descriptorpb"
	"google.golang.org/protobuf/types/dynamicpb"
)

const (
	colon              = ":"
	protobufFileSuffix = ".proto"
	defaultProductName = "default"
	defaultBranch      = "main"

	ServiceOptionsDeprecatedFieldFullName protoreflect.FullName = "google.protobuf.ServiceOptions.deprecated"
	MethodOptionsDeprecatedFieldFullName  protoreflect.FullName = "google.protobuf.MethodOptions.deprecated"
	MessageOptionsDeprecatedFieldFullName protoreflect.FullName = "google.protobuf.MessageOptions.deprecated"
)

type findDescriptorType int

const (
	_any findDescriptorType = iota
	_file
	_service
	_method
	_message
	_enum
	_extension
)

var (
	defaultInitHandler InitProtoEventHandler = noopHandler{}

	standardImports map[string]protoreflect.FileDescriptor

	standardFilenames = []string{
		"google/protobuf/any.proto",
		"google/protobuf/api.proto",
		"google/protobuf/compiler/plugin.proto",
		"google/protobuf/descriptor.proto",
		"google/protobuf/duration.proto",
		"google/protobuf/empty.proto",
		"google/protobuf/field_mask.proto",
		"google/protobuf/source_context.proto",
		"google/protobuf/struct.proto",
		"google/protobuf/timestamp.proto",
		"google/protobuf/type.proto",
		"google/protobuf/wrappers.proto",
	}

	allFindDescriptorTypes = []findDescriptorType{
		_service,
		_method,
		_message,
		_enum,
		_extension,
	}

	errZeroProducts = errors.New("products cannot be empty")
	errZeroProjects = errors.New("projects cannot be empty")
)

func init() {
	standardImports = map[string]protoreflect.FileDescriptor{}
	for _, fn := range standardFilenames {
		fd, err := protoregistry.GlobalFiles.FindFileByPath(fn)
		if err != nil {
			panic(err.Error())
		}
		standardImports[fn] = fd
	}
}

type (
	Option     func(pm *ProtoManager)
	FindOption func(fo *findOptions)

	InitProtoEventHandler interface {
		OnVisitDirectory(string, fs.FileInfo) bool
		OnVisitFile(string, fs.FileInfo) bool
		OnInitFileDescriptor(protoreflect.FileDescriptor) bool
		OnInitServiceDescriptor(protoreflect.ServiceDescriptor) bool
		OnInitMethodDescriptor(protoreflect.MethodDescriptor) bool
		OnInitMessageDescriptor(*protoregistry.Types, protoreflect.MessageDescriptor) bool
		OnInitEnumDescriptor(*protoregistry.Types, protoreflect.EnumDescriptor) bool
		OnInitExtensionDescriptor(*protoregistry.Types, protoreflect.ExtensionDescriptor) bool
	}
)

type (
	ProtoManager struct {
		products []protoProduct

		currentProductBranch string
		projectData          map[string]map[string]*ProtoData // key1: project, key2: branch, value2: *ProtoData
		branchData           map[string]map[string]*ProtoData // key1: branch, key2: project, value2: *ProtoData

		singleFlight syncx.SingleFlight
		lock         sync.RWMutex
	}

	Product struct {
		Name     string    // 名称
		Branch   string    // 分支
		Projects []Project // 项目
	}
	Project struct {
		Name         string                // 名称
		Branch       string                // 分支
		Path         string                // 本地路径
		ImportPaths  []string              // 导入路径
		ExcludePaths []string              // 排除路径
		ExcludeFiles []string              // 排除文件
		EventHandler InitProtoEventHandler // 事件处理器
	}
	protoProduct struct {
		name     string
		branch   string
		projects []protoProject
	}
	protoProject struct {
		name         string
		branch       string
		path         string
		importPaths  set.Set[string]
		excludePaths set.Set[string]
		excludeFiles set.Set[string]
		initHandler  InitProtoEventHandler
	}

	ProtoData struct {
		name   string
		branch string

		sourceFiles              set.Set[string]
		fileDescriptors          map[string]protoreflect.FileDescriptor
		serviceDescriptors       map[string]protoreflect.ServiceDescriptor
		methodDescriptors        map[string]protoreflect.MethodDescriptor
		messageDescriptors       map[string]protoreflect.MessageDescriptor
		enumDescriptors          map[string]protoreflect.EnumDescriptor
		extensionTypeDescriptors map[string]protoreflect.ExtensionTypeDescriptor
		protoregistryTypes       *protoregistry.Types
		messagePool              Pool
	}

	findOptions struct {
		findType      findDescriptorType
		productBranch string
		projectName   string
		projectBranch string
	}

	productData struct {
		productBranch string
		projectName   string
		projectBranch string
		data          *ProtoData
	}
	descriptorData struct {
		data       *ProtoData
		descriptor any
	}
)

func WithProducts(products ...Product) Option {
	return func(pm *ProtoManager) {
		cache := make(map[string]lang.PlaceholderType, len(pm.products)+len(products))
		for _, product := range pm.products {
			cache[product.Key()] = lang.Placeholder
		}

		for _, product := range products {
			if len(product.Projects) == 0 {
				continue
			}

			if product.Name == "" {
				product.Name = defaultProductName
			}
			if product.Branch == "" {
				product.Branch = defaultBranch
			}
			if pm.currentProductBranch == "" {
				pm.currentProductBranch = product.Branch
			}

			key := product.Key()
			if _, ok := cache[key]; ok {
				continue
			}

			projects := make([]protoProject, 0, len(product.Projects))
			for _, project := range product.Projects {
				if project.Path == "" {
					continue
				}

				if project.Branch == "" {
					project.Branch = product.Branch
				}
				if project.EventHandler == nil {
					project.EventHandler = defaultInitHandler
				}

				projects = append(projects, project.convert())
			}

			cache[key] = lang.Placeholder
			pm.products = append(
				pm.products, protoProduct{
					name:     product.Name,
					branch:   product.Branch,
					projects: projects,
				},
			)
		}
	}
}

// NewProtoManager 创建`Proto`管理器
func NewProtoManager(options ...Option) (*ProtoManager, error) {
	pm := &ProtoManager{
		singleFlight: syncx.NewSingleFlight(),
	}

	for _, option := range options {
		option(pm)
	}

	if err := pm.validate(); err != nil {
		return nil, err
	}

	pm.projectData = make(map[string]map[string]*ProtoData, 64)
	pm.branchData = make(map[string]map[string]*ProtoData, 64)
	for _, product := range pm.products {
		projectCache, ok := pm.branchData[product.branch]
		if !ok {
			projectCache = make(map[string]*ProtoData, len(product.projects))
			pm.branchData[product.branch] = projectCache
		}

		for _, project := range product.projects {
			branchCache, ok := pm.projectData[project.name]
			if !ok {
				branchCache = make(map[string]*ProtoData, len(pm.products))
				pm.projectData[project.name] = branchCache
			}

			_, ok1 := projectCache[project.name]
			_, ok2 := branchCache[project.branch]
			if ok1 && ok2 {
				continue
			}

			data, err := project.load(nil)
			if err != nil {
				return nil, err
			}

			if !ok1 {
				projectCache[project.name] = data
			}
			if !ok2 {
				branchCache[project.branch] = data
			}
		}
	}

	return pm, nil
}

func withFindDescriptorType(tp findDescriptorType) FindOption {
	return func(fo *findOptions) {
		fo.findType = tp
	}
}

func WithProductBranch(branch string) FindOption {
	return func(fo *findOptions) {
		if branch == "" {
			fo.productBranch = defaultBranch
		} else {
			fo.productBranch = branch
		}
	}
}

func WithProjectNameAndBranch(name, branch string) FindOption {
	return func(fo *findOptions) {
		fo.projectName = name
		fo.projectBranch = branch
	}
}

func (p Product) Key() string {
	return p.Name + colon + p.Branch
}

func (p Project) convert() protoProject {
	return protoProject{
		name:         p.Name,
		branch:       p.Branch,
		path:         p.Path,
		importPaths:  set.NewMapset[string](p.ImportPaths...),
		excludePaths: set.NewMapset[string](p.ExcludePaths...),
		excludeFiles: set.NewMapset[string](p.ExcludeFiles...),
		initHandler:  p.EventHandler,
	}
}

func (p protoProduct) Key() string {
	return p.name + colon + p.branch
}

func (p protoProject) load(data *ProtoData) (*ProtoData, error) {
	if data != nil {
		data.name = p.name
		data.branch = p.branch

		data.sourceFiles.Clear()
		clearMap(data.fileDescriptors)
		clearMap(data.serviceDescriptors)
		clearMap(data.methodDescriptors)
		clearMap(data.messageDescriptors)
		clearMap(data.enumDescriptors)
		clearMap(data.extensionTypeDescriptors)
		data.protoregistryTypes = new(protoregistry.Types)
		data.messagePool.Clear()
	} else {
		data = &ProtoData{
			name:   p.name,
			branch: p.branch,

			fileDescriptors:          make(map[string]protoreflect.FileDescriptor),
			serviceDescriptors:       make(map[string]protoreflect.ServiceDescriptor),
			methodDescriptors:        make(map[string]protoreflect.MethodDescriptor),
			messageDescriptors:       make(map[string]protoreflect.MessageDescriptor),
			enumDescriptors:          make(map[string]protoreflect.EnumDescriptor),
			extensionTypeDescriptors: make(map[string]protoreflect.ExtensionTypeDescriptor),
			protoregistryTypes:       new(protoregistry.Types),
			messagePool:              NewPool(),
		}
	}

	paths := make([]string, 0, p.importPaths.Size()+1)
	paths = append(paths, p.importPaths.Keys()...)
	paths = append(paths, p.path)

	var err error
	data.sourceFiles, err = p.walkDir(paths)
	if err != nil {
		return nil, err
	}

	parser := &protoparse.Parser{
		ImportPaths: paths,
	}

	// `.proto`文件 => []*desc.FileDescriptor (github.com/jhump/protoreflect/desc)
	fileDescriptors, err := parser.ParseFiles(data.sourceFiles.Keys()...)
	if err != nil {
		return nil, err
	}

	// 初始化`protoregistryTypes`（包括：`enum`和`extension`）
	needToRebuild, err := data.initRegistryTypes(fileDescriptors)
	if err != nil {
		return nil, err
	}

	if needToRebuild {
		// 使用包含指定的`extension`的`protoregistry`来重建`FileDescriptor`
		fileDescriptors, err = data.rebuildFileDescriptors(fileDescriptors)
		if err != nil {
			return nil, err
		}
	}

	data.initWithFileDescriptors(p, fileDescriptors)
	return data, nil
}

func (p protoProject) walkDir(paths []string) (files set.Set[string], err error) {
	files = set.NewMapset[string]()

	for _, path_ := range paths {
		if err = p.walkWithRoot(path_, files); err != nil {
			return files, err
		}
	}

	return files, nil
}

func (p protoProject) walkWithRoot(root string, cache set.Set[string]) error {
	return filepath.Walk(
		root, func(path string, info fs.FileInfo, err error) error {
			if err != nil {
				return err
			} else if info.IsDir() {
				if strings.HasPrefix(info.Name(), ".") {
					// 忽略目录 `.` 和 `..`
					return filepath.SkipDir
				}

				if p.excludePaths.Has(info.Name()) || p.excludePaths.Has(path) {
					// 忽略排除的目录
					return filepath.SkipDir
				}

				// 处理目录
				if p.initHandler != nil && !p.initHandler.OnVisitDirectory(path, info) {
					return filepath.SkipDir
				}
			} else if !info.IsDir() && strings.HasSuffix(info.Name(), protobufFileSuffix) {
				if p.excludeFiles.Has(info.Name()) {
					// 忽略排除的文件
					return filepath.SkipDir
				}

				// 处理文件
				if p.initHandler != nil && !p.initHandler.OnVisitFile(path, info) {
					return filepath.SkipDir
				}

				rel, err := filepath.Rel(root, path)
				if err != nil {
					return filepath.SkipDir
				}

				cache.Put(rel)
			}

			return nil
		},
	)
}

func (d *ProtoData) initRegistryTypes(fileDescriptors []*desc.FileDescriptor) (bool, error) {
	var (
		rebuild bool
		once    sync.Once
	)
	setToRebuildFn := func() {
		once.Do(
			func() {
				rebuild = true
			},
		)
	}

	for _, fileDescriptor := range fileDescriptors {
		for _, enum := range fileDescriptor.GetEnumTypes() {
			// 把`enum`注册到`protoregistry`中
			enumType := dynamicpb.NewEnumType(enum.UnwrapEnum())
			if err := d.protoregistryTypes.RegisterEnum(enumType); err != nil {
				return rebuild, err
			}

			setToRebuildFn()
		}

		for _, extension := range fileDescriptor.GetExtensions() {
			// 把`extension`注册到`protoregistry`中
			extensionType := dynamicpb.NewExtensionType(extension.UnwrapField())
			if err := d.protoregistryTypes.RegisterExtension(extensionType); err != nil {
				return rebuild, err
			}

			setToRebuildFn()
		}
	}

	return rebuild, nil
}

func (d *ProtoData) rebuildFileDescriptors(in []*desc.FileDescriptor) ([]*desc.FileDescriptor, error) {
	// *desc.FileDescriptor (github.com/jhump/protoreflect/desc) => *descriptorpb.FileDescriptorProto (google.golang.org/protobuf/types/descriptorpb)
	fileDescriptorProtos := make([]*descriptorpb.FileDescriptorProto, 0, len(standardImports)+len(in))
	for _, fileDescriptor := range in {
		bs, err := proto.MarshalOptions{AllowPartial: true}.Marshal(fileDescriptor.AsFileDescriptorProto())
		if err != nil {
			return nil, err
		}

		var fileDescriptorProto descriptorpb.FileDescriptorProto
		err = proto.UnmarshalOptions{
			AllowPartial:   true,
			DiscardUnknown: true,
			Resolver:       d.protoregistryTypes, // 使用包含`extension`的`protoregistry`
		}.Unmarshal(bs, &fileDescriptorProto)
		if err != nil {
			return nil, err
		}

		fileDescriptorProtos = append(fileDescriptorProtos, &fileDescriptorProto)
	}

	for _, standardImport := range standardImports {
		fd, err := desc.WrapFile(standardImport)
		if err != nil {
			continue
		}

		fileDescriptorProtos = append(fileDescriptorProtos, fd.AsFileDescriptorProto())
	}

	// []*descriptorpb.FileDescriptorProto (google.golang.org/protobuf/types/descriptorpb) => map[string]*desc.FileDescriptor (github.com/jhump/protoreflect/desc)
	fileDescriptorMap, err := desc.CreateFileDescriptors(fileDescriptorProtos)
	if err != nil {
		return nil, err
	}

	out := make([]*desc.FileDescriptor, 0, len(fileDescriptorMap))
	for _, fileDescriptor := range fileDescriptorMap {
		out = append(out, fileDescriptor)
	}

	return out, nil
}

func (d *ProtoData) initWithFileDescriptors(project protoProject, files []*desc.FileDescriptor) {
	for _, file := range files {
		// 处理`FileDescriptor`
		fileDescriptor := file.UnwrapFile()
		if project.initHandler != nil && !project.initHandler.OnInitFileDescriptor(fileDescriptor) {
			continue
		}
		d.fileDescriptors[file.GetFullyQualifiedName()] = fileDescriptor

		for _, service := range file.GetServices() {
			// 处理`ServiceDescriptor`
			serviceDescriptor := service.UnwrapService()
			if project.initHandler != nil && !project.initHandler.OnInitServiceDescriptor(serviceDescriptor) {
				continue
			}
			d.serviceDescriptors[service.GetFullyQualifiedName()] = serviceDescriptor

			for _, method := range service.GetMethods() {
				// 处理`MethodDescriptor`
				methodDescriptor := method.UnwrapMethod()
				if project.initHandler != nil && !project.initHandler.OnInitMethodDescriptor(methodDescriptor) {
					continue
				}

				d.methodDescriptors[method.GetFullyQualifiedName()] = methodDescriptor
			}
		}

		for _, message := range file.GetMessageTypes() {
			// 处理`MessageDescriptor`
			messageDescriptor := message.UnwrapMessage()
			if project.initHandler != nil && !project.initHandler.OnInitMessageDescriptor(
				d.protoregistryTypes, messageDescriptor,
			) {
				continue
			}

			d.messageDescriptors[message.GetFullyQualifiedName()] = messageDescriptor
		}

		for _, enum := range file.GetEnumTypes() {
			// 处理`EnumDescriptor`
			enumDescriptor := enum.UnwrapEnum()
			if project.initHandler != nil && !project.initHandler.OnInitEnumDescriptor(
				d.protoregistryTypes, enumDescriptor,
			) {
				continue
			}

			d.enumDescriptors[enum.GetFullyQualifiedName()] = enumDescriptor
		}

		for _, extension := range file.GetExtensions() {
			if !extension.IsExtension() {
				continue
			}

			// 处理`FieldDescriptor`
			extensionDescriptor := extension.UnwrapField()
			if project.initHandler != nil && !project.initHandler.OnInitExtensionDescriptor(
				d.protoregistryTypes, extensionDescriptor,
			) {
				continue
			}

			d.extensionTypeDescriptors[extension.GetFullyQualifiedName()] = dynamicpb.NewExtensionType(extensionDescriptor).TypeDescriptor()
		}
	}
}

func (pm *ProtoManager) validate() error {
	if len(pm.products) == 0 {
		return errZeroProducts
	}

	var productName string
	for _, product := range pm.products {
		if productName != "" && productName != product.name {
			return errors.Errorf("multiple products[%s, %s] found", productName, product.name)
		}

		productName = product.name
	}

	return nil
}

func (pm *ProtoManager) handleFindOptions(options ...FindOption) *findOptions {
	fo := &findOptions{
		findType: _any,
	}
	for _, option := range options {
		option(fo)
	}
	if fo.productBranch == "" {
		fo.productBranch = pm.currentProductBranch
	}

	return fo
}

func (pm *ProtoManager) findDescriptor(name string, options ...FindOption) (*descriptorData, error) {
	var key string
	fo := pm.handleFindOptions(options...)
	if fo.projectName != "" && fo.projectBranch != "" {
		key = fo.projectName + colon + fo.projectBranch + colon + name
	} else {
		key = fo.productBranch + colon + name
	}

	v, err := pm.singleFlight.Do(
		key, func() (any, error) {
			if fo.projectName != "" && fo.projectBranch != "" {
				return pm.findDescriptorByProjectNameAndBranch(fo.findType, fo.projectName, fo.projectBranch, name)
			}

			return pm.findDescriptorByProductBranch(fo.findType, fo.productBranch, name)
		},
	)
	if err != nil {
		return nil, err
	}

	return v.(*descriptorData), nil
}

func (pm *ProtoManager) findDescriptorByProductBranch(
	tp findDescriptorType, productBranch, name string,
) (*descriptorData, error) {
	pm.lock.RLock()
	defer pm.lock.RUnlock()

	m, ok := pm.branchData[productBranch]
	if !ok {
		return nil, errors.Errorf("unknown branch of product, branch: %s", productBranch)
	}

	for _, data := range m {
		if d := pm.findDescriptorByType(tp, data, name); d != nil {
			return &descriptorData{
				data:       data,
				descriptor: d,
			}, nil
		}
	}

	return nil, errors.Errorf(
		"descriptor not found by product branch, branch: %s, descriptor: %s",
		productBranch, name,
	)
}

func (pm *ProtoManager) findDescriptorByProjectNameAndBranch(
	tp findDescriptorType, projectName, projectBranch, name string,
) (*descriptorData, error) {
	pm.lock.RLock()
	defer pm.lock.RUnlock()

	m, ok := pm.projectData[projectName]
	if !ok {
		return nil, errors.Errorf("unknown name of project, name: %s", projectName)
	}
	data, ok := m[projectBranch]
	if !ok {
		return nil, errors.Errorf("unknown branch of project, branch: %s", projectBranch)
	}

	if d := pm.findDescriptorByType(tp, data, name); d != nil {
		return &descriptorData{
			data:       data,
			descriptor: d,
		}, nil
	}

	return nil, errors.Errorf(
		"descriptor not found by project name and branch, name: %s, branch: %s, descriptor: %s",
		projectName, projectBranch, name,
	)
}

func (pm *ProtoManager) findDescriptorByType(tp findDescriptorType, data *ProtoData, name string) any {
	var tps []findDescriptorType
	if tp == _any {
		tps = allFindDescriptorTypes
	} else {
		tps = []findDescriptorType{tp}
	}

	for _, fdt := range tps {
		var (
			v  any
			ok bool
		)
		switch fdt {
		case _service:
			v, ok = data.serviceDescriptors[name]
		case _method:
			v, ok = data.methodDescriptors[name]
		case _message:
			v, ok = data.messageDescriptors[name]
		case _enum:
			v, ok = data.enumDescriptors[name]
		case _extension:
			v, ok = data.extensionTypeDescriptors[name]
		default:
			continue
		}
		if ok {
			return v
		}
	}

	return nil
}

// FindServiceDescriptorByName 通过完整的服务名称获取服务描述
func (pm *ProtoManager) FindServiceDescriptorByName(name string, options ...FindOption) (
	protoreflect.ServiceDescriptor, error,
) {
	options = append(options, withFindDescriptorType(_service))
	d, err := pm.findDescriptor(name, options...)
	if err != nil {
		return nil, err
	}

	v, ok := d.descriptor.(protoreflect.ServiceDescriptor)
	if !ok {
		return nil, errors.Errorf("the descriptor[%s]'s type[%T] is not a service descriptor", name, d.descriptor)
	}

	return v, nil
}

// FindMethodDescriptorByName 通过完整的方法名称获取方法描述
func (pm *ProtoManager) FindMethodDescriptorByName(name string, options ...FindOption) (
	protoreflect.MethodDescriptor, error,
) {
	options = append(options, withFindDescriptorType(_method))
	d, err := pm.findDescriptor(name, options...)
	if err != nil {
		return nil, err
	}

	v, ok := d.descriptor.(protoreflect.MethodDescriptor)
	if !ok {
		return nil, errors.Errorf("the descriptor[%s]'s type[%T] is not a method descriptor", name, d.descriptor)
	}

	return v, nil
}

// FindMessageDescriptorByName 通过完整的消息名称获取消息描述
func (pm *ProtoManager) FindMessageDescriptorByName(name string, options ...FindOption) (
	protoreflect.MessageDescriptor, error,
) {
	options = append(options, withFindDescriptorType(_message))
	d, err := pm.findDescriptor(name, options...)
	if err != nil {
		return nil, err
	}

	v, ok := d.descriptor.(protoreflect.MessageDescriptor)
	if !ok {
		return nil, errors.Errorf("the descriptor[%s]'s type[%T] is not a message descriptor", name, d.descriptor)
	}

	return v, nil
}

// FindEnumDescriptorByName 通过完整的枚举名称获取枚举描述
func (pm *ProtoManager) FindEnumDescriptorByName(name string, options ...FindOption) (
	protoreflect.EnumDescriptor, error,
) {
	options = append(options, withFindDescriptorType(_enum))
	d, err := pm.findDescriptor(name, options...)
	if err != nil {
		return nil, err
	}

	v, ok := d.descriptor.(protoreflect.EnumDescriptor)
	if !ok {
		return nil, errors.Errorf("the descriptor[%s]'s type[%T] is not an enum descriptor", name, d.descriptor)
	}

	return v, nil
}

// FindExtensionDescriptorByName 通过完整的扩展名称获取扩展描述
func (pm *ProtoManager) FindExtensionDescriptorByName(
	name string, options ...FindOption,
) (protoreflect.ExtensionDescriptor, error) {
	options = append(options, withFindDescriptorType(_extension))
	d, err := pm.findDescriptor(name, options...)
	if err != nil {
		return nil, err
	}

	if v, ok := d.descriptor.(protoreflect.ExtensionType); ok {
		return v.TypeDescriptor().Descriptor(), nil
	} else if v, ok := d.descriptor.(protoreflect.ExtensionTypeDescriptor); ok {
		return v.Descriptor(), nil
	} else if v, ok := d.descriptor.(protoreflect.ExtensionDescriptor); ok {
		return v, nil
	} else {
		return nil, errors.Errorf("the descriptor[%s]'s type[%T] is not an extension descriptor", name, d.descriptor)
	}
}

// FindInputMessageDescriptorByMethodName 通过完整的方法名称获取请求消息描述
func (pm *ProtoManager) FindInputMessageDescriptorByMethodName(
	name string, options ...FindOption,
) (protoreflect.MessageDescriptor, error) {
	method, err := pm.FindMethodDescriptorByName(name, options...)
	if err != nil {
		return nil, err
	}

	return method.Input(), nil
}

// FindOutputMessageDescriptorByMethodName 通过完整的方法名称获取响应消息描述
func (pm *ProtoManager) FindOutputMessageDescriptorByMethodName(
	name string, options ...FindOption,
) (protoreflect.MessageDescriptor, error) {
	method, err := pm.FindMethodDescriptorByName(name, options...)
	if err != nil {
		return nil, err
	}

	return method.Output(), err
}

func (pm *ProtoManager) rangeDescriptors(fn func(data *ProtoData) bool, options ...FindOption) error {
	fo := pm.handleFindOptions(options...)
	if fo.projectName != "" && fo.projectBranch != "" {
		return pm.rangeDescriptorsByProjectNameAndBranch(fo.projectName, fo.projectBranch, fn)
	}

	return pm.rangeDescriptorsByProductBranch(fo.productBranch, fn)
}

func (pm *ProtoManager) rangeDescriptorsByProductBranch(
	productBranch string, fn func(data *ProtoData) bool,
) error {
	pm.lock.RLock()
	defer pm.lock.RUnlock()

	m, ok := pm.branchData[productBranch]
	if !ok {
		return errors.Errorf("unknown branch of product, branch: %s", productBranch)
	}

	for _, data := range m {
		if !fn(data) {
			break
		}
	}

	return nil
}

func (pm *ProtoManager) rangeDescriptorsByProjectNameAndBranch(
	projectName, projectBranch string, fn func(data *ProtoData) bool,
) error {
	pm.lock.RLock()
	defer pm.lock.RUnlock()

	m, ok := pm.projectData[projectName]
	if !ok {
		return errors.Errorf("unknown name of project, name: %s", projectName)
	}
	data, ok := m[projectBranch]
	if !ok {
		return errors.Errorf("unknown branch of project, branch: %s", projectBranch)
	}

	fn(data)
	return nil
}

// RangeFileDescriptors 遍历`FileDescriptor`
func (pm *ProtoManager) RangeFileDescriptors(fn func(protoreflect.FileDescriptor) bool, options ...FindOption) error {
	return pm.rangeDescriptors(
		func(data *ProtoData) bool {
			for _, v := range data.fileDescriptors {
				if !fn(v) {
					return false
				}
			}

			return true
		}, options...,
	)
}

// RangeServiceDescriptors 遍历`ServiceDescriptor`
func (pm *ProtoManager) RangeServiceDescriptors(
	fn func(protoreflect.ServiceDescriptor) bool, options ...FindOption,
) error {
	return pm.rangeDescriptors(
		func(data *ProtoData) bool {
			for _, v := range data.serviceDescriptors {
				if !fn(v) {
					return false
				}
			}

			return true
		}, options...,
	)
}

// RangeMethodDescriptors 遍历`MethodDescriptor`
func (pm *ProtoManager) RangeMethodDescriptors(
	fn func(protoreflect.MethodDescriptor) bool, options ...FindOption,
) error {
	return pm.rangeDescriptors(
		func(data *ProtoData) bool {
			for _, v := range data.methodDescriptors {
				if !fn(v) {
					return false
				}
			}

			return true
		}, options...,
	)
}

// RangeMessageDescriptors 遍历`MessageDescriptor`
func (pm *ProtoManager) RangeMessageDescriptors(
	fn func(protoreflect.MessageDescriptor) bool, options ...FindOption,
) error {
	return pm.rangeDescriptors(
		func(data *ProtoData) bool {
			for _, v := range data.messageDescriptors {
				if !fn(v) {
					return false
				}
			}

			return true
		}, options...,
	)
}

// RangeServiceOptions 遍历`ServiceOptions`
func (pm *ProtoManager) RangeServiceOptions(
	name string, fn func(protoreflect.FieldDescriptor, protoreflect.Value) bool, options ...FindOption,
) error {
	sd, err := pm.FindServiceDescriptorByName(name, options...)
	if err != nil {
		return err
	}

	sd.Options().ProtoReflect().Range(fn)
	return nil
}

// RangeMethodOptions 遍历`MethodOptions`
func (pm *ProtoManager) RangeMethodOptions(
	name string, fn func(protoreflect.FieldDescriptor, protoreflect.Value) bool, options ...FindOption,
) error {
	md, err := pm.FindMethodDescriptorByName(name, options...)
	if err != nil {
		return err
	}

	md.Options().ProtoReflect().Range(fn)
	return nil
}

// SwitchTo 切换当前分支到指定分支
func (pm *ProtoManager) SwitchTo(productBranch string) error {
	_, err := pm.singleFlight.Do(
		productBranch, func() (any, error) {
			pm.lock.RLock()
			_, ok := pm.branchData[productBranch]
			pm.lock.RUnlock()
			if !ok {
				return nil, errors.Errorf("unknown branch of product, branch: %s", productBranch)
			}

			pm.lock.Lock()
			defer pm.lock.Unlock()

			if pm.currentProductBranch != productBranch {
				pm.currentProductBranch = productBranch
			}

			return nil, nil
		},
	)

	return err
}

// ReloadByBranch 重新加载指定分支的`proto`项目
func (pm *ProtoManager) ReloadByBranch(productBranch string) error {
	if productBranch == "" {
		productBranch = pm.currentProductBranch
	}

	_, err := pm.singleFlight.Do(
		productBranch, func() (any, error) {
			items := make([]*productData, 0, len(pm.projectData))
			for _, p := range pm.products {
				if p.branch != productBranch {
					continue
				}

				for _, project := range p.projects {
					data, err := project.load(nil)
					if err != nil {
						return nil, err
					}

					items = append(
						items, &productData{
							productBranch: productBranch,
							projectName:   project.name,
							projectBranch: project.branch,
							data:          data,
						},
					)
				}
			}

			pm.lock.Lock()
			defer pm.lock.Unlock()

			// 加载成功后，再替换
			m1, ok1 := pm.branchData[productBranch]
			if !ok1 {
				return nil, errors.Errorf("unknown branch of product, branch: %s", productBranch)
			}
			for _, item := range items {
				m1[item.projectName] = item.data

				if m2, ok2 := pm.projectData[item.projectName]; ok2 {
					m2[item.projectBranch] = item.data
				}
			}

			return nil, nil
		},
	)

	return err
}

// ReloadByProduct 重新加载指定的产品
func (pm *ProtoManager) ReloadByProduct(product Product) error {
	if len(product.Projects) == 0 {
		return errZeroProjects
	}
	if product.Name == "" {
		product.Name = defaultProductName
	}
	if product.Branch == "" {
		product.Branch = defaultBranch
	}
	if product.Name != pm.products[0].name {
		return errors.Errorf("different product name, current: %s, target: %s", pm.products[0].name, product.Name)
	}

	_, err := pm.singleFlight.Do(
		product.Branch, func() (any, error) {
			projects := make([]protoProject, 0, len(product.Projects))
			items := make([]*productData, 0, len(product.Projects))
			for _, project := range product.Projects {
				p := project.convert()
				data, err := p.load(nil)
				if err != nil {
					return nil, err
				}

				projects = append(projects, p)
				items = append(
					items, &productData{
						productBranch: product.Branch,
						projectName:   p.name,
						projectBranch: p.branch,
						data:          data,
					},
				)
			}

			pm.lock.Lock()
			defer pm.lock.Unlock()

			// 加载成功后，再替换
			m1, ok1 := pm.branchData[product.Branch]
			if !ok1 {
				m1 = make(map[string]*ProtoData, len(product.Projects))
				pm.branchData[product.Branch] = m1
			}
			for _, item := range items {
				m1[item.projectName] = item.data

				if m2, ok2 := pm.projectData[item.projectName]; ok2 {
					m2[item.projectBranch] = item.data
				}
			}

			for i, p := range pm.products {
				// 如果指定分支已存在则更新
				if p.branch == product.Branch {
					pm.products[i] = protoProduct{
						name:     product.Name,
						branch:   product.Branch,
						projects: projects,
					}
					return nil, nil
				}
			}

			// 如果指定分支不存在则添加
			pm.products = append(
				pm.products, protoProduct{
					name:     product.Name,
					branch:   product.Branch,
					projects: projects,
				},
			)
			return nil, nil
		},
	)

	return err
}

// CreateMessage 通过完整的消息名称在消息池子中获取或者创建动态消息
func (pm *ProtoManager) CreateMessage(name string, options ...FindOption) (*dynamicpb.Message, error) {
	// 通过完整的消息名称获取消息描述
	options = append(options, withFindDescriptorType(_message))
	d, err := pm.findDescriptor(name, options...)
	if err != nil {
		return nil, err
	}

	v, ok := d.descriptor.(protoreflect.MessageDescriptor)
	if !ok {
		return nil, errors.Errorf("the descriptor[%s]'s type[%T] is not a message descriptor", name, d.descriptor)
	}

	// 通过消息描述获取或者创建动态消息
	return d.data.messagePool.Get(v), nil
}

// PutMessage 把动态消息放回消息池子中
func (pm *ProtoManager) PutMessage(message *dynamicpb.Message, options ...FindOption) {
	if message == nil {
		return
	}

	// 通过完整的消息名称获取消息描述
	options = append(options, withFindDescriptorType(_message))
	d, err := pm.findDescriptor(string(message.Descriptor().FullName()), options...)
	if err != nil {
		return
	}

	pm.lock.RLock()
	defer pm.lock.RUnlock()

	d.data.messagePool.Put(message)
}

// MarshalPB 字节切片反序列化为ProtoBuf Message，再序列化为字节切片（bytes -> PB -> bytes in WIRE format）
func (pm *ProtoManager) MarshalPB(name string, data []byte, options ...FindOption) ([]byte, error) {
	return pm.marshal(name, data, MESSAGE, options...)
}

// MarshalJSONPB 字节切片反序列化为ProtoBuf Message，再序列化为JSON字节切片（bytes -> PB -> bytes in JSON format）
func (pm *ProtoManager) MarshalJSONPB(name string, data []byte, options ...FindOption) ([]byte, error) {
	return pm.marshal(name, data, JSON, options...)
}

func (pm *ProtoManager) marshal(name string, data []byte, format Format, options ...FindOption) ([]byte, error) {
	m, err := pm.CreateMessage(name, options...)
	if err != nil {
		return nil, err
	} else if m == nil {
		return nil, errors.Errorf("protobuf message not found, message: %s", name)
	}
	defer pm.PutMessage(m)

	if err = UnmarshalMessage(data, m); err != nil {
		return nil, errors.Errorf(
			"failed to unmarshal byte data to message, data: %s, message: %s, error: %v",
			hex.EncodeToString(data), name, err,
		)
	}

	switch format {
	case JSON:
		return MarshalJSON(m)
	case TEXT:
		return MarshalText(m)
	default:
		return MarshalMessage(m)
	}
}

// UnmarshalPB JSON字节切片反序列化为ProtoBuf Message，再序列化为字节切片（JSON -> PB -> bytes in WIRE format）
func (pm *ProtoManager) UnmarshalPB(name string, data []byte, options ...FindOption) ([]byte, error) {
	return pm.unmarshal(name, data, MESSAGE, options...)
}

// UnmarshalJSONPB JSON字节切片反序列化为ProtoBuf Message，再序列化为JSON字节切片（JSON -> PB -> bytes in JSON format）
func (pm *ProtoManager) UnmarshalJSONPB(name string, data []byte, options ...FindOption) ([]byte, error) {
	return pm.unmarshal(name, data, JSON, options...)
}

// UnmarshalMessage JSON字节切片反序列化为ProtoBuf Message（JSON -> PB）
func (pm *ProtoManager) UnmarshalMessage(message *dynamicpb.Message, data []byte) error {
	return pm.unmarshalMessage(message, data)
}

func (pm *ProtoManager) unmarshal(name string, data []byte, format Format, options ...FindOption) ([]byte, error) {
	m, err := pm.CreateMessage(name, options...)
	if err != nil {
		return nil, err
	} else if m == nil {
		return nil, errors.Errorf("protobuf message not found, message: %s", name)
	}
	defer pm.PutMessage(m)

	if err := pm.unmarshalMessage(m, data); err != nil {
		return nil, errors.Errorf(
			"failed to unmarshal json data to message, data: %s, message: %s, error: %v",
			data, name, err,
		)
	}

	switch format {
	case JSON:
		return MarshalJSON(m)
	case TEXT:
		return MarshalText(m)
	default:
		return MarshalMessage(m)
	}
}

func (pm *ProtoManager) unmarshalMessage(message *dynamicpb.Message, data []byte) error {
	// 补全数据
	patchData := patchDataByMessage(message.Descriptor(), data)
	return UnmarshalJSON(patchData, message)
}

// patchDataByMessage 补全传入的数据
// 1. 对于`message`中的`enum`字段，如果`data`中对应的值为`string`类型则尝试转换为`int32`类型
// 2. 对于`message`中的`required`字段，如果`data`中没有设置则设置默认值
func patchDataByMessage(md protoreflect.MessageDescriptor, data []byte) []byte {
	// `data` (bytes) => `m` (map[string]any)
	var m map[string]any
	err := jsonx.Unmarshal(data, &m)
	if err != nil {
		logx.Errorf("failed to unmarshal data to json object, data: %s", data)
		return data
	}

	// 尝试修复枚举字段（json.Number => int32）
	tryToFixEnumFields(md, m)

	if md.Syntax() == protoreflect.Proto2 {
		// 填写必须字段的默认值
		fillDefaultValue(md, m)
	}

	out, err := jsonx.Marshal(m)
	if err != nil {
		logx.Errorf("failed to marshal json object to bytes in json format, data: %+v", m)
		return data
	}

	return out
}

func tryToFixEnumFields(md protoreflect.MessageDescriptor, m map[string]any) {
	if m == nil {
		return
	}

	fds := md.Fields()
	for i := 0; i < fds.Len(); i++ {
		fd := fds.Get(i)
		key := string(fd.Name())
		v, ok := m[key]
		if !ok {
			continue
		}

		switch fd.Kind() {
		case protoreflect.MessageKind:
			if vv, ok := v.(map[string]any); ok {
				tryToFixEnumFields(fd.Message(), vv)
			}
		case protoreflect.EnumKind:
			rv := reflect.ValueOf(v)
			rv = reflect.Indirect(rv)
			if rv.Kind() != reflect.String {
				continue
			}

			i, err := strconv.ParseInt(rv.String(), 10, 64)
			if err != nil {
				continue
			} else if i < math.MinInt32 || i > math.MaxInt32 {
				continue
			}

			m[key] = int32(i)
		}
	}
}

func fillDefaultValue(md protoreflect.MessageDescriptor, m map[string]any) {
	fds := md.Fields()
	// 只对`required`字段设置默认值
	for i, nums := 0, md.RequiredNumbers(); i < nums.Len(); i++ {
		fd := fds.ByNumber(nums.Get(i))
		if !fd.HasDefault() {
			var (
				name     = string(fd.Name())
				jsonName = fd.JSONName()
				textName = fd.TextName()

				key    string
				exists bool
			)

			if _, exists = m[name]; exists {
				key = name
			} else if _, exists = m[jsonName]; exists {
				key = jsonName
			} else if _, exists = m[textName]; exists {
				key = textName
			} else {
				key = name
			}

			if !exists {
				switch fd.Kind() {
				case protoreflect.MessageKind:
					// 存在栈溢出的风险
					m[key] = make(map[string]any)
					fillDefaultValue(fd.Message(), m[key].(map[string]any))
				case protoreflect.StringKind:
					m[key] = ""
				case protoreflect.BoolKind:
					m[key] = false
				case protoreflect.BytesKind:
					m[key] = nil
				default: // 对于`protoreflect.EnumKind`类型暂时也只能设置为`0`，但有可能存在问题
					m[key] = 0
				}
			} else if fd.Kind() == protoreflect.MessageKind {
				if val, isMap := m[key].(map[string]any); isMap {
					fillDefaultValue(fd.Message(), val)
				}
			}
		}
	}
}

type noopHandler struct{}

func (h noopHandler) OnVisitDirectory(_ string, _ fs.FileInfo) bool                 { return true }
func (h noopHandler) OnVisitFile(_ string, _ fs.FileInfo) bool                      { return true }
func (h noopHandler) OnInitFileDescriptor(_ protoreflect.FileDescriptor) bool       { return true }
func (h noopHandler) OnInitServiceDescriptor(_ protoreflect.ServiceDescriptor) bool { return true }
func (h noopHandler) OnInitMethodDescriptor(_ protoreflect.MethodDescriptor) bool   { return true }
func (h noopHandler) OnInitMessageDescriptor(_ *protoregistry.Types, _ protoreflect.MessageDescriptor) bool {
	return true
}

func (h noopHandler) OnInitEnumDescriptor(_ *protoregistry.Types, _ protoreflect.EnumDescriptor) bool {
	return true
}

func (h noopHandler) OnInitExtensionDescriptor(_ *protoregistry.Types, _ protoreflect.ExtensionDescriptor) bool {
	return true
}
