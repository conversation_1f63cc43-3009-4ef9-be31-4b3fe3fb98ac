package protobuf

import (
	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/dynamicpb"
)

const (
	NullValueEnumFullName = "google.protobuf.NullValue"
)

var defaultParseOptions = ParseOptions{
	UseProtoNames:  true,
	UseEnumNumbers: true,
}

func ParseMessage(in protoreflect.Message) (out map[string]any) {
	return defaultParseOptions.ParseMessage(in)
}

type ParseOptions struct {
	// UseProtoNames uses proto field name instead of lowerCamelCase name in JSON
	// field names.
	UseProtoNames bool

	// UseEnumNumbers emits enum values as numbers.
	UseEnumNumbers bool
}

func (o ParseOptions) ParseMessage(in protoreflect.Message) (out map[string]any) {
	return o.parseMessage(in)
}

func (o ParseOptions) parseMessage(in protoreflect.Message) (out map[string]any) {
	if in == nil || in.Descriptor() == nil || in.Descriptor().Fields() == nil {
		return make(map[string]any)
	}

	fields := in.Descriptor().Fields()
	out = make(map[string]any, fields.Len())

	// 注：
	// 文件：`google.golang.org/protobuf/types/dynamicpb/dynamic.go`
	// 函数：`func (m *Message) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool)`
	// `message`对象中没有设置的字段，将不会回调传入的`f`函数，因此需要换一种方法来遍历`message`对象的字段
	//
	//in.Range(
	//	func(fd protoreflect.FieldDescriptor, val protoreflect.Value) bool {
	//		name := fd.JSONName()
	//		if o.UseProtoNames {
	//			name = fd.TextName()
	//		}
	//
	//		out[name] = o.parseValue(val, fd)
	//		return true
	//	},
	//)

	for i := 0; i < fields.Len(); i++ {
		field := fields.Get(i)
		value := in.Get(field)

		name := field.JSONName()
		if o.UseProtoNames {
			name = field.TextName()
		}

		out[name] = o.parseValue(value, field)
	}

	return out
}

func (o ParseOptions) parseValue(val protoreflect.Value, fd protoreflect.FieldDescriptor) (out any) {
	if !val.IsValid() {
		return nil
	}

	switch {
	case fd.IsList():
		return o.parseList(val.List(), fd)
	case fd.IsMap():
		return o.parseMap(val.Map(), fd)
	default:
		return o.parseSingular(val, fd)
	}
}

func (o ParseOptions) parseList(l protoreflect.List, fd protoreflect.FieldDescriptor) (out []any) {
	out = make([]any, 0, l.Len())
	for i := 0; i < l.Len(); i++ {
		out = append(out, o.parseSingular(l.Get(i), fd))
	}

	return out
}

func (o ParseOptions) parseMap(m protoreflect.Map, fd protoreflect.FieldDescriptor) (out map[string]any) {
	out = make(map[string]any, m.Len())
	m.Range(
		func(key protoreflect.MapKey, val protoreflect.Value) bool {
			out[key.String()] = o.parseSingular(val, fd.MapValue())
			return true
		},
	)

	return out
}

func (o ParseOptions) parseSingular(val protoreflect.Value, fd protoreflect.FieldDescriptor) (out any) {
	switch kind := fd.Kind(); kind {
	case protoreflect.BoolKind:
		return val.Bool()
	case protoreflect.StringKind:
		return val.String()
	case protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind:
		return int32(val.Int())
	case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
		return val.Int()
	case protoreflect.Uint32Kind, protoreflect.Fixed32Kind:
		return uint32(val.Uint())
	case protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
		return val.Uint()
	case protoreflect.FloatKind:
		return float32(val.Float())
	case protoreflect.DoubleKind:
		return val.Float()
	case protoreflect.BytesKind:
		return val.Bytes() // base64.StdEncoding.EncodeToString(val.Bytes())
	case protoreflect.EnumKind:
		if fd.Enum().FullName() == NullValueEnumFullName {
			return nil
		}

		desc := fd.Enum().Values().ByNumber(val.Enum())
		if o.UseEnumNumbers || desc == nil {
			return int32(val.Enum())
		}

		return string(desc.Name())
	case protoreflect.MessageKind, protoreflect.GroupKind:
		v := val.Interface()
		if v == nil {
			return nil
		} else if m, ok := v.(*dynamicpb.Message); ok && (m == nil || !m.ProtoReflect().IsValid()) {
			return nil
		} else if val.String() == "<nil>" {
			return nil
		}

		return o.parseMessage(val.Message())
	default:
		logx.Errorf("%v has unknown kind: %v", fd.FullName(), kind)
		return nil
	}
}
