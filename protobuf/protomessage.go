package protobuf

import (
	protoV1 "github.com/golang/protobuf/proto"
	"github.com/jhump/protoreflect/dynamic"
	"github.com/pkg/errors"
	protoV2 "google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/dynamicpb"
)

var (
	defaultProtoMessageMarshalOptions   = protoV2.MarshalOptions{}
	defaultProtoMessageUnmarshalOptions = protoV2.UnmarshalOptions{}
)

// MarshalMessage writes the given proto.Message in wire format.
func MarshalMessage(a any) ([]byte, error) {
	switch m := a.(type) {
	case dynamicpb.Message:
		return defaultProtoMessageMarshalOptions.Marshal((&m).Interface())
	case *dynamicpb.Message:
		return defaultProtoMessageMarshalOptions.Marshal(m.Interface())
	case protoV2.Message:
		return defaultProtoMessageMarshalOptions.Marshal(m)
	case dynamic.Message:
		return (&m).Marshal()
	case *dynamic.Message:
		return m.Marshal()
	case protoV1.Message:
		return protoV1.Marshal(m)
	default:
		return nil, errors.Errorf(
			"%T is neither dynamicpb.Message nor protoV2.Message nor dynamic.Message nor protoV1.Message", a,
		)
	}
}

// UnmarshalMessage reads the given []byte and populates the given proto.Message.
// The provided message must be mutable (e.g., a non-nil pointer to a message).
func UnmarshalMessage(data []byte, a any) error {
	switch m := a.(type) {
	case dynamicpb.Message:
		return defaultProtoMessageUnmarshalOptions.Unmarshal(data, (&m).Interface())
	case *dynamicpb.Message:
		return defaultProtoMessageUnmarshalOptions.Unmarshal(data, m.Interface())
	case protoV2.Message:
		return defaultProtoMessageUnmarshalOptions.Unmarshal(data, m)
	case dynamic.Message:
		return (&m).Unmarshal(data)
	case *dynamic.Message:
		return m.Unmarshal(data)
	case protoV1.Message:
		return protoV1.Unmarshal(data, m)
	default:
		return errors.Errorf(
			"%T is neither a dynamicpb.Message nor protoV2.Message nor dynamic.Message nor protoV1.Message", a,
		)
	}
}
