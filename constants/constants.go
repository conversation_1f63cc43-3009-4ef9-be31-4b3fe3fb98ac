package constants

const (
	SensitiveWorld = "******"
	NoNeedToListen = "no need to listen"
)

// LogicOption 逻辑运算
type LogicOption = string

const (
	AND LogicOption = "AND" // 且
	OR  LogicOption = "OR"  // 或
)

// CompareOption 比较运算
type CompareOption = string

const (
	EQ                CompareOption = "EQ"                // 等于
	NE                CompareOption = "NE"                // 不等于
	LT                CompareOption = "LT"                // 小于
	LE                CompareOption = "LE"                // 小于等于
	GT                CompareOption = "GT"                // 大于
	GE                CompareOption = "GE"                // 大于等于
	Contains          CompareOption = "CONTAINS"          // 包含
	NotContains       CompareOption = "NOT_CONTAINS"      // 不包含
	Regex             CompareOption = "RE"                // 正则
	DBIn              CompareOption = "IN"                // 数据库的`IN`操作
	DBNotIn           CompareOption = "NOT_IN"            // 数据库的`NOT IN`操作
	DBLike            CompareOption = "LIKE"              // 数据库的`LIKE`操作
	DBNotLike         CompareOption = "NOT_LIKE"          // 数据库的`NOT LIKE`操作
	DBBetween         CompareOption = "BETWEEN"           // 数据库的`BETWEEN`操作
	DBJsonContains    CompareOption = "JSON_CONTAINS"     // 数据库的`JSON_CONTAINS`操作
	DBNotJsonContains CompareOption = "NOT_JSON_CONTAINS" // 数据库的`NOT JSON_CONTAINS`操作
	IsNull            CompareOption = "IS_NULL"           // 字段为空
	IsNotNull         CompareOption = "IS_NOT_NULL"       // 字段不为空

)

// OrderType 排序类型
type OrderType = string

const (
	ASC  OrderType = "ASC"  // 升序
	DESC OrderType = "DESC" // 降序
)

// LogicDeleteFlag 逻辑删除标识
type LogicDeleteFlag = int8

const (
	NotDeleted LogicDeleteFlag = iota // 0: 未删除
	HasDeleted                        // 1: 已删除
)

// LatestVersionFlag 最新版本标识
type LatestVersionFlag = int8

const (
	IsNotLatestVersion LatestVersionFlag = iota // 0: 不是最新版本
	IsLatestVersion                             // 1: 是最新版本
)

// CommonStatusFlag 通用状态标识
type CommonStatusFlag = int8

const (
	NullStatus    CommonStatusFlag = iota // 0: Null（正常情况下不使用）
	EnableStatus                          // 1: 生效
	DisableStatus                         // 2: 失效
)

// MetricErrorType metric错误类型
type MetricErrorType = string

const (
	SystemError   MetricErrorType = "system"
	BusinessError MetricErrorType = "business"
	EmptyError    MetricErrorType = "empty"
)

// FinishedFlag 完成标识
type FinishedFlag = int8

const (
	NotFinished FinishedFlag = iota // 0: 未完成
	Finished                        // 1: 已完成
)

type Scheme string

const (
	TCP            Scheme = "tcp"
	WS             Scheme = "ws"
	WSS            Scheme = "wss"
	HTTP           Scheme = "http"
	HTTPS          Scheme = "https"
	GRPC           Scheme = "grpc"
	GIT            Scheme = "git"
	SSH            Scheme = "ssh"
	REDIS          Scheme = "redis"
	REDIS_CLUSTER  Scheme = "redis-cluster"
	REDIS_SENTINEL Scheme = "redis-sentinel"
)
