package mqworker

import "context"

type (
	BytesPayloadTask              func(payload []byte) (err error)
	StringPayloadTask             func(payload string) (err error)
	BytesPayloadBytesResultTask   func(payload []byte) (result []byte, err error)
	StringPayloadBytesResultTask  func(payload string) (result []byte, err error)
	BytesPayloadStringResultTask  func(payload []byte) (result string, err error)
	StringPayloadStringResultTask func(payload string) (result string, err error)

	BytesPayloadTaskWithContext              func(ctx context.Context, payload []byte) (err error)
	StringPayloadTaskWithContext             func(ctx context.Context, payload string) (err error)
	BytesPayloadBytesResultTaskWithContext   func(ctx context.Context, payload []byte) (result []byte, err error)
	StringPayloadBytesResultTaskWithContext  func(ctx context.Context, payload string) (result []byte, err error)
	BytesPayloadStringResultTaskWithContext  func(ctx context.Context, payload []byte) (result string, err error)
	StringPayloadStringResultTaskWithContext func(ctx context.Context, payload string) (result string, err error)
)
