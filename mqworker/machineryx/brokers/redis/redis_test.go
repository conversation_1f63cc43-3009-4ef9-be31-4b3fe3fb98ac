package redis

import (
	"context"
	"testing"

	"github.com/redis/go-redis/v9"
)

func TestLua(t *testing.T) {
	rdb := redis.NewClient(
		&redis.Options{
			Addr:     "127.0.0.1:6379",
			Password: "Quwan@2020",
			DB:       0,
		},
	)

	ret, err := rdb.Eval(
		context.Background(), `
local member = redis.call("ZRANGEBYSCORE", KEYS[1], "-inf", ARGV[1], "LIMIT", 0, 1)[1]

if member then
  local key = ARGV[2] .. member
  local val = redis.call('GET', key)
  return {member, val}
end

return nil
`, []string{"mq"}, 30, "name:",
	).Result()
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("Result: %+v\n", ret)
}
