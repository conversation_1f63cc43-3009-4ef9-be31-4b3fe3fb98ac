package redis

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-redsync/redsync/v4"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/utils"

	"github.com/RichardKnop/machinery/v2/brokers/errs"
	// "github.com/RichardKnop/machinery/v2/brokers/iface"
	"github.com/RichardKnop/machinery/v2/config"
	"github.com/RichardKnop/machinery/v2/log"
	"github.com/RichardKnop/machinery/v2/tasks"
	"github.com/shopspring/decimal"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx/brokers/iface"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx/common"
)

const defaultRedisDelayedTasksKey = "delayed_tasks"

// const defaultLockPrefix = "MxLock:"
// const defaultLockValue = "MxLock:789654"
const defaultJobPrefix = "Mxjob:"

// BrokerGR represents a Redis broker
type BrokerGR struct {
	common.Broker
	rclient      redis.UniversalClient
	consumingWG  sync.WaitGroup // wait group to make sure whole consumption completes
	processingWG sync.WaitGroup // use wait group to make sure task processing completes
	delayedWG    sync.WaitGroup
	// If set, path to a socket file overrides hostname
	socketPath           string
	redsync              *redsync.Redsync
	redisOnce            sync.Once
	redisDelayedTasksKey string
}

// NewGR creates new Broker instance
func NewGR(cnf *config.Config, addrs []string, db int) iface.Broker {
	b := &BrokerGR{Broker: common.NewBroker(cnf)}

	var password string
	parts := strings.Split(addrs[0], "@")
	if len(parts) >= 2 {
		// with password
		password = strings.Join(parts[:len(parts)-1], "@")
		addrs[0] = parts[len(parts)-1] // addr is the last one without @
	}

	ropt := &redis.UniversalOptions{
		Addrs:    addrs,
		DB:       db,
		Password: password,
	}
	if cnf.Redis != nil {
		ropt.MasterName = cnf.Redis.MasterName
	}

	b.rclient = redis.NewUniversalClient(ropt)
	if cnf.Redis.DelayedTasksKey != "" {
		b.redisDelayedTasksKey = cnf.Redis.DelayedTasksKey
	} else {
		b.redisDelayedTasksKey = defaultRedisDelayedTasksKey
	}
	return b
}

// StartConsuming enters a loop and waits for incoming messages
func (b *BrokerGR) StartConsuming(consumerTag string, concurrency int, taskProcessor iface.TaskProcessor) (
	bool, error,
) {
	b.consumingWG.Add(1)
	defer b.consumingWG.Done()

	if concurrency < 1 {
		concurrency = runtime.NumCPU() * 2
	}

	b.Broker.StartConsuming(consumerTag, concurrency, taskProcessor)

	// Ping the server to make sure connection is live
	_, err := b.rclient.Ping(context.Background()).Result()
	if err != nil {
		b.GetRetryFunc()(b.GetRetryStopChan())

		// Return err if retry is still true.
		// If retry is false, broker.StopConsuming() has been called and
		// therefore Redis might have been stopped. Return nil exit
		// StartConsuming()
		if b.GetRetry() {
			return b.GetRetry(), err
		}
		return b.GetRetry(), errs.ErrConsumerStopped
	}

	// Channel to which we will push tasks ready for processing by worker
	deliveries := make(chan []byte, concurrency)
	pool := make(chan struct{}, concurrency)

	// initialize worker pool with maxWorkers workers
	fmt.Println(concurrency)
	for i := 0; i < concurrency; i++ {
		pool <- struct{}{}
	}

	// A receiving goroutine keeps popping messages from the queue by BLPOP
	// If the message is valid and can be unmarshaled into a proper structure
	// we send it to the deliveries channel
	go func() {
		log.INFO.Print("[*] Waiting for messages. To exit press CTRL+C")

		for {
			select {
			// A way to stop this goroutine from b.StopConsuming
			case <-b.GetStopChan():
				close(deliveries)
				return
			case <-pool:
				select {
				case <-b.GetStopChan():
					close(deliveries)
					return
				default:
				}

				if taskProcessor.PreConsumeHandler() {
					task, _ := b.nextTask(getQueueGR(b.GetConfig(), taskProcessor))
					// TODO: should this error be ignored?
					if len(task) > 0 {
						deliveries <- task
					}
				}

				pool <- struct{}{}
			}
		}
	}()

	// A goroutine to watch for delayed tasks and push them to deliveries
	// channel for consumption by the worker
	b.delayedWG.Add(1)
	go func() {
		defer b.delayedWG.Done()

		for {
			select {
			// A way to stop this goroutine from b.StopConsuming
			case <-b.GetStopChan():
				return
			default:
				task, err := b.nextDelayedTask(b.redisDelayedTasksKey)
				if err != nil {
					continue
				}

				signature := new(tasks.Signature)
				decoder := json.NewDecoder(bytes.NewReader(task))
				decoder.UseNumber()
				if err := decoder.Decode(signature); err != nil {
					log.ERROR.Print(errs.NewErrCouldNotUnmarshalTaskSignature(task, err))
				}

				if err := b.Publish(context.Background(), signature); err != nil {
					log.ERROR.Print(err)
				}
			}
		}
	}()

	if err := b.consume(deliveries, concurrency, pool, taskProcessor); err != nil {
		return b.GetRetry(), err
	}

	// Waiting for any tasks being processed to finish
	b.processingWG.Wait()

	return b.GetRetry(), nil
}

// StopConsuming quits the loop
func (b *BrokerGR) StopConsuming() {
	b.Broker.StopConsuming()
	// Waiting for the delayed tasks goroutine to have stopped
	b.delayedWG.Wait()
	// Waiting for consumption to finish
	b.consumingWG.Wait()

	b.rclient.Close()
}

// Publish places a new message on the default queue
func (b *BrokerGR) Publish1(ctx context.Context, signature *tasks.Signature) error {
	// Adjust routing key (this decides which queue the message will be published to)
	b.Broker.AdjustRoutingKey(signature)

	msg, err := json.Marshal(signature)
	if err != nil {
		return fmt.Errorf("JSON marshal error: %s", err)
	}

	// Check the ETA signature field, if it is set and it is in the future,
	// delay the task
	if signature.ETA != nil {
		now := time.Now().UTC()

		if signature.ETA.After(now) {
			score := signature.ETA.UnixNano()
			err = b.rclient.ZAdd(
				context.Background(), b.redisDelayedTasksKey, redis.Z{Score: float64(score), Member: msg},
			).Err()
			return err
		}
	}

	err = b.rclient.RPush(context.Background(), signature.RoutingKey, msg).Err()
	return err
}

// Input:
// KEYS[1] -> key
// KEYS[2] -> routing key
// --
// ARGV[1] -> task message
// ARGV[2] -> current time in unix time
// ARGV[3] -> task UUID
//
// Output:
// Returns 1 if successfully added
// Returns 0 if task ID conflicts with another task
// Returns -1 if task unique key already exists
var enqueueCmd = redis.NewScript(
	`
local ok = redis.call("SET", KEYS[1], ARGV[1], "NX")
if not ok then
  return -1 
end
if redis.call("ZADD", KEYS[2], ARGV[2], ARGV[3]) ~= 1 then
  return 0
end
return 1
`,
)

// Publish 把原来的redis的队列的数据结构由list改成zset
func (b *BrokerGR) Publish(ctx context.Context, signature *tasks.Signature) error {
	// Adjust routing key (this decides which queue the message will be published to)
	b.Broker.AdjustRoutingKey(signature)

	msg, err := json.Marshal(signature)
	if err != nil {
		return fmt.Errorf("JSON marshal error: %s", err)
	}

	// Check the ETA signature field, if it is set and it is in the future,
	// delay the task
	if signature.ETA != nil {
		now := time.Now().UTC()

		if signature.ETA.After(now) {
			score := signature.ETA.UnixNano()
			err = b.rclient.ZAdd(
				context.Background(), b.redisDelayedTasksKey, redis.Z{Score: float64(score), Member: msg},
			).Err()
			return err
		}
	}

	// lockKey := fmt.Sprintf("%s%s", defaultLockPrefix, signature.UUID)
	jobKey := fmt.Sprintf("%s%s", defaultJobPrefix, signature.UUID)
	// job key对应的value是任务内容，zset存放任务id
	score := time.Now().UTC().UnixNano()

	n, err := enqueueCmd.Run(ctx, b.rclient, []string{jobKey, signature.RoutingKey}, msg, score, signature.UUID).Int64()
	if err != nil {
		return err
	} else if n == -1 {
		log.ERROR.Printf("Task unique key[%s] already exists, enqueue message: %s", jobKey, msg)
	} else if n == 0 {
		log.WARNING.Printf("Task UUID[%s] is already in the queue[%s]", signature.UUID, signature.RoutingKey)
	}
	return nil

	//pipe := b.rclient.TxPipeline()
	//// pipe.Set(ctx, jobKey, msg, time.Duration(2*24*60*60)*time.Second)
	//pipe.Set(ctx, jobKey, msg, 0)
	//pipe.ZAdd(ctx, signature.RoutingKey, &redis.Z{Score: float64(score), Member: signature.UUID})
	//_, err = pipe.Exec(ctx)
	//if err != nil {
	//	return err
	//}
	//// 此处不需要判断pipe中的指令是否全部执行成功，若有错误的指令并产生特殊情况，获取任务函数会自行处理
	//return nil
}

// GetPendingTasks returns a slice of task signatures waiting in the queue
// func (b *BrokerGR) GetPendingTasks(queue string) ([]*tasks.Signature, error) {

// 	if queue == "" {
// 		queue = b.GetConfig().DefaultQueue
// 	}
// 	results, err := b.rclient.LRange(context.Background(), queue, 0, -1).Result()
// 	if err != nil {
// 		return nil, err
// 	}

// 	taskSignatures := make([]*tasks.Signature, len(results))
// 	for i, result := range results {
// 		signature := new(tasks.Signature)
// 		decoder := json.NewDecoder(strings.NewReader(result))
// 		decoder.UseNumber()
// 		if err := decoder.Decode(signature); err != nil {
// 			return nil, err
// 		}
// 		taskSignatures[i] = signature
// 	}
// 	return taskSignatures, nil
// }

// GetDelayedTasks returns a slice of task signatures that are scheduled, but not yet in the queue
func (b *BrokerGR) GetDelayedTasks() ([]*tasks.Signature, error) {
	results, err := b.rclient.ZRange(context.Background(), b.redisDelayedTasksKey, 0, -1).Result()
	if err != nil {
		return nil, err
	}

	taskSignatures := make([]*tasks.Signature, len(results))
	for i, result := range results {
		signature := new(tasks.Signature)
		decoder := json.NewDecoder(strings.NewReader(result))
		decoder.UseNumber()
		if err := decoder.Decode(signature); err != nil {
			return nil, err
		}
		taskSignatures[i] = signature
	}
	return taskSignatures, nil
}

// consume takes delivered messages from the channel and manages a worker pool
// to process tasks concurrently
func (b *BrokerGR) consume1(deliveries <-chan []byte, concurrency int, taskProcessor iface.TaskProcessor) error {
	errorsChan := make(chan error, concurrency*2)
	pool := make(chan struct{}, concurrency)

	// init pool for Worker tasks execution, as many slots as Worker concurrency param
	go func() {
		for i := 0; i < concurrency; i++ {
			pool <- struct{}{}
		}
	}()

	for {
		select {
		case err := <-errorsChan:
			return err
		case d, open := <-deliveries:
			if !open {
				return nil
			}
			if concurrency > 0 {
				// get execution slot from pool (blocks until one is available)
				<-pool
			}

			b.processingWG.Add(1)

			// Consume the task inside a goroutine so multiple tasks
			// can be processed concurrently
			go func() {
				if err := b.consumeOne(d, taskProcessor); err != nil {
					errorsChan <- err
				}

				b.processingWG.Done()

				if concurrency > 0 {
					// give slot back to pool
					pool <- struct{}{}
				}
			}()
		}
	}
}

// 修改过的consume
func (b *BrokerGR) consume(
	deliveries <-chan []byte, concurrency int, pool chan struct{}, taskProcessor iface.TaskProcessor,
) error {
	errorsChan := make(chan error, concurrency*2)
	// pool := make(chan struct{}, concurrency)

	// init pool for Worker tasks execution, as many slots as Worker concurrency param
	// go func() {
	// 	for i := 0; i < concurrency; i++ {
	// 		pool <- struct{}{}
	// 	}
	// }()

	for {
		select {
		case err := <-errorsChan:
			return err
		case d, open := <-deliveries:
			if !open {
				return nil
			}
			if concurrency > 0 {
				// get execution slot from pool (blocks until one is available)
				<-pool
			}

			b.processingWG.Add(1)

			// Consume the task inside a goroutine so multiple tasks
			// can be processed concurrently
			go func() {
				if err := b.consumeOne(d, taskProcessor); err != nil {
					errorsChan <- err
				}

				b.processingWG.Done()

				if concurrency > 0 {
					// give slot back to pool
					pool <- struct{}{}
				}
			}()
		}
	}
}

// consumeOne processes a single message using TaskProcessor
func (b *BrokerGR) consumeOne(delivery []byte, taskProcessor iface.TaskProcessor) error {
	signature := new(tasks.Signature)
	decoder := json.NewDecoder(bytes.NewReader(delivery))
	decoder.UseNumber()
	if err := decoder.Decode(signature); err != nil {
		return errs.NewErrCouldNotUnmarshalTaskSignature(delivery, err)
	}

	// If the task is not registered, we requeue it,
	// there might be different workers for processing specific tasks
	if !b.IsTaskRegistered(signature.Name) {
		if signature.IgnoreWhenTaskNotRegistered {
			return nil
		}
		log.INFO.Printf("Task not registered with this worker. Requeuing message: %s", delivery)

		// b.rclient.RPush(context.Background(), getQueueGR(b.GetConfig(), taskProcessor), delivery)
		err := b.Publish(context.Background(), signature)
		if err != nil {
			log.ERROR.Print(err)
		}
		return err
	}

	log.DEBUG.Printf("Received new message: %s", delivery)

	return taskProcessor.Process(signature)
}

// nextTask pops next available task from the default queue
func (b *BrokerGR) nextTask1(queue string) (result []byte, err error) {
	pollPeriodMilliseconds := 1000 // default poll period for normal tasks
	if b.GetConfig().Redis != nil {
		configuredPollPeriod := b.GetConfig().Redis.NormalTasksPollPeriod
		if configuredPollPeriod > 0 {
			pollPeriodMilliseconds = configuredPollPeriod
		}
	}
	pollPeriod := time.Duration(pollPeriodMilliseconds) * time.Millisecond

	items, err := b.rclient.BLPop(context.Background(), pollPeriod, queue).Result()
	if err != nil {
		return []byte{}, err
	}

	// items[0] - the name of the key where an element was popped
	// items[1] - the value of the popped element
	if len(items) != 2 {
		return []byte{}, redis.Nil
	}

	result = []byte(items[1])

	return result, nil
}

// Input:
// KEYS[1] -> mq name
// --
// ARGV[1] -> current time in unix time
// ARGV[2] -> task key prefix
//
// Output:
// Returns nil if no processable task is found in the given queue.
// Returns the task UUID and an encoded task message.
var dequeueCmd = redis.NewScript(
	`
local member = redis.call("ZRANGEBYSCORE", KEYS[1], "-inf", ARGV[1], "LIMIT", 0, 1)[1]

if member then
  local key = ARGV[2] .. member
  local val = redis.call('GET', key)
  redis.call('ZREM', KEYS[1], member)
  redis.call('DEL', key)
  return {member, val}
end

return nil
`,
)

// 重写的nextTask
// 获取任务的逻辑
// 1.首先获取zset中第一个的member的名称
// 2.然后根据名称去生成改任务名称的lockname，然后使用redis的watch 它
// 3.判断lockname的value是否不为空，为空则unwatch它，便结束这一轮的任务
// 4.开启事务，删除zset中这个member，删除lockname，执行事务，如果执行事务失败的话，unwatch lockname，结束这一轮
// 5.根据member再去redis去获取任务内容，返回任务内容    发现一个bug，如果zset中最早的那个任务的lock丢失的话，那么所有的worker都会卡在这里，现在感觉唯一能够触发这个bug的就是获取的时候，pipeline只删除了lock但没有删除zset中的任务，理论上可能会有这种问题，但实际应该不会出现
// 备注：5的bug已处理
// 移除RetryCompletedTask功能，用job key 替代 lock key
func (b *BrokerGR) nextTask(queue string) ([]byte, error) {
	pollPeriodMilliseconds := 200 // default poll period for normal tasks
	if b.GetConfig().Redis != nil {
		configuredPollPeriod := b.GetConfig().Redis.NormalTasksPollPeriod
		if configuredPollPeriod > 0 {
			pollPeriodMilliseconds = configuredPollPeriod
		}
	}
	pollPeriod := time.Duration(pollPeriodMilliseconds) * time.Millisecond

	ctx, cancel := context.WithTimeout(context.Background(), pollPeriod)
	defer cancel()

	now := time.Now().UTC().UnixNano()
	timer := time.NewTimer(pollPeriod)
	for {
		select {
		case <-timer.C:
			return nil, redis.Nil
		default:
			ret, err := dequeueCmd.Run(ctx, b.rclient, []string{queue}, now, defaultJobPrefix).Result()
			if err != nil {
				return nil, err
			} else if ret != nil {
				ss, err := cast.ToStringSliceE(ret)
				if err != nil {
					return nil, err
				}

				log.INFO.Printf("Find a task, UUID: %s, message: %s", ss[0], ss[1])
				return utils.StringToByteSlice(ss[1]), err
			}

			// slow down the rate of accessing redis
			time.Sleep(pollPeriod / 10)
		}
	}

	//var (
	//	items  []string
	//	result []byte
	//	err    error
	//)
	//
	//// 根据最早时间获取第一个任务
	//now := time.Now().UTC().UnixNano()
	//items, err = b.rclient.ZRangeByScore(context.Background(), queue, &redis.ZRangeBy{
	//	Min: "0", Max: strconv.FormatInt(now, 10), Offset: 0, Count: 1,
	//}).Result()
	//if err != nil {
	//	return []byte{}, err
	//}
	//if len(items) != 1 {
	//	return []byte{}, redis.Nil
	//}
	//
	//jobKey := fmt.Sprintf("%s%s", defaultJobPrefix, items[0])
	//
	//watchFunc := func(tx *redis.Tx) error {
	//	// only return the first zrange value if there are no other changes in this key
	//	// to make sure a delayed task would only be consumed once
	//
	//	ctx := context.Background()
	//	// 定义删除队列的指定任务和任务的锁的pipe函数
	//	fn := func(pipe redis.Pipeliner) error {
	//		pipe.ZRem(ctx, queue, items[0])
	//		pipe.Del(ctx, jobKey)
	//		return nil
	//	}
	//	// 判断lockKey是否存在
	//	var le error
	//	result, le = tx.Get(ctx, jobKey).Bytes()
	//	if le != nil && le == redis.Nil {
	//		_, err = tx.TxPipelined(ctx, fn)
	//		return le
	//	}
	//	// 上述情况依然删除两个key，原因是 如果zset中最早的任务的lock key已经被删除了，会导致所有worker都获取不到这个最早的任务，并且都卡在这里。这里的操作是兜底操作
	//	_, err = tx.TxPipelined(ctx, fn)
	//	// TODO 这里需要判断pipe的指令执行结果
	//	return err
	//}
	//
	//if err = b.rclient.Watch(context.Background(), watchFunc, jobKey); err != nil {
	//	return []byte{}, err
	//}
	//
	//return result, err
}

// nextDelayedTask pops a value from the ZSET key using WATCH/MULTI/EXEC commands.
func (b *BrokerGR) nextDelayedTask(key string) ([]byte, error) {
	//pipe := b.rclient.Pipeline()
	//
	//defer func() {
	//	// Return connection to normal state on error.
	//	// https://redis.io/commands/discard
	//	if err != nil {
	//		pipe.Discard()
	//	}
	//}()

	var (
		result []byte
		items  []string
		err    error
	)

	pollPeriod := 500 // default poll period for delayed tasks
	if b.GetConfig().Redis != nil {
		configuredPollPeriod := b.GetConfig().Redis.DelayedTasksPollPeriod
		// the default period is 0, which bombards redis with requests, despite
		// our intention of doing the opposite
		if configuredPollPeriod > 0 {
			pollPeriod = configuredPollPeriod
		}
	}

	for {
		// Space out queries to ZSET so we don't bombard redis
		// server with relentless ZRANGEBYSCOREs
		time.Sleep(time.Duration(pollPeriod) * time.Millisecond)
		watchFunc := func(tx *redis.Tx) error {
			now := time.Now().UTC().UnixNano()

			// https://redis.io/commands/zrangebyscore
			ctx := context.Background()
			items, err = tx.ZRevRangeByScore(
				ctx, key, &redis.ZRangeBy{
					Min: "0", Max: strconv.FormatInt(now, 10), Offset: 0, Count: 1,
				},
			).Result()
			if err != nil {
				return err
			}
			if len(items) != 1 {
				return redis.Nil
			}

			// only return the first zrange value if there are no other changes in this key
			// to make sure a delayed task would only be consumed once
			_, err = tx.TxPipelined(
				ctx, func(pipe redis.Pipeliner) error {
					pipe.ZRem(ctx, key, items[0])
					result = []byte(items[0])
					return nil
				},
			)

			return err
		}

		if err = b.rclient.Watch(context.Background(), watchFunc, key); err != nil {
			return result, err
		} else {
			break // 下一次循环
		}
	}

	return result, err
}

func getQueueGR(config *config.Config, taskProcessor iface.TaskProcessor) string {
	customQueue := taskProcessor.CustomQueue()
	if customQueue == "" {
		return config.DefaultQueue
	}
	return customQueue
}

// 新增broker方法
func (b *BrokerGR) GetTaskInfo(s string) (*tasks.Signature, error) {
	jk := fmt.Sprintf("%s%s", defaultJobPrefix, s)
	delivery, err := b.rclient.Get(context.Background(), jk).Bytes()
	if err != nil {
		return nil, err
	}
	signature := new(tasks.Signature)
	decoder := json.NewDecoder(bytes.NewReader(delivery))
	decoder.UseNumber()
	if err := decoder.Decode(signature); err != nil {
		return nil, errs.NewErrCouldNotUnmarshalTaskSignature(delivery, err)
	}
	return signature, nil
}

func (b *BrokerGR) Top(s string) error {
	/*
		1.获取zset中的首个元素的score，如果没有则返回
		2.把score减1作为新的score给s
		3.watch s的lockkey ， 判断lockkey不为空， 设置zset中的score（用事务保存步骤的连贯性）
	*/
	signature, err := b.GetTaskInfo(s)
	if err != nil {
		return err
	}
	b.Broker.AdjustRoutingKey(signature) // 获取队列名
	now := time.Now().UTC().UnixNano()
	items, err := b.rclient.ZRangeByScoreWithScores(
		context.Background(), signature.RoutingKey, &redis.ZRangeBy{
			Min: "0", Max: strconv.FormatInt(now, 10), Offset: 0, Count: 1,
		},
	).Result()
	if err != nil {
		return err
	}
	if len(items) != 1 {
		return redis.Nil
	}
	var num float64 = 1000
	score, _ := decimal.NewFromFloat(items[0].Score).Sub(decimal.NewFromFloat(num)).Float64()
	// 拼接lock名称
	// lk := fmt.Sprintf("%s%s", defaultLockPrefix, items[0].Member)
	lk := fmt.Sprintf("%s%s", defaultJobPrefix, items[0].Member)

	watchFunc := func(tx *redis.Tx) error {
		// only return the first zrange value if there are no other changes in this key
		// to make sure a delayed task would only be consumed once
		// 判断lockKey是否存在
		ctx := context.Background()
		_, err := tx.Get(ctx, lk).Result()
		if err != nil {
			return err
		}
		_, err = tx.TxPipelined(
			ctx, func(pipe redis.Pipeliner) error {
				pipe.ZAdd(ctx, signature.RoutingKey, redis.Z{Score: score, Member: s})
				return nil
			},
		)
		return err
	}

	if err = b.rclient.Watch(context.Background(), watchFunc, lk); err != nil {
		return err
	}

	return nil
}

func (b *BrokerGR) Sort(t, s string, up bool) error {
	tSignature, err := b.GetTaskInfo(t)
	if err != nil {
		return err
	}
	b.Broker.AdjustRoutingKey(tSignature)

	sSignature, err := b.GetTaskInfo(s)
	if err != nil {
		return err
	}
	b.Broker.AdjustRoutingKey(sSignature)

	if sSignature.RoutingKey != tSignature.RoutingKey {
		return errors.New("两个任务不在同一个队列中")
	}

	// 获取目标任务的score
	tScore, err := b.rclient.ZScore(context.Background(), tSignature.RoutingKey, tSignature.UUID).Result()
	if err != nil {
		return err
	}
	var num float64 = 1000
	var sScore float64
	if up {
		sScore, _ = decimal.NewFromFloat(tScore).Sub(decimal.NewFromFloat(num)).Float64()
	} else {
		sScore, _ = decimal.NewFromFloat(tScore).Add(decimal.NewFromFloat(num)).Float64()
	}

	// 拼接lock名称
	// lk := fmt.Sprintf("%s%s", defaultLockPrefix, s)
	lk := fmt.Sprintf("%s%s", defaultJobPrefix, s)

	watchFunc := func(tx *redis.Tx) error {
		// only return the first zrange value if there are no other changes in this key
		// to make sure a delayed task would only be consumed once
		// 判断lockKey是否存在
		ctx := context.Background()
		_, err := tx.Get(ctx, lk).Result()
		if err != nil {
			return err
		}
		_, err = tx.TxPipelined(
			ctx, func(pipe redis.Pipeliner) error {
				pipe.ZAdd(ctx, sSignature.RoutingKey, redis.Z{Score: sScore, Member: s})
				return nil
			},
		)
		return err
	}

	if err = b.rclient.Watch(context.Background(), watchFunc, lk); err != nil {
		return err
	}

	return nil
}

// 已弃用
// func (b *BrokerGR) RetryCompletedTask(t string, s string) error {
// 	signature, err := b.GetTaskInfo(t)
// 	if err != nil {
// 		return err
// 	}

// 	signature.UUID = s
// 	if err := b.Publish(context.Background(), signature); err != nil {
// 		log.ERROR.Print(err)
// 	}
// 	return nil
// }

func (b *BrokerGR) CancelPendingTask(s string) error {
	signature, err := b.GetTaskInfo(s)
	if err != nil {
		return err
	}
	b.Broker.AdjustRoutingKey(signature)

	// 拼接lock名称
	// lk := fmt.Sprintf("%s%s", defaultLockPrefix, s)
	lk := fmt.Sprintf("%s%s", defaultJobPrefix, s)

	watchFunc := func(tx *redis.Tx) error {
		// only return the first zrange value if there are no other changes in this key
		// to make sure a delayed task would only be consumed once
		// 判断lockKey是否存在
		ctx := context.Background()
		_, err := tx.Get(ctx, lk).Result()
		if err != nil {
			return err
		}
		_, err = tx.TxPipelined(
			ctx, func(pipe redis.Pipeliner) error {
				pipe.ZRem(ctx, signature.RoutingKey, signature.UUID)
				pipe.Del(ctx, lk)
				return nil
			},
		)
		return err
	}

	if err = b.rclient.Watch(context.Background(), watchFunc, lk); err != nil {
		return err
	}
	return nil
}

func (b *BrokerGR) GetTasks(ts ...string) ([]*tasks.Signature, error) {
	ctx := context.Background()
	pipe := b.rclient.TxPipeline()
	for _, t := range ts {
		pipe.Get(ctx, t)
	}
	res, err := pipe.Exec(ctx)
	if err != nil {
		fmt.Println(err)
	}
	l := make([]*tasks.Signature, 0)

	for i, r := range res {
		signature := new(tasks.Signature)
		if r.Err() != nil {
			signature.UUID = ts[i]
			l = append(l, signature)
			continue
		}
		decoder := json.NewDecoder(strings.NewReader(r.String()))
		decoder.UseNumber()
		if err := decoder.Decode(signature); err != nil {
			signature.UUID = ts[i]
			l = append(l, signature)
			continue
		}
		l = append(l, signature)
	}
	return l, nil
}

// 修改后的pending task
func (b *BrokerGR) GetPendingTasks(queue string) ([]*tasks.Signature, error) {
	if queue == "" {
		queue = b.GetConfig().DefaultQueue
	}

	results, err := b.rclient.ZRange(context.Background(), queue, 0, -1).Result()
	if err != nil {
		return nil, err
	}

	return b.GetTasks(results...)
}
