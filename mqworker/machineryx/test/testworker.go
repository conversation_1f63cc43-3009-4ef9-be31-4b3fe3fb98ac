package main

import (
	"context"
	"errors"
	"fmt"
	"os"
	"time"

	"github.com/google/uuid"
	"github.com/urfave/cli"

	// "github.com/RichardKnop/machinery/v2"
	"github.com/RichardKnop/machinery/v2/config"
	"github.com/RichardKnop/machinery/v2/log"
	"github.com/RichardKnop/machinery/v2/tasks"

	// redisbackend "github.com/RichardKnop/machinery/v2/backends/redis"
	// redisbroker "github.com/RichardKnop/machinery/v2/brokers/redis"
	exampletasks "github.com/RichardKnop/machinery/v2/example/tasks"
	// "github.com/RichardKnop/machinery/v2/example/tracers"
	eagerlock "github.com/RichardKnop/machinery/v2/locks/eager"
	"github.com/opentracing/opentracing-go"
	opentracinglog "github.com/opentracing/opentracing-go/log"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx"
	redisbackend "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx/backends/redis"
	redisbroker "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx/brokers/redis"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx/tracers"
)

var app *cli.App

func init() {
	// Initialize a CLI app
	app = cli.NewApp()
	app.Name = "machinery"
	app.Usage = "machinery worker and send example tasks with machinery send"
	app.Version = "0.0.0"
}

func main() {
	// Set the CLI app commands
	app.Commands = []cli.Command{
		{
			Name:  "worker",
			Usage: "launch machinery worker",
			Action: func(c *cli.Context) error {
				if err := worker(); err != nil {
					return cli.NewExitError(err.Error(), 1)
				}
				return nil
			},
		},
		{
			Name:  "send",
			Usage: "send example tasks ",
			Action: func(c *cli.Context) error {
				if err := send(); err != nil {
					return cli.NewExitError(err.Error(), 1)
				}
				return nil
			},
		},
	}

	// Run the CLI app
	_ = app.Run(os.Args)
}

func SumInts(numbers []int64) (int64, error) {
	time.Sleep(20 * time.Second)
	var sum int64
	for _, num := range numbers {
		sum += num
	}
	return sum, nil
}

func Add(args ...int64) (int64, error) {
	sum := int64(0)
	for _, arg := range args {
		sum += arg
	}
	return sum, nil
	// return 0, errors.New("ttttttttt")
}

func AddSuccessTask(sum int64) (string, error) {
	fmt.Println("lalalala")
	fmt.Println(sum)
	fmt.Println("lalalala")
	return "lalal", nil
}

func AddFailureTask(s string) (string, error) { // 只会接受到error的内容
	fmt.Println("lalalala2")
	fmt.Println(s)
	fmt.Println("lalalala2")
	return "lalal2", nil
}

func Add2(args ...int64) (int64, error) {
	// sum := int64(0)
	// for _, arg := range args {
	// 	sum += arg
	// }
	// return sum, nil
	return 0, errors.New("ttttttttt")
}

func Add2SuccessTask(s string, sum int64) (string, error) {
	fmt.Println("lalalala3")
	fmt.Println(s)
	fmt.Println(sum)
	fmt.Println("lalalala3")
	return "lalal", nil
}

func Add2FailureTask(s string, y float64) (string, error) { // 只会接受到error的内容
	fmt.Println("lalalala2")
	fmt.Println(s)
	fmt.Println(y)
	fmt.Println("lalalala2")
	return "lalal2", nil
}

func startServer() (*machineryx.Server, error) {
	cnf := &config.Config{
		DefaultQueue:    "TTTGOGOGO",
		ResultsExpireIn: 3600,
		Redis: &config.RedisConfig{
			MaxIdle:                3,
			IdleTimeout:            240,
			ReadTimeout:            15,
			WriteTimeout:           15,
			ConnectTimeout:         15,
			NormalTasksPollPeriod:  1000,
			DelayedTasksPollPeriod: 500,
		},
	}

	// Create server instance
	broker := redisbroker.NewGR(cnf, []string{"127.0.0.1:6379"}, 4)   // 创建一个redis的客户端
	backend := redisbackend.NewGR(cnf, []string{"127.0.0.1:6379"}, 4) // 同样是创建一个redis的客户端，和上面的区别是实现的方法有差异
	lock := eagerlock.New()                                           // 多读单写锁
	server := machineryx.NewServer(cnf, broker, backend, lock)        // 创建一个server

	// Register tasks
	tasksMap := map[string]any{
		"add":               Add,
		"multiply":          exampletasks.Multiply,
		"sum_ints":          SumInts,
		"sum_floats":        exampletasks.SumFloats,
		"concat":            exampletasks.Concat,
		"split":             exampletasks.Split,
		"panic_task":        exampletasks.PanicTask,
		"long_running_task": exampletasks.LongRunningTask,
		"add_success":       AddSuccessTask,
		"add_failure":       AddFailureTask,
		"add2":              Add2,
		"add2_success":      Add2SuccessTask,
		"add2_failure":      Add2FailureTask,
	}

	return server, server.RegisterTasks(tasksMap)
}

func worker() error {
	consumerTag := "cc66"

	cleanup, err := tracers.SetupTracer(consumerTag, "http://127.0.0.1:14268/api/traces")
	if err != nil {
		log.FATAL.Fatalln("Unable to instantiate a tracer:", err)
	}
	defer cleanup()

	server, err := startServer()
	if err != nil {
		return err
	}

	// var splitTask = tasks.Signature{
	// 	Name: "split",
	// 	Args: []tasks.Arg{
	// 		{
	// 			Type:  "string",
	// 			Value: "foo",
	// 		},
	// 	},
	// }

	// err = server.RegisterPeriodicTask("*/5 * * * ?", "nihao", &splitTask)
	// if err != nil {
	// 	return fmt.Errorf("Could not send task: %s", err.Error())
	// }

	// The second argument is a consumer tag
	// Ideally, each worker should have a unique tag (worker1, worker2 etc)
	worker := server.NewWorker(consumerTag, 1)

	// Here we inject some custom code for error handling,
	// start and end of task hooks, useful for metrics for example.
	errorHandler := func(err error) {
		log.ERROR.Println("I am an error handler:", err)
	}

	preTaskHandler := func(signature *tasks.Signature) {
		log.INFO.Println("I am a start of task handler for:", signature.Name)
	}

	postTaskHandler := func(signature *tasks.Signature) {
		log.INFO.Println("I am an end of task handler for:", signature.Name)
	}

	worker.SetPostTaskHandler(postTaskHandler)
	worker.SetErrorHandler(errorHandler)
	worker.SetPreTaskHandler(preTaskHandler)

	return worker.Launch()
}

func send() error {
	cleanup, err := tracers.SetupTracer("sender557", "http://127.0.0.1:14268/api/traces")
	fmt.Println("55555")
	if err != nil {
		log.FATAL.Fatalln("Unable to instantiate a tracer:", err)
	}
	defer cleanup()

	server, err := startServer()
	if err != nil {
		return err
	}

	var (
		addTask0, addTask1, addTask2 tasks.Signature
		// addTask0                                          tasks.Signature
		multiplyTask0, multiplyTask1                      tasks.Signature
		sumIntsTask, sumFloatsTask, concatTask, splitTask tasks.Signature
		panicTask                                         tasks.Signature
		longRunningTask                                   tasks.Signature
	)
	fmt.Println(addTask1, addTask2, multiplyTask0, multiplyTask1, panicTask, longRunningTask, splitTask)

	initTasks := func() {
		addTask0 = tasks.Signature{
			Name: "add",
			Args: []tasks.Arg{
				{
					Type:  "int64",
					Value: 1,
				},
				{
					Type:  "int64",
					Value: 1,
				},
			},
		}

		addTask1 = tasks.Signature{
			Name: "add",
			Args: []tasks.Arg{
				{
					Type:  "int64",
					Value: 2,
				},
				{
					Type:  "int64",
					Value: 2,
				},
			},
		}

		addTask2 = tasks.Signature{
			Name: "add",
			Args: []tasks.Arg{
				{
					Type:  "int64",
					Value: 5,
				},
				{
					Type:  "int64",
					Value: 6,
				},
			},
		}

		multiplyTask0 = tasks.Signature{
			Name: "multiply",
			Args: []tasks.Arg{
				{
					Type:  "int64",
					Value: 4,
				},
			},
		}

		multiplyTask1 = tasks.Signature{
			Name: "multiply",
		}

		sumIntsTask = tasks.Signature{
			Name: "sum_ints",
			Args: []tasks.Arg{
				{
					Type:  "[]int64",
					Value: []int64{1, 2},
				},
			},
		}

		sumFloatsTask = tasks.Signature{
			Name: "sum_floats",
			Args: []tasks.Arg{
				{
					Type:  "[]float64",
					Value: []float64{1.5, 2.7},
				},
			},
		}

		concatTask = tasks.Signature{
			Name: "concat",
			Args: []tasks.Arg{
				{
					Type:  "[]string",
					Value: []string{"foo", "bar"},
				},
			},
		}

		splitTask = tasks.Signature{
			Name: "split",
			Args: []tasks.Arg{
				{
					Type:  "string",
					Value: "foo",
				},
			},
		}

		panicTask = tasks.Signature{
			Name: "panic_task",
		}

		longRunningTask = tasks.Signature{
			Name: "long_running_task",
		}
	}

	/*
	 * Lets start a span representing this run of the `send` command and
	 * set a batch id as baggage so it can travel all the way into
	 * the worker functions.
	//  */
	// span, ctx := opentracing.StartSpanFromContext(context.Background(), "send")
	span, ctx := opentracing.StartSpanFromContext(context.Background(), "send22")

	fmt.Println(opentracing.GlobalTracer())
	fmt.Println(6666)
	defer span.Finish()

	batchID := uuid.New().String()
	// span.SetAttributes()
	span.SetBaggageItem("batch.id", batchID)
	span.LogFields(opentracinglog.String("batch.id", batchID))

	log.INFO.Println("Starting batch:", batchID)
	/*
	 * First, let's try sending a single task
	 */
	initTasks()
	addSuccessT := tasks.Signature{
		Name: "add_success",
		Args: []tasks.Arg{},
	}
	addFailureT := tasks.Signature{
		Name: "add_failure",
		Args: []tasks.Arg{},
	}
	addTask0.OnSuccess = make([]*tasks.Signature, 0)
	addTask0.OnSuccess = append(addTask0.OnSuccess, &addSuccessT)
	addTask0.OnError = make([]*tasks.Signature, 0)
	addTask0.OnError = append(addTask0.OnError, &addFailureT)

	addTask9 := tasks.Signature{
		Name: "add2",
		Args: []tasks.Arg{
			{
				Type:  "int64",
				Value: 1,
			},
			{
				Type:  "int64",
				Value: 1,
			},
		},
	}

	add2SuccessT := tasks.Signature{
		Name: "add2_success",
		Args: []tasks.Arg{
			{
				Type:  "string",
				Value: "999",
			},
		},
	}
	add2FailureT := tasks.Signature{
		Name: "add2_failure",
		Args: []tasks.Arg{
			{
				Type:  "float64",
				Value: 1,
			},
		},
	}
	addTask9.OnSuccess = make([]*tasks.Signature, 0)
	addTask9.OnSuccess = append(addTask9.OnSuccess, &add2SuccessT)
	addTask9.OnError = make([]*tasks.Signature, 0)
	addTask9.OnError = append(addTask9.OnError, &add2FailureT)

	log.INFO.Println("Single task:")

	asyncResult, err := server.SendTaskWithContext(ctx, &addTask0)
	if err != nil {
		return fmt.Errorf("Could not send task: %s", err.Error())
	}
	fmt.Println(asyncResult)

	asyncResult, err = server.SendTaskWithContext(ctx, &addTask9)
	if err != nil {
		return fmt.Errorf("Could not send task: %s", err.Error())
	}
	fmt.Println(asyncResult)

	// results, err := asyncResult.Get(time.Millisecond * 5)
	// if err != nil {
	// 	return fmt.Errorf("Getting task result failed with error: %s", err.Error())
	// }
	// log.INFO.Printf("1 + 1 = %v\n", tasks.HumanReadableResults(results))

	/*
	 * Try couple of tasks with a slice argument and slice return value
	 */
	asyncResult, err = server.SendTaskWithContext(ctx, &sumIntsTask)
	if err != nil {
		return fmt.Errorf("Could not send task: %s", err.Error())
	}
	fmt.Println(asyncResult)

	// results, err = asyncResult.Get(time.Millisecond * 5)
	// if err != nil {
	// 	return fmt.Errorf("Getting task result failed with error: %s", err.Error())
	// }
	// log.INFO.Printf("sum([1, 2]) = %v\n", tasks.HumanReadableResults(results))

	asyncResult, err = server.SendTaskWithContext(ctx, &sumFloatsTask)
	if err != nil {
		return fmt.Errorf("Could not send task: %s", err.Error())
	}
	fmt.Println(asyncResult)

	// results, err = asyncResult.Get(time.Millisecond * 5)
	// if err != nil {
	// 	return fmt.Errorf("Getting task result failed with error: %s", err.Error())
	// }
	// log.INFO.Printf("sum([1.5, 2.7]) = %v\n", tasks.HumanReadableResults(results))

	asyncResult, err = server.SendTaskWithContext(ctx, &concatTask)
	if err != nil {
		return fmt.Errorf("Could not send task: %s", err.Error())
	}
	fmt.Println(asyncResult)

	// results, err = asyncResult.Get(time.Millisecond * 5)
	// if err != nil {
	// 	return fmt.Errorf("Getting task result failed with error: %s", err.Error())
	// }
	// log.INFO.Printf("concat([\"foo\", \"bar\"]) = %v\n", tasks.HumanReadableResults(results))

	// asyncResult, err = server.SendTaskWithContext(ctx, &splitTask)
	// if err != nil {
	// 	return fmt.Errorf("Could not send task: %s", err.Error())
	// }

	// results, err = asyncResult.Get(time.Millisecond * 5)
	// if err != nil {
	// 	return fmt.Errorf("Getting task result failed with error: %s", err.Error())
	// }
	// log.INFO.Printf("split([\"foo\"]) = %v\n", tasks.HumanReadableResults(results))

	// err = server.RegisterPeriodicTask("*/5 * * * ?", "nihao", &splitTask)
	// if err != nil {
	// 	return fmt.Errorf("Could not send task: %s", err.Error())
	// }
	// time.Sleep(800 * time.Second)

	// results, err = asyncResult.Get(time.Millisecond * 5)
	// if err != nil {
	// 	return fmt.Errorf("Getting task result failed with error: %s", err.Error())
	// }
	// log.INFO.Printf("split([\"foo\"]) = %v\n", tasks.HumanReadableResults(results))

	/*
	 * Now let's explore ways of sending multiple tasks
	 */

	// Now let's try a parallel execution
	// initTasks()
	// log.INFO.Println("Group of tasks (parallel execution):")

	// group, err := tasks.NewGroup(&addTask0, &addTask1, &addTask2)
	// if err != nil {
	// 	return fmt.Errorf("Error creating group: %s", err.Error())
	// }

	// asyncResults, err := server.SendGroupWithContext(ctx, group, 10)
	// if err != nil {
	// 	return fmt.Errorf("Could not send group: %s", err.Error())
	// }

	// for _, asyncResult := range asyncResults {
	// 	results, err = asyncResult.Get(time.Millisecond * 5)
	// 	if err != nil {
	// 		return fmt.Errorf("Getting task result failed with error: %s", err.Error())
	// 	}
	// 	log.INFO.Printf(
	// 		"%v + %v = %v\n",
	// 		asyncResult.Signature.Args[0].Value,
	// 		asyncResult.Signature.Args[1].Value,
	// 		tasks.HumanReadableResults(results),
	// 	)
	// }

	// // Now let's try a group with a chord
	// initTasks()
	// log.INFO.Println("Group of tasks with a callback (chord):")

	// group, err = tasks.NewGroup(&addTask0, &addTask1, &addTask2)
	// if err != nil {
	// 	return fmt.Errorf("Error creating group: %s", err.Error())
	// }

	// chord, err := tasks.NewChord(group, &multiplyTask1)
	// if err != nil {
	// 	return fmt.Errorf("Error creating chord: %s", err)
	// }

	// chordAsyncResult, err := server.SendChordWithContext(ctx, chord, 10)
	// if err != nil {
	// 	return fmt.Errorf("Could not send chord: %s", err.Error())
	// }

	// results, err = chordAsyncResult.Get(time.Millisecond * 5)
	// if err != nil {
	// 	return fmt.Errorf("Getting chord result failed with error: %s", err.Error())
	// }
	// log.INFO.Printf("(1 + 1) * (2 + 2) * (5 + 6) = %v\n", tasks.HumanReadableResults(results))

	// // Now let's try chaining task results
	// initTasks()
	// log.INFO.Println("Chain of tasks:")

	// chain, err := tasks.NewChain(&addTask0, &addTask1, &addTask2, &multiplyTask0)
	// if err != nil {
	// 	return fmt.Errorf("Error creating chain: %s", err)
	// }

	// chainAsyncResult, err := server.SendChainWithContext(ctx, chain)
	// if err != nil {
	// 	return fmt.Errorf("Could not send chain: %s", err.Error())
	// }

	// results, err = chainAsyncResult.Get(time.Millisecond * 5)
	// if err != nil {
	// 	return fmt.Errorf("Getting chain result failed with error: %s", err.Error())
	// }
	// log.INFO.Printf("(((1 + 1) + (2 + 2)) + (5 + 6)) * 4 = %v\n", tasks.HumanReadableResults(results))

	// // Let's try a task which throws panic to make sure stack trace is not lost
	// initTasks()
	// asyncResult, err = server.SendTaskWithContext(ctx, &panicTask)
	// if err != nil {
	// 	return fmt.Errorf("Could not send task: %s", err.Error())
	// }

	// _, err = asyncResult.Get(time.Millisecond * 5)
	// if err == nil {
	// 	return errors.New("Error should not be nil if task panicked")
	// }
	// log.INFO.Printf("Task panicked and returned error = %v\n", err.Error())

	// // Let's try a long running task
	// initTasks()
	// asyncResult, err = server.SendTaskWithContext(ctx, &longRunningTask)
	// if err != nil {
	// 	return fmt.Errorf("Could not send task: %s", err.Error())
	// }

	// results, err = asyncResult.Get(time.Millisecond * 5)
	// if err != nil {
	// 	return fmt.Errorf("Getting long running task result failed with error: %s", err.Error())
	// }
	// log.INFO.Printf("Long running task returned = %v\n", tasks.HumanReadableResults(results))

	return nil
}
