package consumer

import (
	"fmt"
	"testing"
	"time"

	"github.com/RichardKnop/machinery/v2/tasks"
)

func TestConsumer(t *testing.T) {
	consumer := NewConsumer(
		ConsumerConfig{
			Broker:      "127.0.0.1:6379",
			Backend:     "127.0.0.1:6379",
			Queue:       "simple_test",
			ConsumerTag: "test_consumer",
			Db:          5,
		},
	)

	err := consumer.RegisterTasks(
		map[string]any{
			"simple": func(data string) (string, error) {
				fmt.Printf("data = %s\n", data)

				time.Sleep(10 * time.Second)
				fmt.Printf("finish ==============\n")

				return "success", nil
			},
		},
	)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("err: %s", err)
		t.<PERSON>ailNow()
	}

	err = consumer.start()
	if err != nil {
		t.<PERSON>rrorf("start err: %s", err)
		t.<PERSON>ail<PERSON>ow()
	}
}

func TestBeat(t *testing.T) {
	consumer := NewConsumer(
		ConsumerConfig{
			Broker:      "127.0.0.1:6379",
			Backend:     "127.0.0.1:6379",
			Queue:       "simple_test",
			ConsumerTag: "test_consumer",
			Db:          5,
		},
	)

	err := consumer.RegisterTasks(
		map[string]any{
			"simple": func(data string) (string, error) {
				fmt.Printf("data = %s\n", data)
				fmt.Printf("finish ==============\n")

				return "success", nil
			},
			"beat": func(cron, value string) (string, error) {
				fmt.Println("hello beat ...")

				_, err := consumer.RegisterPeriodicTask(
					cron, "beat", &tasks.Signature{
						Name: "simple",
						Args: []tasks.Arg{
							{Value: value, Type: "string"},
						},
					},
				)
				if err != nil {
					fmt.Println("RegisterPeriodicTask err: ", err)
					t.FailNow()
				}
				fmt.Println("finish beat ===")
				return "success", nil
			},
		},
	)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	err = consumer.start()
	if err != nil {
		t.Errorf("start err: %s", err)
		t.FailNow()
	}
}
