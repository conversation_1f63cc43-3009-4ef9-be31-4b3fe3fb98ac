package consumer

import (
	"fmt"

	"github.com/RichardKnop/machinery/v2/config"
	"github.com/RichardKnop/machinery/v2/example/tracers"
	"github.com/RichardKnop/machinery/v2/tasks"
	"github.com/robfig/cron/v3"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/lock"
	machinery "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx"

	redisbackend "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx/backends/redis"
	redisbroker "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx/brokers/redis"
)

type ConsumerConfig struct {
	Broker      string
	Backend     string
	Queue       string
	ConsumerTag string
	Db          int
	MaxWorker   int // 最大并发数, 当等于0时并发数为cpu数*2
}

type Consumer struct {
	conf    ConsumerConfig
	mserver *machinery.Server
	cleaner func()
}

func NewConsumer(conf ConsumerConfig) *Consumer {
	consumer := &Consumer{
		conf: conf,
	}

	var err error
	consumer.cleaner, err = tracers.SetupTracer(conf.ConsumerTag)
	if err != nil {
		fmt.Println("Unable to instantiate a tracer:", err)
	}

	// TODO: 配置化
	redisConf := &config.Config{
		DefaultQueue:    conf.Queue,
		ResultsExpireIn: 3600,
		Redis: &config.RedisConfig{
			MaxIdle:                3,
			IdleTimeout:            240,
			ReadTimeout:            15,
			WriteTimeout:           15,
			ConnectTimeout:         15,
			NormalTasksPollPeriod:  1000,
			DelayedTasksPollPeriod: 500,
		},
	}

	broker := redisbroker.NewGR(redisConf, []string{conf.Broker}, conf.Db)
	backend := redisbackend.NewGR(redisConf, []string{conf.Backend}, conf.Db)

	lo := lock.New()                                                       // 多读单写锁
	consumer.mserver = machinery.NewServer(redisConf, broker, backend, lo) // 创建一个server

	return consumer
}

func (c *Consumer) RegisterTasks(tasks map[string]any) error {
	return c.mserver.RegisterTasks(tasks)
}

func (c *Consumer) RegisterPeriodicTask(spec, name string, signature *tasks.Signature) (id cron.EntryID, err error) {
	return c.mserver.RegisterPeriodicTaskX(spec, name, signature)
}

func (c *Consumer) RemovePeriodicTask(id cron.EntryID) {
	c.mserver.RemovePeriodicTaskX(id)
}

func (c *Consumer) GetPeriodicTask(id cron.EntryID) cron.Entry {
	return c.mserver.GetPeriodicTaskX(id)
}

func (c *Consumer) GetALLPeriodicTask() []cron.Entry {
	return c.mserver.GetALLPeriodicTaskX()
}

func (c *Consumer) Start() {
	err := c.start()
	if err != nil {
		logx.Error(err)
	}
}

func (c *Consumer) Stop() {
	logx.Info("Stopping consumer server ...")
	c.cleaner()
}

func (c *Consumer) start() error {
	worker := c.mserver.NewWorker(c.conf.ConsumerTag, c.conf.MaxWorker)
	return worker.Launch()
}

//func handleError(err error) {
//	if err == nil {
//		return
//	}
//
//	logx.Error(err)
//	panic(any(err))
//}
