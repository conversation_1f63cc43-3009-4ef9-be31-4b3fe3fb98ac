package producer

import (
	"context"
	"fmt"

	"github.com/opentracing/opentracing-go"
	opentracinglog "github.com/opentracing/opentracing-go/log"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/lock"
	machinery "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx"
	redisbackend "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx/backends/redis"
	redisbroker "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx/brokers/redis"

	// "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/machineryx/tracers" // 暂时不用

	"github.com/RichardKnop/machinery/v2/backends/result"
	"github.com/RichardKnop/machinery/v2/config"
	"github.com/RichardKnop/machinery/v2/log"
	"github.com/RichardKnop/machinery/v2/tasks"
	"github.com/google/uuid"

	// "github.com/opentracing/opentracing-go"  // 暂时不用
	// opentracinglog "github.com/opentracing/opentracing-go/log"  // 暂时不用
	"github.com/zeromicro/go-zero/core/trace"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	oteltrace "go.opentelemetry.io/otel/trace"
)

type ProducerConfig struct {
	Broker  string
	Backend string
	Queue   string
	Db      int
	// Name     string
	// Endpoint string
}

var producerAttributeKey = attribute.Key("batchID")

func startSpan(ctx context.Context, batchID, spanName string) (context.Context, oteltrace.Span) {
	tracer := otel.GetTracerProvider().Tracer(trace.TraceName)
	start, span := tracer.Start(ctx,
		spanName,
		oteltrace.WithSpanKind(oteltrace.SpanKindClient),
	)
	span.SetAttributes(producerAttributeKey.String(batchID))

	return start, span
}

func endSpan(span oteltrace.Span, err error) {
	defer span.End()

	if err == nil {
		span.SetStatus(codes.Ok, "")
		return
	}

	span.SetStatus(codes.Error, err.Error())
	span.RecordError(err)
}

type Producer struct {
	svr *machinery.Server
}

func NewProducer(conf ProducerConfig) *Producer {
	return &Producer{
		svr: StartServer(conf),
	}
}

func (prod *Producer) AsyncPush(ctx context.Context, task *tasks.Signature, operationName string) (*result.AsyncResult, error) {
	span, ctx := opentracing.StartSpanFromContext(ctx, operationName)
	defer span.Finish()

	batchID := uuid.New().String()
	span.SetBaggageItem("batch.id", batchID)
	span.LogFields(opentracinglog.String("batch.id", batchID))

	asyncResult, err := prod.svr.SendTaskWithContext(ctx, task)
	if err != nil {
		return nil, err
	}
	return asyncResult, nil
}

func StartServer(conf ProducerConfig) *machinery.Server {
	// cleanup, err := tracers.SetupTracer(conf.Name, conf.Endpoint)  // 暂时不用
	// if err != nil {
	// 	log.FATAL.Fatalln("Unable to instantiate a tracer:", err)
	// }
	// defer cleanup()

	cnf := &config.Config{
		DefaultQueue:    conf.Queue,
		ResultsExpireIn: 3600,
		Redis: &config.RedisConfig{
			MaxIdle:                3,
			IdleTimeout:            240,
			ReadTimeout:            15,
			WriteTimeout:           15,
			ConnectTimeout:         15,
			NormalTasksPollPeriod:  1000,
			DelayedTasksPollPeriod: 500,
		},
	}

	// Create server instance
	broker := redisbroker.NewGR(cnf, []string{conf.Broker}, conf.Db)    // 创建一个redis的客户端
	backend := redisbackend.NewGR(cnf, []string{conf.Backend}, conf.Db) // 同样是创建一个redis的客户端，和上面的区别是实现的方法有差异
	lo := lock.New()                                                    // 多读单写锁
	server := machinery.NewServer(cnf, broker, backend, lo)             // 创建一个server

	return server
}

func InitSimpleTask(name, executeId, routingKey string, args []tasks.Arg) tasks.Signature {
	return tasks.Signature{
		UUID:       executeId,
		Name:       name,
		Args:       args,
		RoutingKey: routingKey,
	}
}

func SendTaskWithContext(server *machinery.Server, task tasks.Signature, tag string) (asyncResult *result.AsyncResult, err error) {
	batchID := uuid.New().String()
	ctx, span := startSpan(context.Background(), batchID, tag)
	defer func() {
		endSpan(span, err)
	}()
	log.INFO.Println("Starting batch:", batchID)
	asyncResult, err = server.SendTaskWithContext(ctx, &task)
	if err != nil {
		return nil, fmt.Errorf("Could not send task: %s", err.Error())
	}
	return asyncResult, nil
}

// 暂时不使用该方法，重新使用新的trace
// func SendTaskWithContext(server *machinery.Server, task tasks.Signature, tag string) (*result.AsyncResult, error) {
// 	span, ctx := opentracing.StartSpanFromContext(context.Background(), tag)
// 	defer span.Finish()

// 	batchID := uuid.New().String()
// 	span.SetBaggageItem("batch.id", batchID)
// 	span.LogFields(opentracinglog.String("batch.id", batchID))

// 	log.INFO.Println("Starting batch:", batchID)
// 	asyncResult, err := server.SendTaskWithContext(ctx, &task)
// 	if err != nil {
// 		return nil, fmt.Errorf("Could not send task: %s", err.Error())
// 	}
// 	return asyncResult, nil

// }
