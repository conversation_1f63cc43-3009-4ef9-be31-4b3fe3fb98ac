package producer

import (
	"context"
	"math/rand"
	"strconv"
	"testing"

	"github.com/RichardKnop/machinery/v2/tasks"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

func TestSendMessage(t *testing.T) {
	producer := NewProducer(ProducerConfig{
		Broker:  "127.0.0.1:6379",
		Backend: "127.0.0.1:6379",
		Queue:   "simple_test",
		Db:      5,
	})

	task := tasks.Signature{
		Name: "simple",
		UUID: utils.GenNanoId("task_id:"),
		Args: []tasks.Arg{
			{Value: "rand Number:" + strconv.Itoa(rand.Intn(100)), Type: "string"},
		},
		OnSuccess: []*tasks.Signature{
			{
				Name:       "callback",
				RoutingKey: "callback_test",
			},
		},
	}

	_, err := producer.AsyncPush(context.Background(), &task, "test_productor")
	if err != nil {
		t.<PERSON><PERSON><PERSON>("err: %s", err)
		t.<PERSON><PERSON><PERSON>()
	}
}

func TestSendBeat(t *testing.T) {
	producer := NewProducer(ProducerConfig{
		Broker:  "127.0.0.1:6379",
		Backend: "127.0.0.1:6379",
		Queue:   "simple_test",
		Db:      5,
	})

	task := tasks.Signature{
		Name: "beat",
		Args: []tasks.Arg{
			{Value: "*/1 * * * *", Type: "string"},
			{Value: "hello world", Type: "string"},
		},
	}

	_, err := producer.AsyncPush(context.Background(), &task, "test_productor")
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}
}
