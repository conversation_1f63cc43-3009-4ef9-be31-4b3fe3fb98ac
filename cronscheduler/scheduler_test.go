package cronscheduler

import (
	"testing"
	"time"

	"github.com/robfig/cron/v3"
)

func TestScheduler_Cron(t *testing.T) {
	testCases := []struct {
		description string
		cronTab     string
	}{
		// https://crontab.guru/
		{description: "every minute */1", cronTab: "*/1 * * * *"},
		{description: "every minute 0/1", cronTab: "0/1 * * * *"},
		{description: "every two minute", cronTab: "0/2 * * * ?"},
		{description: "every day 23:30", cronTab: "30 23 * * *"},
		{description: "every day 23:30", cronTab: "30 23 ? * *"},
		{description: "every weekday 6:00,12:00,19:00", cronTab: "0 6,12,19 * * MON-FRI"},
	}

	for _, tc := range testCases {
		t.Run(
			tc.description, func(t *testing.T) {
				s, err := cron.ParseStandard(tc.cronTab)
				if err != nil {
					t.Fatal(err)
				}

				tt := time.Now()
				for i := 0; i < 10; i++ {
					tt = s.Next(tt)
					t.Logf("%s", tt.Format("2006-01-02 15:04:05.000"))
				}
			},
		)
	}
}
