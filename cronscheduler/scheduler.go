package cronscheduler

import (
	"sync"
	"time"

	"github.com/go-co-op/gocron"
	"github.com/zeromicro/go-zero/core/logx"
)

type Scheduler struct {
	*gocron.Scheduler

	startOnce sync.Once
	stopOnce  sync.Once
}

func NewScheduler() *Scheduler {
	s := &Scheduler{
		Scheduler: gocron.NewScheduler(time.UTC),
	}
	return s
}

// RegisterTasks 注册定时任务
// tasks.key: crontab语法
// tasks.value: 任务
func (s *Scheduler) RegisterTasks(tasks map[string]func()) error {
	for crontab, task := range tasks {
		_, err := s.Cron(crontab).Do(task)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *Scheduler) Start() {
	s.startOnce.Do(
		func() {
			logx.Infof("Starting scheduler server ...")
			s.StartAsync()
		},
	)
}

func (s *Scheduler) Stop() {
	s.stopOnce.Do(
		func() {
			logx.Infof("Stopping scheduler server ...")
			s.Scheduler.Stop()
		},
	)
}
