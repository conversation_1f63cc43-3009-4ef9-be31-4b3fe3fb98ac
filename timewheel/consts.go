package timewheel

import "time"

type taskID int64

const (
	modeIsCircle  = true
	modeNotCircle = false

	modeIsAsync  = true
	modeNotAsync = false

	tickQueueSize = 10
	bucketCount   = 128

	tickDurationOneMillisecond     = time.Millisecond
	tickDurationTenMillisecond     = 10 * time.Millisecond
	tickDurationHundredMillisecond = 100 * time.Millisecond
	tickDurationOneSecond          = time.Second
)
