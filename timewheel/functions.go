package timewheel

import (
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/lang"
)

var (
	defaultOneMillisecondTimeWheel, _     = NewTimeWheel(tickDurationOneMillisecond, bucketCount)
	defaultTenMillisecondTimeWheel, _     = NewTimeWheel(tickDurationTenMillisecond, bucketCount)
	defaultHundredMillisecondTimeWheel, _ = NewTimeWheel(tickDurationHundredMillisecond, bucketCount)
	defaultOneSecondTimeWheel, _          = NewTimeWheel(tickDurationOneSecond, bucketCount)
	mutex                                 sync.Mutex
)

func getTimeWheelByDelay(delay time.Duration) (tw *TimeWheel) {
	mutex.Lock()
	defer mutex.Unlock()

	defer func() {
		if tw != nil {
			tw.Start()
		}
	}()

	switch {
	case delay >= time.Millisecond && delay < 10*time.Millisecond:
		tw = defaultOneMillisecondTimeWheel
	case delay >= 10*time.Millisecond && delay < 100*time.Millisecond:
		tw = defaultTenMillisecondTimeWheel
	case delay >= 100*time.Millisecond && delay < time.Second:
		tw = defaultHundredMillisecondTimeWheel
	default:
		tw = defaultOneSecondTimeWheel
	}

	return
}

func ResetDefaultTimeWheel(tw *TimeWheel) {
	mutex.Lock()
	defer mutex.Unlock()

	switch {
	case tw.tick >= time.Millisecond && tw.tick < 10*time.Millisecond:
		defaultOneMillisecondTimeWheel = tw
	case tw.tick >= 10*time.Millisecond && tw.tick < 100*time.Millisecond:
		defaultTenMillisecondTimeWheel = tw
	case tw.tick >= 100*time.Millisecond && tw.tick < time.Second:
		defaultHundredMillisecondTimeWheel = tw
	default:
		defaultOneSecondTimeWheel = tw
	}
	tw.Start()
}

func Add(delay time.Duration, callback func()) *Task {
	tw := getTimeWheelByDelay(delay)
	return tw.Add(delay, callback)
}

func AddCron(delay time.Duration, callback func()) *Task {
	tw := getTimeWheelByDelay(delay)
	return tw.AddCron(delay, callback)
}

func Remove(task *Task) {
	tw := getTimeWheelByDelay(task.delay)
	tw.Remove(task)
}

func NewTimer(delay time.Duration) *Timer {
	tw := getTimeWheelByDelay(delay)
	return tw.NewTimer(delay)
}

func NewTicker(delay time.Duration) *Ticker {
	tw := getTimeWheelByDelay(delay)
	return tw.NewTicker(delay)
}

func AfterFunc(delay time.Duration, callback func()) *Timer {
	tw := getTimeWheelByDelay(delay)
	return tw.AfterFunc(delay, callback)
}

func After(delay time.Duration) <-chan time.Time {
	tw := getTimeWheelByDelay(delay)
	return tw.After(delay)
}

func Sleep(delay time.Duration) {
	tw := getTimeWheelByDelay(delay)
	tw.Sleep(delay)
}

func notifyChannel(q chan lang.PlaceholderType) {
	select {
	case q <- lang.Placeholder:
	default:
	}
}
