package timewheel

import (
	"context"
	"sync"
	"sync/atomic"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
)

var (
	errorTickInvalid         = errors.New("invalid params, must tick >= 1 ms")
	errorBucketNumberInvalid = errors.New("invalid params, must bucketsNum > 0")
)

type Option func(*TimeWheel)

// TickSafeMode set to tick safe mode
func TickSafeMode() Option {
	return func(tw *TimeWheel) {
		tw.tickQueue = make(chan time.Time, tickQueueSize)
	}
}

// SyncPoolMode set to sync pool mode
func SyncPoolMode() Option {
	return func(tw *TimeWheel) {
		tw.syncPool = true
	}
}

type TimeWheel struct {
	randomID int64

	tick      time.Duration
	ticker    *time.Ticker
	tickQueue chan time.Time

	bucketsNum    int
	buckets       []map[taskID]*Task // key: added item, value: *Task
	bucketIndexes map[taskID]int     // key: added item, value: bucket position

	currentIndex int

	onceStart sync.Once

	stopC chan lang.PlaceholderType

	syncPool bool
	exited   bool

	sync.RWMutex
}

// NewTimeWheel create new time wheel
func NewTimeWheel(tick time.Duration, bucketsNum int, options ...Option) (*TimeWheel, error) {
	if tick.Milliseconds() < 1 {
		return nil, errorTickInvalid
	}
	if bucketsNum <= 0 {
		return nil, errorBucketNumberInvalid
	}

	tw := &TimeWheel{
		// tick
		tick:      tick,
		tickQueue: make(chan time.Time, 10),

		// store
		bucketsNum:    bucketsNum,
		bucketIndexes: make(map[taskID]int, 1024*100),
		buckets:       make([]map[taskID]*Task, bucketsNum),
		currentIndex:  0,

		// signal
		stopC: make(chan lang.PlaceholderType),
	}

	for i := 0; i < bucketsNum; i++ {
		tw.buckets[i] = make(map[taskID]*Task, 16)
	}

	for _, option := range options {
		option(tw)
	}

	return tw, nil
}

// Start the time wheel
func (tw *TimeWheel) Start() {
	// only once start
	tw.onceStart.Do(
		func() {
			tw.ticker = time.NewTicker(tw.tick)
			go tw.scheduler()
			go tw.tickGenerator()
		},
	)
}

// Stop the time wheel
func (tw *TimeWheel) Stop() {
	tw.stopC <- lang.Placeholder
}

func (tw *TimeWheel) scheduler() {
	queue := tw.ticker.C
	if tw.tickQueue == nil {
		queue = tw.tickQueue
	}

	for {
		select {
		case <-queue:
			tw.handleTick()

		case <-tw.stopC:
			tw.exited = true
			tw.ticker.Stop()
			return
		}
	}
}

func (tw *TimeWheel) tickGenerator() {
	if tw.tickQueue != nil {
		return
	}

	for !tw.exited {
		if <-tw.ticker.C; true {
			select {
			case tw.tickQueue <- time.Now():
			default:
				panic("raise long time blocking")
			}
		}
	}
}

func (tw *TimeWheel) collectTask(task *Task) {
	index := tw.bucketIndexes[task.id]
	delete(tw.bucketIndexes, task.id)
	delete(tw.buckets[index], task.id)

	if tw.syncPool {
		defaultTaskPool.put(task)
	}
}

func (tw *TimeWheel) handleTick() {
	tw.Lock()
	defer tw.Unlock()

	bucket := tw.buckets[tw.currentIndex]
	for k, task := range bucket {
		if task.stop {
			tw.collectTask(task)
			continue
		}

		if bucket[k].round > 0 {
			bucket[k].round--
			continue
		}

		if task.async {
			go task.callback()
		} else {
			task.callback()
		}

		// circle
		if task.circle {
			tw.collectTask(task)
			tw.putCircle(task, modeIsCircle)
			continue
		}

		// gc
		tw.collectTask(task)
	}

	if tw.currentIndex == tw.bucketsNum-1 {
		tw.currentIndex = 0
		return
	}

	tw.currentIndex++
}

// Add a task
func (tw *TimeWheel) Add(delay time.Duration, callback func()) *Task {
	return tw.addAny(delay, callback, modeNotCircle, modeIsAsync)
}

// AddCron add a interval task
func (tw *TimeWheel) AddCron(delay time.Duration, callback func()) *Task {
	return tw.addAny(delay, callback, modeIsCircle, modeIsAsync)
}

func (tw *TimeWheel) addAny(delay time.Duration, callback func(), circle, async bool) *Task {
	if delay <= 0 {
		delay = tw.tick
	}

	id := tw.genUniqueID()

	var task *Task
	if tw.syncPool {
		task = defaultTaskPool.get()
	} else {
		task = &Task{}
	}

	task.delay = delay
	task.id = id
	task.callback = callback
	task.circle = circle
	task.async = async // refer to src/runtime/time.go

	tw.put(task)
	return task
}

func (tw *TimeWheel) put(task *Task) {
	tw.Lock()
	defer tw.Unlock()

	tw.store(task, false)
}

func (tw *TimeWheel) putCircle(task *Task, circleMode bool) {
	tw.store(task, circleMode)
}

func (tw *TimeWheel) store(task *Task, circleMode bool) {
	round := tw.calculateRound(task.delay)
	index := tw.calculateIndex(task.delay)

	if round > 0 && circleMode {
		task.round = round - 1
	} else {
		task.round = round
	}

	tw.bucketIndexes[task.id] = index
	tw.buckets[index][task.id] = task
}

func (tw *TimeWheel) calculateRound(delay time.Duration) (round int) {
	delaySeconds := delay.Seconds()
	tickSeconds := tw.tick.Seconds()
	round = int(delaySeconds / tickSeconds / float64(tw.bucketsNum))
	return
}

func (tw *TimeWheel) calculateIndex(delay time.Duration) (index int) {
	delaySeconds := delay.Seconds()
	tickSeconds := tw.tick.Seconds()
	index = (int(float64(tw.currentIndex) + delaySeconds/tickSeconds)) % tw.bucketsNum
	return
}

// Remove a task
func (tw *TimeWheel) Remove(task *Task) {
	// tw.removeC <- task
	tw.remove(task)
}

func (tw *TimeWheel) remove(task *Task) {
	tw.Lock()
	defer tw.Unlock()

	tw.collectTask(task)
}

// NewTimer returns a timer with specified duration
func (tw *TimeWheel) NewTimer(delay time.Duration) *Timer {
	queue := make(chan lang.PlaceholderType, 1)
	task := tw.addAny(delay, func() { notifyChannel(queue) }, modeNotCircle, modeNotAsync)

	ctx, cancel := context.WithCancel(context.Background())
	return &Timer{
		tw:     tw,
		task:   task,
		C:      queue,
		Ctx:    ctx,
		cancel: cancel,
	}
}

func (tw *TimeWheel) AfterFunc(delay time.Duration, callback func()) *Timer {
	queue := make(chan lang.PlaceholderType, 1)
	fn := func() {
		callback()
		notifyChannel(queue)
	}
	task := tw.addAny(delay, fn, modeNotCircle, modeIsAsync)

	ctx, cancel := context.WithCancel(context.Background())
	return &Timer{
		tw:     tw,
		C:      queue,
		task:   task,
		Ctx:    ctx,
		cancel: cancel,
		fn:     callback,
	}
}

// NewTicker returns a ticker with specified duration
func (tw *TimeWheel) NewTicker(delay time.Duration) *Ticker {
	queue := make(chan lang.PlaceholderType, 1)
	task := tw.addAny(delay, func() { notifyChannel(queue) }, modeIsCircle, modeNotAsync)

	ctx, cancel := context.WithCancel(context.Background())
	return &Ticker{
		tw:     tw,
		task:   task,
		C:      queue,
		Ctx:    ctx,
		cancel: cancel,
	}
}

func (tw *TimeWheel) After(delay time.Duration) <-chan time.Time {
	queue := make(chan time.Time, 1)
	fn := func() {
		queue <- time.Now()
	}
	tw.addAny(delay, fn, modeNotCircle, modeNotAsync)
	return queue
}

func (tw *TimeWheel) Sleep(delay time.Duration) {
	queue := make(chan lang.PlaceholderType, 1)
	fn := func() {
		queue <- lang.Placeholder
	}
	tw.addAny(delay, fn, modeNotCircle, modeNotAsync)
	<-queue
}

func (tw *TimeWheel) genUniqueID() taskID {
	id := atomic.AddInt64(&tw.randomID, 1)
	return taskID(id)
}
