package timewheel

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/lang"
)

type Timer struct {
	tw   *TimeWheel
	task *Task

	fn     func() // external custom func
	stopFn func() // call function when timer stop

	C chan lang.PlaceholderType

	Ctx    context.Context
	cancel context.CancelFunc
}

func (t *Timer) Reset(delay time.Duration) {
	// first stop old task
	t.task.stop = true

	// make new task
	var task *Task
	if t.fn != nil {
		task = t.tw.addAny(delay,
			func() {
				t.fn()
				notifyChannel(t.C)
			},
			modeNotCircle,
			modeIsAsync, // must async mode
		)
	} else {
		task = t.tw.addAny(delay,
			func() {
				notifyChannel(t.C)
			},
			modeNotCircle,
			modeNotAsync,
		)
	}

	t.task = task
}

func (t *Timer) Stop() {
	if t.stopFn != nil {
		t.stopFn()
	}

	t.task.stop = true
	t.cancel()
	t.tw.Remove(t.task)
}

func (t *Timer) AddStopFunc(callback func()) {
	t.stopFn = callback
}
