package version

import (
	"html/template"
	"runtime"
	"strings"
	"text/tabwriter"
)

// Default build-time variable.
// These values are overridden via ldflags
var (
	Version        = ""
	GitBranch      = ""
	GitTag         = ""
	GitCommit      = ""
	BuildDatetime  = ""
	BuildGoVersion = ""

	versionTemplate = `The Service's Version Information is as follow:
 Version:	{{ .Version }}
 Git Branch:	{{ .GitBranch }}
 Git Tag:	{{ .GitTag }}
 Git Commit:	{{ .GitCommit }}
 Build Datetime:	{{ .BuildDatetime }}
 Build Go Version:	{{ .BuildGoVersion }}
 Runtime Go Version:	{{ .RuntimeGoVersion }}
 Compiler:	{{ .Compiler }}
 OS/Arch:	{{ .Os }}/{{ .Arch }}
`
	tpl, _ = template.New("version").Parse(versionTemplate)
)

type Info struct {
	Version          string `json:"version"`
	GitBranch        string `json:"git_branch"`
	GitTag           string `json:"git_tag"`
	GitCommit        string `json:"git_commit"`
	BuildDatetime    string `json:"build_datetime"`
	BuildGoVersion   string `json:"build_go_version"`
	RuntimeGoVersion string `json:"runtime_go_version"`
	Compiler         string `json:"compiler"`
	Os               string `json:"os"`
	Arch             string `json:"arch"`
}

func NewVersionInfo() *Info {
	return &Info{
		Version:          Version,
		GitBranch:        GitBranch,
		GitTag:           GitTag,
		GitCommit:        GitCommit,
		BuildDatetime:    BuildDatetime,
		BuildGoVersion:   BuildGoVersion,
		RuntimeGoVersion: runtime.Version(),
		Compiler:         runtime.Compiler,
		Os:               runtime.GOOS,
		Arch:             runtime.GOARCH,
	}
}

func (i *Info) String() string {
	var b strings.Builder

	w := tabwriter.NewWriter(&b, 20, 1, 1, ' ', 0)

	_ = tpl.Execute(w, i)
	_ = w.Flush()

	return b.String()
}
