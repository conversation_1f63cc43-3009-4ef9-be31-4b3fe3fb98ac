package errorx

// GRPC Error Code (1 - 16)
const (
	// GrpcCanceled indicates the operation was canceled (typically by the caller).
	//
	// The gRPC framework will generate this error code when cancellation
	// is requested.
	GrpcCanceled Code = 1

	// GrpcUnknown error. An example of where this error may be returned is
	// if a Status value received from another address space belongs to
	// an error-space that is not known in this address space. Also
	// errors raised by APIs that do not return enough error information
	// may be converted to this error.
	//
	// The gRPC framework will generate this error code in the above two
	// mentioned cases.
	GrpcUnknown Code = 2

	// GrpcInvalidArgument indicates client specified an invalid argument.
	// Note that this differs from FailedPrecondition. It indicates arguments
	// that are problematic regardless of the state of the system
	// (e.g., a malformed file name).
	//
	// This error code will not be generated by the gRPC framework.
	GrpcInvalidArgument Code = 3

	// GrpcDeadlineExceeded means operation expired before completion.
	// For operations that change the state of the system, this error may be
	// returned even if the operation has completed successfully. For
	// example, a successful response from a server could have been delayed
	// long enough for the deadline to expire.
	//
	// The gRPC framework will generate this error code when the deadline is
	// exceeded.
	GrpcDeadlineExceeded Code = 4

	// GrpcNotFound means some requested entity (e.g., file or directory) was
	// not found.
	//
	// This error code will not be generated by the gRPC framework.
	GrpcNotFound Code = 5

	// GrpcAlreadyExists means an attempt to create an entity failed because one
	// already exists.
	//
	// This error code will not be generated by the gRPC framework.
	GrpcAlreadyExists Code = 6

	// GrpcPermissionDenied indicates the caller does not have permission to
	// execute the specified operation. It must not be used for rejections
	// caused by exhausting some resource (use ResourceExhausted
	// instead for those errors). It must not be
	// used if the caller cannot be identified (use Unauthenticated
	// instead for those errors).
	//
	// This error code will not be generated by the gRPC core framework,
	// but expect authentication middleware to use it.
	GrpcPermissionDenied Code = 7

	// GrpcResourceExhausted indicates some resource has been exhausted, perhaps
	// a per-user quota, or perhaps the entire file system is out of space.
	//
	// This error code will be generated by the gRPC framework in
	// out-of-memory and server overload situations, or when a message is
	// larger than the configured maximum size.
	GrpcResourceExhausted Code = 8

	// GrpcFailedPrecondition indicates operation was rejected because the
	// system is not in a state required for the operation's execution.
	// For example, directory to be deleted may be non-empty, an rmdir
	// operation is applied to a non-directory, etc.
	//
	// A litmus test that may help a service implementor in deciding
	// between FailedPrecondition, Aborted, and Unavailable:
	//  (a) Use Unavailable if the client can retry just the failing call.
	//  (b) Use Aborted if the client should retry at a higher-level
	//      (e.g., restarting a read-modify-write sequence).
	//  (c) Use FailedPrecondition if the client should not retry until
	//      the system state has been explicitly fixed. E.g., if an "rmdir"
	//      fails because the directory is non-empty, FailedPrecondition
	//      should be returned since the client should not retry unless
	//      they have first fixed up the directory by deleting files from it.
	//  (d) Use FailedPrecondition if the client performs conditional
	//      REST Get/Update/Delete on a resource and the resource on the
	//      server does not match the condition. E.g., conflicting
	//      read-modify-write on the same resource.
	//
	// This error code will not be generated by the gRPC framework.
	GrpcFailedPrecondition Code = 9

	// GrpcAborted indicates the operation was aborted, typically due to a
	// concurrency issue like sequencer check failures, transaction aborts,
	// etc.
	//
	// See litmus test above for deciding between FailedPrecondition,
	// Aborted, and Unavailable.
	//
	// This error code will not be generated by the gRPC framework.
	GrpcAborted Code = 10

	// GrpcOutOfRange means operation was attempted past the valid range.
	// E.g., seeking or reading past end of file.
	//
	// Unlike InvalidArgument, this error indicates a problem that may
	// be fixed if the system state changes. For example, a 32-bit file
	// system will generate InvalidArgument if asked to read at an
	// offset that is not in the range [0,2^32-1], but it will generate
	// OutOfRange if asked to read from an offset past the current
	// file size.
	//
	// There is a fair bit of overlap between FailedPrecondition and
	// OutOfRange. We recommend using OutOfRange (the more specific
	// error) when it applies so that callers who are iterating through
	// a space can easily look for an OutOfRange error to detect when
	// they are done.
	//
	// This error code will not be generated by the gRPC framework.
	GrpcOutOfRange Code = 11

	// GrpcUnimplemented indicates operation is not implemented or not
	// supported/enabled in this service.
	//
	// This error code will be generated by the gRPC framework. Most
	// commonly, you will see this error code when a method implementation
	// is missing on the server. It can also be generated for unknown
	// compression algorithms or a disagreement as to whether an RPC should
	// be streaming.
	GrpcUnimplemented Code = 12

	// GrpcInternal errors. Means some invariants expected by underlying
	// system has been broken. If you see one of these errors,
	// something is very broken.
	//
	// This error code will be generated by the gRPC framework in several
	// internal error conditions.
	GrpcInternal Code = 13

	// GrpcUnavailable indicates the service is currently unavailable.
	// This is a most likely a transient condition and may be corrected
	// by retrying with a backoff. Note that it is not always safe to retry
	// non-idempotent operations.
	//
	// See litmus test above for deciding between FailedPrecondition,
	// Aborted, and Unavailable.
	//
	// This error code will be generated by the gRPC framework during
	// abrupt shutdown of a server process or network connection.
	GrpcUnavailable Code = 14

	// GrpcDataLoss indicates unrecoverable data loss or corruption.
	//
	// This error code will not be generated by the gRPC framework.
	GrpcDataLoss Code = 15

	// GrpcUnauthenticated indicates the request does not have valid
	// authentication credentials for the operation.
	//
	// The gRPC framework will generate this error code when the
	// authentication metadata is invalid or a Credentials' callback fails,
	// but also expect authentication middleware to generate it.
	GrpcUnauthenticated Code = 16
)

var (
	ErrGrpcCanceled           = _new(GrpcCanceled, "Request canceled by the client.")
	ErrGrpcUnknown            = _new(GrpcUnknown, "Unknown server error.")
	ErrGrpcInvalidArgument    = _new(GrpcInvalidArgument, "Client specified an invalid argument.")
	ErrGrpcDeadlineExceeded   = _new(GrpcDeadlineExceeded, "Request deadline exceeded.")
	ErrGrpcNotFound           = _new(GrpcNotFound, "A specified resource is not found.")
	ErrGrpcAlreadyExists      = _new(GrpcAlreadyExists, "The resource that a client tried to create already exists.")
	ErrGrpcPermissionDenied   = _new(GrpcPermissionDenied, "Client does not have sufficient permission.")
	ErrGrpcResourceExhausted  = _new(GrpcResourceExhausted, "Either out of resource quota or reaching rate limiting.")
	ErrGrpcFailedPrecondition = _new(GrpcFailedPrecondition, "Request can not be executed in the current system state, such as deleting a non-empty directory.")
	ErrGrpcAborted            = _new(GrpcAborted, "Concurrency conflict, such as read-modify-write conflict.")
	ErrGrpcOutOfRange         = _new(GrpcOutOfRange, "Client specified an invalid range.")
	ErrGrpcUnimplemented      = _new(GrpcUnimplemented, "API method not implemented by the server.")
	ErrGrpcInternal           = _new(GrpcInternal, "Internal server error.")
	ErrGrpcUnavailable        = _new(GrpcUnavailable, "Service unavailable.")
	ErrGrpcDataLoss           = _new(GrpcDataLoss, "Unrecoverable data loss or data corruption.")
	ErrGrpcUnauthenticated    = _new(GrpcUnauthenticated, "Request not authenticated due to missing, invalid, or expired OAuth token.")
)
