package errorx

import (
	"github.com/zeromicro/go-zero/core/lang"
)

type Code uint32

const (
	MinCustomSystemCode   Code = 10000
	MaxCustomSystemCode   Code = 11999
	MinCustomBusinessCode Code = 50000
	MaxCustomBusinessCode Code = 51999

	DefaultInterval = 99 // 默认区间范围

	baseIndexOfCommon      = 0
	baseIndexOfUser        = 100
	baseIndexOfPermission  = 200
	baseIndexOfNotifier    = 300
	baseIndexOfBeat        = 400
	baseIndexOfRedashQuery = 500
)

const (
	OK Code = 0

	Failure       Code = 98
	InternalError Code = 99 // 99: 系统内部错误
)

// 注：自定义的全局错误码，如果是系统错误的需要在这里手工添加
var systemErrorCodes = map[Code]lang.PlaceholderType{
	InternalError: lang.Placeholder,

	// GRPC Error Code
	GrpcAborted:       lang.Placeholder,
	GrpcOutOfRange:    lang.Placeholder,
	GrpcUnimplemented: lang.Placeholder,
	GrpcInternal:      lang.Placeholder,
	GrpcUnavailable:   lang.Placeholder,

	// HTTP Status Code
	HttpInternalServerError: lang.Placeholder,
	HttpNotImplemented:      lang.Placeholder,
	HttpBadGateway:          lang.Placeholder,
	HttpServiceUnavailable:  lang.Placeholder,
}

func IsSystemError(code Code) bool {
	// custom system error code range: [10000, 50000)
	if code >= MinCustomSystemCode && code < MinCustomBusinessCode {
		return true
	} else if _, ok := systemErrorCodes[code]; ok {
		return true
	}
	return false
}
