package errorx

import (
	"fmt"

	"github.com/hashicorp/go-multierror"
	"google.golang.org/grpc/status"
)

type Errors interface {
	Code() Code
	Message() string
}

type Error struct {
	code    Code
	message string
}

// New returns an Error representing code and msg.
func New(code Code, msg string) *Error {
	//if code < MinCustomSystemCode {
	//	panic(fmt.Sprintf("error code must be equal or greater than %d", MinCustomSystemCode))
	//}

	return &Error{code, msg}
}

// Newf returns New(code, fmt.Sprintf(format, args...)).
func Newf(code Code, format string, args ...any) *Error {
	return New(code, fmt.Sprintf(format, args...))
}

// _new returns an Error representing code and msg, just for global error code
func _new(code Code, msg string) *Error {
	return &Error{code, msg}
}

// Err returns an error representing code and msg. If code is OK, returns nil.
func Err(code Code, msg string) error {
	return New(code, msg).Err()
}

// Errorf returns Error(code, fmt.Sprintf(format, args...)).
func Errorf(code Code, format string, args ...any) error {
	return Err(code, fmt.Sprintf(format, args...))
}

// FromError returns an Error representation of err.
func FromError(err error) (e *Error, ok bool) {
	if err == nil {
		return nil, true
	}

	switch v := err.(type) {
	case *Error:
		return v, true
	case Errors:
		return New(v.Code(), v.Message()), true
	case *multierror.Error:
		return New(MultipleError, v.Error()), true
	case interface{ Unwrap() error }:
		return FromError(v.Unwrap())
	case interface {
		GRPCStatus() *status.Status
	}:
		s := v.GRPCStatus()
		return New(Code(s.Code()), s.Message()), true
	default:
		return New(Unknown, err.Error()), false
	}
}

// Convert is a convenience function which removes the need to handle the
// boolean return value from FromError.
func Convert(err error) *Error {
	s, _ := FromError(err)
	return s
}

// RootError returns the root error which is an instance of Error
func RootError(err error) (*Error, bool) {
	for {
		switch v := err.(type) {
		case *Error:
			return v, true
		case interface{ Unwrap() error }:
			err = v.Unwrap()
		case interface{ Cause() error }:
			err = v.Cause()
		default:
			return nil, false
		}
	}
}

func (e *Error) Code() Code {
	return e.code
}

func (e *Error) Message() string {
	return e.message
}

// Err returns an immutable error representing s; returns nil if s.Code() is OK.
func (e *Error) Err() error {
	if e.Code() == OK {
		return nil
	}
	return e
}

func (e *Error) String() string {
	return fmt.Sprintf("error: code = %d, msg = %s", e.Code(), e.Message())
}

func (e *Error) Error() string {
	return e.String()
}
