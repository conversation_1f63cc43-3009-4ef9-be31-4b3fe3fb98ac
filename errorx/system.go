package errorx

// 自定义系统错误码: 10000 - 11999

// `common`: 10000 - 10099
const (
	_ = MinCustomSystemCode + baseIndexOfCommon + iota // 10000: min

	Unknown                 // 10001: 未知错误
	DBError                 // 10002: 数据库错误
	RedisError              // 10003: Redis错误
	SerializationError      // 10004: 序列化（或反序列化）错误
	GenerateUniqueIdFailure // 10005: 生成唯一ID失败
	NewObjectFailure        // 10006: 创建对象失败
	ResetObjectFailure      // 10007: 重置对象失败
	CopyToStructFailure     // 10008: 拷贝到结构体失败
	EvalRedisScriptFailure  // 10009: 执行Redis脚本（Lua）失败

	_ = MinCustomSystemCode + baseIndexOfCommon + DefaultInterval // 10099: max
)

// `user`: 10100 - 10199
const (
	_ = MinCustomSystemCode + baseIndexOfUser + iota // 10100: min

	_ = MinCustomSystemCode + baseIndexOfUser + DefaultInterval // 10199: max
)

// `permission`: 10200 - 10299
const (
	_ = MinCustomSystemCode + baseIndexOfPermission + iota // 10200: min

	_ = MinCustomSystemCode + baseIndexOfPermission + DefaultInterval // 10299: max
)

// `notifier`: 10300 - 10399
const (
	_ = MinCustomSystemCode + baseIndexOfNotifier + iota // 10300: min

	_ = MinCustomSystemCode + baseIndexOfNotifier + DefaultInterval // 10399: max
)

// `beat`: 10400 - 10499
const (
	_ = MinCustomSystemCode + baseIndexOfBeat + iota // 10400: min

	_ = MinCustomSystemCode + baseIndexOfBeat + DefaultInterval // 10499: max
)

// `redashquery`: 10500 - 10599
const (
	_ = MinCustomSystemCode + baseIndexOfRedashQuery + iota // 10500: min

	_ = MinCustomSystemCode + baseIndexOfRedashQuery + DefaultInterval // 10599: max
)
