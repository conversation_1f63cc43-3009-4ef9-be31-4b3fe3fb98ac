package errorx

// 自定义业务错误码: 50000 - 51999

// `common`: 50000 - 50099
const (
	_ = MinCustomBusinessCode + baseIndexOfCommon + iota // 50000: min

	ParseParamError          // 50001: 参数解析错误
	ValidateParamError       // 50002: 参数验证错误
	AcquireRedisLockFailure  // 50003: 获取Redis锁失败
	ReleaseRedisLockFailure  // 50004: 释放Redis锁失败
	MultipleError            // 50005: 多个错误
	FileOperationFailure     // 50006: 文件操作失败（包括：读、写）
	CronExpressionParseError // 50007: Cron表达式解析错误
	NotExists                // 50008: 数据不存在
	AlreadyExists            // 50009: 数据已存在
	DoesNotSupport           // 50010: 暂不支持
	ValidateFailure          // 50011: 验证失败
	ProhibitedBehavior       // 50012: 被禁止的行为
	TypeError                // 50013: 类型错误
	CallExternalAPIFailure   // 50014: 调用外部接口失败
	FSMError                 // 50015: 有限状态机错误（包括：事件错误和状态流转错误）
	GitOperationFailure      // 50016: Git操作失败（如：clone、pull）

	_ = MinCustomBusinessCode + baseIndexOfCommon + DefaultInterval // 50099: max
)

// `user`: 50100 - 50199
const (
	_ = MinCustomBusinessCode + baseIndexOfUser + iota // 50100: min

	AuthError         // 50101: 用户认证错误
	NeedLogin         // 50102: 需要登录
	NeedRefreshToken  // 50103: 需要刷新令牌
	RefreshFailed     // 50104: 刷新令牌失败
	CurrentUserIsNull // 50105: 当前用户为NULL

	_ = MinCustomBusinessCode + baseIndexOfUser + DefaultInterval // 50199: max
)

// `permission`: 50200 - 50299
const (
	_ = MinCustomBusinessCode + baseIndexOfPermission + iota // 50200: min

	PermissionInsufficient // 50200: 权限不足

	_ = MinCustomBusinessCode + baseIndexOfPermission + DefaultInterval // 50299: max
)

// `notifier`: 50300 - 50399
const (
	_ = MinCustomBusinessCode + baseIndexOfNotifier + iota // 50300: min

	_ = MinCustomBusinessCode + baseIndexOfNotifier + DefaultInterval // 50399: max
)

// `beat`: 50400 - 50499
const (
	_ = MinCustomBusinessCode + baseIndexOfBeat + iota // 50400: min

	CreateOrModifyPeriodicTaskFailure // 50401: 创建或编辑定时任务失败
	RemovePeriodicTaskFailure         // 50402: 移除定时任务失败
	StartOrStopPeriodicTaskFailure    // 50403: 启动或停止定时任务失败

	_ = MinCustomBusinessCode + baseIndexOfBeat + DefaultInterval // 50499: max
)

// `redashquery`: 50500 - 50599
const (
	_ = MinCustomBusinessCode + baseIndexOfRedashQuery + iota // 50500: min

	_ = MinCustomBusinessCode + baseIndexOfRedashQuery + DefaultInterval // 50599: max
)
