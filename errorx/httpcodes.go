package errorx

// HTTP Status Code (100 - 511)
const (
	HttpContinue           Code = 100 // RFC 7231, 6.2.1
	HttpSwitchingProtocols Code = 101 // RFC 7231, 6.2.2
	HttpProcessing         Code = 102 // RFC 2518, 10.1
	HttpEarlyHints         Code = 103 // RFC 8297

	HttpOK                   Code = 200 // RFC 7231, 6.3.1
	HttpCreated              Code = 201 // RFC 7231, 6.3.2
	HttpAccepted             Code = 202 // RFC 7231, 6.3.3
	HttpNonAuthoritativeInfo Code = 203 // RFC 7231, 6.3.4
	HttpNoContent            Code = 204 // RFC 7231, 6.3.5
	HttpResetContent         Code = 205 // RFC 7231, 6.3.6
	HttpPartialContent       Code = 206 // RFC 7233, 4.1
	HttpMultiStatus          Code = 207 // RFC 4918, 11.1
	HttpAlreadyReported      Code = 208 // RFC 5842, 7.1
	HttpIMUsed               Code = 226 // RFC 3229, 10.4.1

	HttpMultipleChoices   Code = 300 // RFC 7231, 6.4.1
	HttpMovedPermanently  Code = 301 // RFC 7231, 6.4.2
	HttpFound             Code = 302 // RFC 7231, 6.4.3
	HttpSeeOther          Code = 303 // RFC 7231, 6.4.4
	HttpNotModified       Code = 304 // RFC 7232, 4.1
	HttpUseProxy          Code = 305 // RFC 7231, 6.4.5
	_                     Code = 306 // RFC 7231, 6.4.6 (Unused)
	HttpTemporaryRedirect Code = 307 // RFC 7231, 6.4.7
	HttpPermanentRedirect Code = 308 // RFC 7538, 3

	HttpBadRequest                   Code = 400 // RFC 7231, 6.5.1
	HttpUnauthorized                 Code = 401 // RFC 7235, 3.1
	HttpPaymentRequired              Code = 402 // RFC 7231, 6.5.2
	HttpForbidden                    Code = 403 // RFC 7231, 6.5.3
	HttpNotFound                     Code = 404 // RFC 7231, 6.5.4
	HttpMethodNotAllowed             Code = 405 // RFC 7231, 6.5.5
	HttpNotAcceptable                Code = 406 // RFC 7231, 6.5.6
	HttpProxyAuthRequired            Code = 407 // RFC 7235, 3.2
	HttpRequestTimeout               Code = 408 // RFC 7231, 6.5.7
	HttpConflict                     Code = 409 // RFC 7231, 6.5.8
	HttpGone                         Code = 410 // RFC 7231, 6.5.9
	HttpLengthRequired               Code = 411 // RFC 7231, 6.5.10
	HttpPreconditionFailed           Code = 412 // RFC 7232, 4.2
	HttpRequestEntityTooLarge        Code = 413 // RFC 7231, 6.5.11
	HttpRequestURITooLong            Code = 414 // RFC 7231, 6.5.12
	HttpUnsupportedMediaType         Code = 415 // RFC 7231, 6.5.13
	HttpRequestedRangeNotSatisfiable Code = 416 // RFC 7233, 4.4
	HttpExpectationFailed            Code = 417 // RFC 7231, 6.5.14
	HttpTeapot                       Code = 418 // RFC 7168, 2.3.3
	HttpMisdirectedRequest           Code = 421 // RFC 7540, 9.1.2
	HttpUnprocessableEntity          Code = 422 // RFC 4918, 11.2
	HttpLocked                       Code = 423 // RFC 4918, 11.3
	HttpFailedDependency             Code = 424 // RFC 4918, 11.4
	HttpUpgradeRequired              Code = 426 // RFC 7231, 6.5.15
	HttpPreconditionRequired         Code = 428 // RFC 6585, 3
	HttpTooManyRequests              Code = 429 // RFC 6585, 4
	HttpRequestHeaderFieldsTooLarge  Code = 431 // RFC 6585, 5
	HttpUnavailableForLegalReasons   Code = 451 // RFC 7725, 3

	HttpInternalServerError           Code = 500 // RFC 7231, 6.6.1
	HttpNotImplemented                Code = 501 // RFC 7231, 6.6.2
	HttpBadGateway                    Code = 502 // RFC 7231, 6.6.3
	HttpServiceUnavailable            Code = 503 // RFC 7231, 6.6.4
	HttpGatewayTimeout                Code = 504 // RFC 7231, 6.6.5
	HttpHTTPVersionNotSupported       Code = 505 // RFC 7231, 6.6.6
	HttpVariantAlsoNegotiates         Code = 506 // RFC 2295, 8.1
	HttpInsufficientStorage           Code = 507 // RFC 4918, 11.5
	HttpLoopDetected                  Code = 508 // RFC 5842, 7.2
	HttpNotExtended                   Code = 510 // RFC 2774, 7
	HttpNetworkAuthenticationRequired Code = 511 // RFC 6585, 6
)

var (
	ErrHttpBadRequest        = _new(HttpBadRequest, "Bad Request")
	ErrHttpUnauthorized      = _new(HttpUnauthorized, "Unauthorized")
	ErrHttpPaymentRequired   = _new(HttpPaymentRequired, "Payment Required")
	ErrHttpForbidden         = _new(HttpForbidden, "Forbidden")
	ErrHttpNotFound          = _new(HttpNotFound, "Not Found")
	ErrHttpMethodNotAllowed  = _new(HttpMethodNotAllowed, "Method Not Allow")
	ErrHttpNotAcceptable     = _new(HttpNotAcceptable, "Not Acceptable")
	ErrHttpProxyAuthRequired = _new(HttpProxyAuthRequired, "Proxy Authentication Required")
	ErrHttpRequestTimeout    = _new(HttpRequestTimeout, "Request timeout")
	ErrHttpConflict          = _new(HttpConflict, "Conflict")

	ErrHttpInternalServerError     = _new(HttpInternalServerError, "Internal Server Error")
	ErrHttpNotImplemented          = _new(HttpNotImplemented, "Not Implemented")
	ErrHttpBadGateway              = _new(HttpBadGateway, "Bad Gateway")
	ErrHttpServiceUnavailable      = _new(HttpServiceUnavailable, "Service Unavailable")
	ErrHttpGatewayTimeout          = _new(HttpGatewayTimeout, "Gateway timeout")
	ErrHttpHTTPVersionNotSupported = _new(HttpHTTPVersionNotSupported, "HTTP Version Not Supported")
)
