package stream

import (
	"context"
	"fmt"
	"net"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"
	redis "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/proc"
	red "github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/core/utils"

	r "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
)

const (
	minVersion             = "5.0.0" // redis stream has been available since version 5.0.0.
	xpendingCommandVersion = "6.2.0" // added the IDLE option and exclusive range intervals since version 6.2.0.

	defaultGroup           = "redisqueue"
	defaultBlockingTimeout = 5 * time.Second
	defaultReclaimInterval = 1 * time.Second
)

var defaultConsumerOptions = ConsumerOptions{
	VisibilityTimeout: 60 * time.Second,
	BlockingTimeout:   5 * time.Second,
	ReclaimInterval:   1 * time.Second,
	BufferSize:        100,
	Concurrency:       10,
}

// ConsumerFunc is a type alias for the functions that will be used to handle
// and process Messages.
type ConsumerFunc func(*Message) error

type registeredConsumer struct {
	fn ConsumerFunc
	id string
}

// ConsumerOptions provide options to configure the Consumer.
type ConsumerOptions struct {
	// Name sets the name of this consumer. This will be used when fetching from
	// Redis. If empty, the hostname will be used.
	Name string
	// GroupName sets the name of the consumer group. This will be used when
	// coordinating in Redis. If empty, the hostname will be used.
	GroupName string
	// VisibilityTimeout dictates the maximum amount of time a message should
	// stay in pending. If there is a message that has been idle for more than
	// this duration, the consumer will attempt to claim it.
	VisibilityTimeout time.Duration
	// BlockingTimeout designates how long the XREADGROUP call blocks for. If
	// this is 0, it will block indefinitely. While this is the most efficient
	// from a polling perspective, if this call never times out, there is no
	// opportunity to yield back to Go at a regular interval. This means it's
	// possible that if no messages are coming in, the consumer cannot
	// gracefully shut down. Instead, it's recommended to set this to 1-5
	// seconds, or even longer, depending on how long your application can wait
	// to shut down.
	BlockingTimeout time.Duration
	// ReclaimInterval is the amount of time in between calls to XPENDING to
	// attempt to reclaim jobs that have been idle for more than the visibility
	// timeout. A smaller duration will result in more frequent checks. This
	// will allow messages to be reaped faster, but it will put more load on
	// Redis.
	ReclaimInterval time.Duration
	// BufferSize determines the size of the channel uses to coordinate the
	// processing of the messages. This determines the maximum number of
	// in-flight messages.
	BufferSize int
	// Concurrency dictates how many goroutines to spawn to handle the messages.
	Concurrency int
	// AutoDestroy dictates whether to automatically destroy when consumer is
	// shutting down.
	AutoDestroy bool
	// RedisClient supersedes the RedisConf field, and allows you to inject
	// an already-made Redis Client for use in the consumer. This may be either
	// the standard client or a cluster client.
	RedisClient redis.UniversalClient
	// RedisConf allows you to configure the underlying Redis connection.
	// This field is used if RedisClient field is nil.
	RedisConf red.RedisConf
}

// Consumer adds a convenient wrapper around dequeuing and managing concurrency.
type Consumer struct {
	// Errors is a channel that you can receive from to centrally handle any
	// errors that may occur either by your ConsumerFuncs or by internal
	// processing functions. Because this is an unbuffered channel, you must
	// have a listener on it. If you don't part of the consumer could stop
	// functioning when errors occur due to the blocking nature of unbuffered
	// channels.
	Errors chan error

	options     ConsumerOptions
	rdb         redis.UniversalClient
	rdbVersion  string
	xpending620 bool

	consumers map[string]registeredConsumer
	streams   []string
	queue     chan *Message
	wg        sync.WaitGroup
	mutex     sync.RWMutex

	stopOnce    sync.Once
	stopReclaim chan lang.PlaceholderType
	stopPoll    chan lang.PlaceholderType
	stopWorkers chan lang.PlaceholderType
}

// NewConsumer uses a default set of options to create a Consumer. It sets Name
// to the hostname, GroupName to "redisqueue", VisibilityTimeout to 60 seconds,
// BufferSize to 100, and Concurrency to 10. In most production environments,
// you'll want to use NewConsumerWithOptions.
func NewConsumer() (*Consumer, error) {
	return NewConsumerWithOptions(defaultConsumerOptions)
}

// NewConsumerWithOptions creates a Consumer with custom ConsumerOptions. If
// Name is left empty, it defaults to the hostname; if GroupName is left empty,
// it defaults to "redisqueue"; if BlockingTimeout is 0, it defaults to 5
// seconds; if ReclaimInterval is 0, it defaults to 1 second.
func NewConsumerWithOptions(options ConsumerOptions) (*Consumer, error) {
	hostname, _ := os.Hostname()

	if options.Name == "" {
		options.Name = hostname
	}
	if options.GroupName == "" {
		options.GroupName = defaultGroup
	}
	if options.BlockingTimeout == 0 {
		options.BlockingTimeout = defaultBlockingTimeout
	}
	if options.ReclaimInterval == 0 {
		options.ReclaimInterval = defaultReclaimInterval
	}

	var rdb redis.UniversalClient
	if options.RedisClient != nil {
		rdb = options.RedisClient
	} else {
		rdb = r.NewClient(options.RedisConf)
	}

	version, err := r.GetRedisVersion(rdb)
	if err != nil {
		return nil, err
	}
	if !utils.CompareVersions(version, ">=", minVersion) {
		return nil, errors.Errorf("redis streams have been available since version %q", minVersion)
	}

	return &Consumer{
		Errors: make(chan error),

		options:     options,
		rdb:         rdb,
		rdbVersion:  version,
		xpending620: utils.CompareVersions(version, ">=", xpendingCommandVersion),

		consumers: make(map[string]registeredConsumer),
		streams:   make([]string, 0),
		queue:     make(chan *Message, options.BufferSize),

		stopReclaim: make(chan lang.PlaceholderType, 1),
		stopPoll:    make(chan lang.PlaceholderType, 1),
		stopWorkers: make(chan lang.PlaceholderType, options.Concurrency),
	}, nil
}

// RegisterWithLastID is the same as Register, except that it also lets you
// specify the oldest message to receive when first creating the consumer group.
// This can be any valid message ID, "0" for all messages in the stream, or "$"
// for only new messages.
//
// If the consumer group already exists the id field is ignored, meaning you'll
// receive unprocessed messages.
func (c *Consumer) RegisterWithLastID(stream, id string, fn ConsumerFunc) {
	if len(id) == 0 {
		id = "0"
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.consumers[stream] = registeredConsumer{
		fn: fn,
		id: id,
	}
}

// Register takes in a stream name and a ConsumerFunc that will be called when a
// message comes in from that stream. Register must be called at least once
// before Run is called. If the same stream name is passed in twice, the first
// ConsumerFunc is overwritten by the second.
func (c *Consumer) Register(stream string, fn ConsumerFunc) {
	c.RegisterWithLastID(stream, "0", fn)
}

// Run starts all the worker goroutines and starts processing from the
// streams that have been registered with Register. All errors will be sent to
// the Errors channel. If Register was never called, an error will be sent and
// Run will terminate early. The same will happen if an error occurs when
// creating the consumer group in Redis. Run will block until Shutdown is called
// and all the in-flight messages have been processed.
func (c *Consumer) Run() {
	count := len(c.consumers)

	if count == 0 {
		c.Errors <- errors.New("at least one consumer function needs to be registered")
		return
	}

	c.mutex.RLock()
	for stream, consumer := range c.consumers {
		ctx, cancel := context.WithTimeout(context.Background(), defaultOperationTimeout)

		c.streams = append(c.streams, stream)
		err := c.rdb.XGroupCreateMkStream(ctx, stream, c.options.GroupName, consumer.id).Err()
		// ignoring the BUSYGROUP error makes this a noop
		if err != nil && err.Error() != "BUSYGROUP Consumer Group name already exists" {
			c.Errors <- errors.Wrapf(
				err,
				"failed to create the consumer group, stream: %s, group: %s",
				stream, c.options.GroupName,
			)

			cancel()
			c.mutex.RUnlock()
			return
		}

		cancel()
	}
	c.mutex.RUnlock()

	for i := 0; i < count; i++ {
		c.streams = append(c.streams, ">")
	}

	threading.GoSafe(c.reclaim)
	threading.GoSafe(c.poll)

	proc.AddShutdownListener(c.Shutdown)

	c.wg.Add(c.options.Concurrency)
	for i := 0; i < c.options.Concurrency; i++ {
		threading.GoSafe(c.work)
	}
	c.wg.Wait()
}

// Shutdown stops new messages from being processed and tells the workers to
// wait until all in-flight messages have been processed, and then they exit.
// The order that things stop is 1) the reclaim process (if it's running), 2)
// the polling process, and 3) the worker processes.
func (c *Consumer) Shutdown() {
	c.stopOnce.Do(
		func() {
			c.stopReclaim <- lang.Placeholder
			if c.options.VisibilityTimeout == 0 {
				c.stopPoll <- lang.Placeholder
			}

			if c.options.AutoDestroy {
				_ = mr.MapReduceVoid[string, string](
					func(source chan<- string) {
						c.mutex.RLock()
						defer c.mutex.RUnlock()

						for stream := range c.consumers {
							source <- stream
						}
					}, func(item string, writer mr.Writer[string], cancel func(error)) {
						ctx, _cancel := context.WithTimeout(context.Background(), defaultOperationTimeout)
						defer _cancel()

						var (
							fn  string
							ret int64
							err error
						)
						if c.options.Name == c.options.GroupName {
							// only one consumer in a group, destroy group
							fn = "XGROUP DESTROY"
							ret, err = c.rdb.XGroupDestroy(ctx, item, c.options.GroupName).Result()
						} else {
							// one or more consumer in a group, delete self consumer
							fn = "XGROUP DELCONSUMER"
							ret, err = c.rdb.XGroupDelConsumer(
								ctx, item, c.options.GroupName, c.options.Name,
							).Result()
						}

						if err != nil {
							logx.Errorf("failed to shutdown consumer by %q, error: %v", fn, err)
						} else {
							logx.Infof("shutdown consumer by %q, result: %d", fn, ret)
						}
					}, func(pipe <-chan string, cancel func(error)) {
					},
				)
			}
		},
	)
}

// reclaim runs in a separate goroutine and checks the list of pending messages
// in every stream. For every message, if it's been idle for longer than the
// VisibilityTimeout, it will attempt to claim that message for this consumer.
// If VisibilityTimeout is 0, this function returns early and no messages are
// reclaimed. It checks the list of pending messages according to
// ReclaimInterval.
func (c *Consumer) reclaim() {
	if c.options.VisibilityTimeout == 0 {
		return
	}

	idle := c.options.VisibilityTimeout
	if !c.xpending620 {
		idle = time.Duration(0)
	}

	ticker := timewheel.NewTicker(c.options.ReclaimInterval)

	for {
		select {
		case <-c.stopReclaim:
			// once the reclaim process has stopped, stop the polling process
			c.stopPoll <- lang.Placeholder
			return
		case <-ticker.C:
			c.mutex.RLock()
			for stream := range c.consumers {
				start := "-"
				end := "+"

				for {
					ctx, cancel := context.WithTimeout(context.Background(), defaultBlockingTimeout)
					result, err := c.rdb.XPendingExt(
						ctx,
						&redis.XPendingExtArgs{
							Stream: stream,
							Group:  c.options.GroupName,
							Idle:   idle,
							Start:  start,
							End:    end,
							Count:  int64(c.options.BufferSize - len(c.queue)),
						},
					).Result()
					cancel()

					if err != nil && !errors.Is(err, redis.Nil) {
						if !strings.HasPrefix(err.Error(), "NOGROUP No such key") {
							c.Errors <- errors.Wrapf(
								err,
								"failed to list the pending messages, stream: %s, group: %s, idle: %s, start: %s, end: %s",
								stream, c.options.GroupName, idle.String(), start, end,
							)
						}

						break
					}

					if len(result) == 0 {
						break
					}

					for _, ext := range result {
						ctx, cancel = context.WithTimeout(context.Background(), defaultBlockingTimeout)
						messages, err := c.rdb.XClaim(
							ctx,
							&redis.XClaimArgs{
								Stream:   stream,
								Group:    c.options.GroupName,
								Consumer: c.options.Name,
								MinIdle:  c.options.VisibilityTimeout,
								Messages: []string{ext.ID},
							},
						).Result()
						cancel()

						if err != nil && !errors.Is(err, redis.Nil) {
							c.Errors <- errors.Wrapf(
								err,
								"failed to claim pending message, stream: %s, group: %s, consumer: %s, idle: %s, message: %s",
								stream, c.options.GroupName, c.options.Name, c.options.VisibilityTimeout.String(),
								jsonx.MarshalToStringIgnoreError(ext),
							)
							break
						}

						// If the Redis nil error is returned, it means that
						// the message no longer exists in the stream.
						// However, it is still in a pending state. This
						// could happen if a message was claimed by a
						// consumer, that consumer died, and the message
						// gets deleted (either through a XDEL call or
						// through MAXLEN). Since the message no longer
						// exists, the only way we can get it out of the
						// pending state is to acknowledge it.
						if errors.Is(err, redis.Nil) {
							ctx, cancel = context.WithTimeout(context.Background(), defaultBlockingTimeout)
							err = c.rdb.XAck(ctx, stream, c.options.GroupName, ext.ID).Err()
							cancel()

							if err != nil && !errors.Is(err, redis.Nil) {
								c.Errors <- errors.Wrapf(
									err, "failed to ack pending message, stream: %s, group: %s, message: %s",
									stream, c.options.GroupName, jsonx.MarshalToStringIgnoreError(ext),
								)
								continue
							}
						}
						c.enqueue(stream, messages)
					}

					lastID := result[len(result)-1].ID
					start, err = c.nextMessageID(lastID)
					if err != nil {
						c.Errors <- errors.Wrapf(
							err, "failed to get the next message ID, stream: %s, group: %s, id: %s",
							stream, c.options.GroupName, lastID,
						)
					}
				}
			}
			c.mutex.RUnlock()
		}
	}
}

// nextMessageID takes in a message ID (e.g. 1564886140363-0) and
// increments the index section (e.g. 1564886140363-1).
// This is the next valid ID value, and it can be used for paging through messages.
func (c *Consumer) nextMessageID(id string) (string, error) {
	if c.xpending620 {
		// support exclusive range intervals since version 6.2.0
		return fmt.Sprintf("(%s", id), nil
	}

	parts := strings.Split(id, "-")
	if len(parts) != 2 {
		return "", errors.Errorf("invalid message id: %s", id)
	}

	index := parts[1]
	parsed, err := strconv.ParseInt(index, 10, 64)
	if err != nil {
		return "", errors.Wrapf(err, "invalid index section of message id, id: %s, index: %s", id, index)
	}

	return fmt.Sprintf("%s-%d", parts[0], parsed+1), nil
}

// poll constantly checks the streams using XREADGROUP to see if there are any
// messages for this consumer to process. It blocks for up to 5 seconds instead
// of blocking indefinitely so that it can periodically check to see if Shutdown
// was called.
func (c *Consumer) poll() {
	args := &redis.XReadGroupArgs{
		Group:    c.options.GroupName,
		Consumer: c.options.Name,
		Streams:  c.streams,
		Block:    c.options.BlockingTimeout,
	}

	for {
		select {
		case <-c.stopPoll:
			// once the polling has stopped (i.e. there will be no more messages
			// put onto c.queue), stop all the workers
			for i := 0; i < c.options.Concurrency; i++ {
				c.stopWorkers <- lang.Placeholder
			}
			return
		default:
			ctx, cancel := context.WithTimeout(context.Background(), defaultOperationTimeout)
			args.Count = int64(c.options.BufferSize - len(c.queue))
			result, err := c.rdb.XReadGroup(ctx, args).Result()
			cancel()

			if err != nil {
				var e net.Error
				if errors.As(err, &e) && e.Timeout() || errors.Is(err, redis.Nil) {
					continue
				}
				if strings.HasPrefix(err.Error(), "NOGROUP No such key") {
					time.Sleep(c.options.BlockingTimeout)
					continue
				}

				c.Errors <- errors.Wrapf(
					err,
					"failed to read messages, group: %s, consumer: %s, streams: %s",
					c.options.GroupName, c.options.Name, jsonx.MarshalToStringIgnoreError(c.streams),
				)
				continue
			}

			for _, s := range result {
				c.enqueue(s.Stream, s.Messages)
			}
		}
	}
}

// enqueue takes a slice of XMessages, creates corresponding Messages, and sends
// them on the centralized channel for worker goroutines to process.
func (c *Consumer) enqueue(stream string, messages []redis.XMessage) {
	for _, m := range messages {
		msg := &Message{
			ID:     m.ID,
			Stream: stream,
			Values: m.Values,
		}
		c.queue <- msg
	}
}

// work is called in a separate goroutine. The number of work goroutines is
// determined by Concurreny. Once it gets a message from the centralized
// channel, it calls the corrensponding ConsumerFunc depending on the stream it
// came from. If no error is returned from the ConsumerFunc, the message is
// acknowledged in Redis.
func (c *Consumer) work() {
	defer c.wg.Done()

	for {
		select {
		case message := <-c.queue:
			if err := c.process(message); err != nil {
				c.Errors <- errors.Wrapf(
					err,
					"failed to call the consumer function, message: %s",
					jsonx.MarshalToStringIgnoreError(message),
				)
				continue
			}

			ctx, cancel := context.WithTimeout(context.Background(), defaultOperationTimeout)
			err := c.rdb.XAck(ctx, message.Stream, c.options.GroupName, message.ID).Err()
			cancel()

			if err != nil {
				c.Errors <- errors.Wrapf(
					err,
					"failed to ack processed message, group: %s, message: %s",
					c.options.GroupName, jsonx.MarshalToStringIgnoreError(message),
				)
			}
		case <-c.stopWorkers:
			return
		}
	}
}

func (c *Consumer) process(msg *Message) (err error) {
	defer func() {
		if re := recover(); re != nil {
			if e, ok := re.(error); ok {
				err = errors.Wrap(e, "failed to call consumer function")
				return
			}

			err = errors.Errorf("failed to call consumer function: %v", re)
		}
	}()

	c.mutex.RLock()
	defer c.mutex.RUnlock()

	err = c.consumers[msg.Stream].fn(msg)
	return
}
