package stream

import (
	"context"

	redis "github.com/redis/go-redis/v9"
	red "github.com/zeromicro/go-zero/core/stores/redis"

	r "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"
)

var defaultProducerOptions = ProducerOptions{
	MaxLength:   1000,
	Approximate: true,
}

// ProducerOptions provide options to configure the Producer.
type ProducerOptions struct {
	// Max<PERSON><PERSON><PERSON> sets the MAXLEN option when calling XADD. This creates a
	// capped stream to prevent the stream from taking up memory indefinitely.
	// It's important to note though that this isn't the maximum number of
	// _completed_ messages, but the maximum number of _total_ messages. This
	// means that if all consumers are down, but producers are still enqueuing,
	// and the maximum is reached, unprocessed message will start to be dropped.
	// So ideally, you'll set this number to be as high as you can make it.
	// More info here: https://redis.io/commands/xadd#capped-streams.
	MaxLength int64
	// Approximate determines whether to use the ~ with the MAXLEN option.
	// This allows the stream trimming to done in a more efficient manner.
	// More info here: https://redis.io/commands/xadd#capped-streams.
	Approximate bool
	// RedisClient supersedes the RedisConf field, and allows you to inject
	// an already-made Redis Client for use in the consumer. This may be either
	// the standard client or a cluster client.
	RedisClient redis.UniversalClient
	// RedisConf allows you to configure the underlying Redis connection.
	// This field is used if RedisClient field is nil.
	RedisConf red.RedisConf
}

// Producer adds a convenient wrapper around enqueuing messages that will be
// processed later by a Consumer.
type Producer struct {
	options ProducerOptions
	rdb     redis.UniversalClient
}

// NewProducer uses a default set of options to create a Producer. It sets
// MaxLength to 1000 and ApproximateMaxLength to true. In most production
// environments, you'll want to use NewProducerWithOptions.
func NewProducer() (*Producer, error) {
	return NewProducerWithOptions(defaultProducerOptions)
}

// NewProducerWithOptions creates a Producer using custom ProducerOptions.
func NewProducerWithOptions(options ProducerOptions) (*Producer, error) {
	var rdb redis.UniversalClient
	if options.RedisClient != nil {
		rdb = options.RedisClient
	} else {
		if err := options.RedisConf.Validate(); err != nil {
			return nil, err
		}

		rdb = r.NewClient(options.RedisConf)
	}

	return &Producer{
		options: options,
		rdb:     rdb,
	}, nil
}

// Enqueue takes in a pointer to Message and enqueues it into the stream set at
// msg.Stream. While you can set msg.ID, unless you know what you're doing, you
// should let Redis auto-generate the ID. If an ID is auto-generated, it will be
// set on msg.ID for your reference. msg.Values is also required.
func (p *Producer) Enqueue(ctx context.Context, msg *Message) error {
	if ctx == nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(context.Background(), defaultOperationTimeout)
		defer cancel()
	}

	args := &redis.XAddArgs{
		MaxLen: p.options.MaxLength,
		Approx: p.options.Approximate,

		ID:     msg.ID,
		Stream: msg.Stream,
		Values: msg.Values,
	}
	id, err := p.rdb.XAdd(ctx, args).Result()
	if err != nil {
		return err
	}

	msg.ID = id
	return nil
}
