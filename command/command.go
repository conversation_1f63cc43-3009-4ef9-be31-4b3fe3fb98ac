package command

import "github.com/spf13/cobra"

var (
	usageTemplate = `Usage:

{{- if .Runnable}}  {{.UseLine}}{{end}}
{{- if .HasAvailableSubCommands}}  {{.CommandPath}}{{- if .HasAvailableFlags}} [OPTIONS]{{end}} COMMAND
{{- else if .HasAvailableFlags}} [OPTIONS]
{{- end}}

{{with (or .Long .Short)}}{{ . | trim }}{{end}}

{{- if gt (len .Aliases) 0}}

Aliases:
  {{.NameAndAliases}}

{{- end}}

{{- if .HasExample}}

Examples:
{{.Example}}

{{- end}}

{{- if .HasAvailableLocalFlags}}

Options:
{{.LocalFlags.FlagUsages | trimTrailingWhitespaces}}

{{- end}}

{{- if .HasAvailableInheritedFlags}}

Global Options:
{{.InheritedFlags.FlagUsages | trimTrailingWhitespaces}}

{{- end}}

{{- if .HasAvailableSubCommands}}

Available Commands:
{{- range .Commands}}
{{- if (or .IsAvailableCommand (eq .Name "help"))}}
  {{rpad .Name .NamePadding }} {{.Short}}
{{- end}}
{{- end}}

{{- end}}

{{- if .HasHelpSubCommands}}

Additional help topics:
{{- range .Commands}}
{{- if .IsAdditionalHelpTopicCommand}}
  {{rpad .CommandPath .CommandPathPadding}} {{.Short}}
{{- end}}
{{- end}}

{{- end}}

{{- if .HasAvailableSubCommands}}

Use "{{.CommandPath}} COMMAND --help" for more information about a command.

{{- end}}
`

	helpTemplate = `
{{if or .Runnable .HasSubCommands}}{{.UsageString}}{{end}}`

	versionTemplate = `{{.Version}}`
)

func NewRootCommand(use, short, long string) *cobra.Command {
	root := &cobra.Command{
		Use:                   use,
		Short:                 short,
		Long:                  long,
		SilenceUsage:          true,
		SilenceErrors:         true,
		TraverseChildren:      true,
		DisableFlagsInUseLine: true,
	}

	root.SetUsageTemplate(usageTemplate)
	root.SetHelpTemplate(helpTemplate)
	root.SetVersionTemplate(versionTemplate)

	return root
}
