# qet-backend-common 公共模块（工具包）

> **QET: Quality Engineering Tools**

## 错误码
> global: 全局错误码
> - 0: OK
> - 99: Internal Error
> - 1 - 16: GRPC错误码 ([codes.go](https://github.com/grpc/grpc-go/blob/master/codes/codes.go))
> - 100 - 511: HTTP状态码 ([status.go](https://github.com/golang/go/blob/master/src/net/http/status.go))
>
> system: 系统错误（10000 - 49999）
> - 公共服务：10000 - 11999 [`qet-backend-common`, `qet-backend-middleware`]
> - 质量平台：12000 - 13999 [`probe-backend`]
> - 效能平台：14000 - 15999 [`insight-backend`]
>
> business: 业务错误（50000 - 99999）
> - 公共服务：50000 - 51999 [`qet-backend-common`, `qet-backend-middleware`]
> - 质量平台：52000 - 53999 [`probe-backend`]
> - 效能平台：54000 - 55999 [`insight-backend`]
>
> **注：项目下的各服务的错误码统一管理，区间划分各项目可按实际情况进行规划**

## Redis Database Number
| project name | service name | database number |
|--------------|--------------|----------------:|
| probe        | manager      |               2 |
| probe        | dispatcher   |               3 |
| probe        | apiworker    |               4 |
| probe        | reporter     |               5 |
| probe        | account      |               6 |
| common       | beat         |               7 |
| common       | notifier     |               8 |
| common       | user         |               9 |
| common       | permission   |              10 |
| insight      | redashquery  |               ? |

