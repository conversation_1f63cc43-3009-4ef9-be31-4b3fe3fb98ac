package log

import (
	"fmt"
	"io"
	"os"
	"path"
	"sync"
	"time"

	ggm_zap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/sysx"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

const (
	callerSkip = 3

	accessFilename = "access.log"
	errorFilename  = "error.log"
	severeFilename = "severe.log"
	slowFilename   = "slow.log"
	statFilename   = "stat.log"

	consoleMode = "console"
	fileMode    = "file"
	volumeMode  = "volume"

	jsonEncoding  = "json"
	plainEncoding = "plain"

	maxSize    = 100
	maxBackups = 10
)

type Level string

const (
	DEBUG  Level = "debug"
	INFO   Level = "info"
	WARN   Level = "warn"
	ERROR  Level = "error"
	SEVERE Level = "severe"
)

var (
	LevelMap = map[Level]zapcore.Level{
		DEBUG:  zapcore.DebugLevel,
		INFO:   zapcore.InfoLevel,
		WARN:   zapcore.WarnLevel,
		ERROR:  zapcore.ErrorLevel,
		SEVERE: zapcore.FatalLevel,
	}

	teeOptions = []TeeOption{
		{
			filename: accessFilename,
			enabler: func(level zapcore.Level) bool {
				return level < zapcore.ErrorLevel
			},
		},
		{
			filename: errorFilename,
			enabler: func(level zapcore.Level) bool {
				return level == zapcore.ErrorLevel
			},
		},
		{
			filename: severeFilename,
			enabler: func(level zapcore.Level) bool {
				return level > zapcore.ErrorLevel
			},
		},
	}
	slowOption = TeeOption{filename: slowFilename}
	statOption = TeeOption{filename: statFilename}

	once sync.Once
)

type ZapWriter struct {
	logger     *zap.Logger
	slowLogger *zap.Logger
	statLogger *zap.Logger
}

type TeeOption struct {
	filename string
	enabler  zap.LevelEnablerFunc
}

func NewZapWriter(c logx.LogConf, opts ...zap.Option) *ZapWriter {
	opts = append(opts, zap.AddCallerSkip(callerSkip))

	logger := zap.New(newMultiCore(c, teeOptions...), opts...)
	slowLogger := zap.New(newCore(c, slowOption), opts...)
	statLogger := zap.New(newCore(c, statOption), opts...)

	return &ZapWriter{
		logger:     logger,
		slowLogger: slowLogger,
		statLogger: statLogger,
	}
}

func NewZapLoggerWithWriter(c logx.LogConf, w io.Writer, opts ...zap.Option) *zap.Logger {
	opts = append(opts, zap.AddCallerSkip(callerSkip))

	enc := newEncoder(c)

	var enabler zapcore.LevelEnabler
	if v, ok := LevelMap[Level(c.Level)]; ok {
		enabler = v
	} else {
		enabler = zapcore.InfoLevel
	}

	return zap.New(zapcore.NewCore(enc, zapcore.AddSync(w), enabler), opts...)
}

func newMultiCore(c logx.LogConf, opts ...TeeOption) zapcore.Core {
	var cores []zapcore.Core

	for _, opt := range opts {
		cores = append(cores, newCore(c, opt))
	}

	return zapcore.NewTee(cores...)
}

func newCore(c logx.LogConf, opt TeeOption) zapcore.Core {
	enc := newEncoder(c)

	if opt.filename != "" {
		if c.Mode == fileMode {
			c.Path = path.Join(c.Path, opt.filename)
		} else if c.Mode == volumeMode {
			c.Path = path.Join(c.Path, c.ServiceName, sysx.Hostname(), opt.filename)
		}
	}
	ws := newWriteSyncer(c)

	var enabler zapcore.LevelEnabler
	if opt.enabler != nil {
		enabler = zap.LevelEnablerFunc(
			func(level zapcore.Level) bool {
				return opt.enabler(level)
			},
		)
	} else if v, ok := LevelMap[Level(c.Level)]; ok {
		enabler = v
	} else {
		enabler = zapcore.InfoLevel
	}

	return zapcore.NewCore(enc, ws, enabler)
}

func newEncoder(c logx.LogConf) zapcore.Encoder {
	ec := newStanderEncoderConfig()

	switch c.Encoding {
	case plainEncoding:
		ec.ConsoleSeparator = " | "
		return zapcore.NewConsoleEncoder(ec)
	case jsonEncoding:
	default: // jsonEncoding
	}
	return zapcore.NewJSONEncoder(ec)
}

func newWriteSyncer(c logx.LogConf) zapcore.WriteSyncer {
	switch c.Mode {
	case fileMode, volumeMode:
		return zapcore.AddSync(
			&lumberjack.Logger{
				Filename:   c.Path,     // 日志文件路径
				MaxSize:    maxSize,    // 每个日志文件保存的最大尺寸，单位：M
				MaxAge:     c.KeepDays, // 文件最多保存多少天
				MaxBackups: maxBackups, // 日志文件最多保存多少个备份
				Compress:   c.Compress, // 是否压缩
			},
		)
	case consoleMode:
	default: // consoleMode
	}
	return zapcore.AddSync(os.Stdout)
}

func newStanderEncoderConfig() zapcore.EncoderConfig {
	return zapcore.EncoderConfig{
		TimeKey:        "ts",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     StanderTimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
		EncodeName:     zapcore.FullNameEncoder,
	}
}

func StanderTimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(t.Format("2006-01-02 15:04:05.000"))
}

func (w *ZapWriter) Logger() *zap.Logger {
	return w.logger
}

func (w *ZapWriter) Alert(v any) {
	w.logger.Error(fmt.Sprint(v))
}

func (w *ZapWriter) Close() error {
	return w.logger.Sync()
}

func (w *ZapWriter) Error(v any, fields ...logx.LogField) {
	w.logger.Error(fmt.Sprint(v), toZapFields(fields...)...)
}

func (w *ZapWriter) Warn(v any, fields ...logx.LogField) {
	w.logger.Warn(fmt.Sprint(v), toZapFields(fields...)...)
}

func (w *ZapWriter) Info(v any, fields ...logx.LogField) {
	w.logger.Info(fmt.Sprint(v), toZapFields(fields...)...)
}

func (w *ZapWriter) Debug(v any, fields ...logx.LogField) {
	w.logger.Debug(fmt.Sprint(v), toZapFields(fields...)...)
}

func (w *ZapWriter) Severe(v any) {
	w.logger.Fatal(fmt.Sprint(v))
}

func (w *ZapWriter) Slow(v any, fields ...logx.LogField) {
	w.slowLogger.Warn(fmt.Sprint(v), toZapFields(fields...)...)
}

func (w *ZapWriter) Stack(v any) {
	w.logger.Error(fmt.Sprint(v), zap.Stack("stack"))
}

func (w *ZapWriter) Stat(v any, fields ...logx.LogField) {
	w.statLogger.Info(fmt.Sprint(v), toZapFields(fields...)...)
}

func (w *ZapWriter) SetLogxWriter() {
	// just set once by one service
	once.Do(
		func() {
			logx.SetWriter(w)
		},
	)
}

func (w *ZapWriter) SetGRPCLoggerV2() {
	ggm_zap.ReplaceGrpcLoggerV2WithVerbosity(w.logger, int(w.logger.Level()))
}

func toZapFields(fields ...logx.LogField) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for _, f := range fields {
		zapFields = append(zapFields, zap.Any(f.Key, f.Value))
	}
	return zapFields
}

func SetWriter(w *ZapWriter, flags ...bool) {
	w.SetLogxWriter()

	if len(flags) > 0 && flags[0] {
		w.SetGRPCLoggerV2()
	}
}
