package server

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/rest"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
)

type (
	TearDownFunc              func()
	NewConfig                 func() Config
	NewService                func(c Config, w *log.ZapWriter) (service.Service, error)
	NewServiceAndTearDownFunc func(c Config, w *log.ZapWriter) (service.Service, TearDownFunc, error)
)

type Config interface {
	ListenOn() string
	LogConfig() logx.LogConf
}

type ApiService interface {
	service.Service

	Use(rest.Middleware)
}

type RpcService interface {
	service.Service

	AddOptions(...grpc.ServerOption)
	AddStreamInterceptors(...grpc.StreamServerInterceptor)
	AddUnaryInterceptors(...grpc.UnaryServerInterceptor)
}

type CombineServerMiddlewaresConf struct {
	ApiServerMiddlewaresConf  // API服务中间件配置
	RpcServerInterceptorsConf // RPC服务拦截器配置
}

type ApiServerMiddlewaresConf struct {
	UnUseRecover bool
	UnUseAuth    bool
	UnUserMetric bool

	CustomMiddlewares []rest.Middleware
}

type RpcServerInterceptorsConf struct {
	UnUseZapTags  bool
	UnUseResponse bool
	UnUseUserInfo bool
	UnUseMetric   bool
	UnUseGzip     bool

	CustomServerOptions      []grpc.ServerOption
	CustomStreamInterceptors []grpc.StreamServerInterceptor
	CustomUnaryInterceptors  []grpc.UnaryServerInterceptor
}
