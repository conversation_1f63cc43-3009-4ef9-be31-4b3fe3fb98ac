package redislimiter

import (
	"context"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/require"
	red "github.com/zeromicro/go-zero/core/stores/redis"

	r "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"
)

var l *Limiter

func TestMain(m *testing.M) {
	mr, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer mr.Close()

	c := r.NewClient(red.RedisConf{Host: mr.Addr()})
	if err = c.FlushDB(context.TODO()).Err(); err != nil {
		panic(err)
	}

	l = NewLimiter(c)

	m.Run()
}

func TestAllow(t *testing.T) {
	ctx := context.Background()

	key := "TestAllow"
	rate := PerSecond(10)
	require.Equal(t, rate.String(), "10 req/s (burst 10)")
	require.False(t, rate.IsZero())

	result, err := l.Allow(ctx, key, rate)
	require.Nil(t, err)
	require.Equal(t, int64(1), result.Allowed)
	require.Equal(t, int64(9), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, 100*time.Millisecond, result.ResetAfter, float64(10*time.Millisecond))

	err = l.Reset(ctx, key)
	require.Nil(t, err)
	result, err = l.Allow(ctx, key, rate)
	require.Nil(t, err)
	require.Equal(t, int64(1), result.Allowed)
	require.Equal(t, int64(9), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, 100*time.Millisecond, result.ResetAfter, float64(10*time.Millisecond))

	result, err = l.AllowN(ctx, key, rate, 2)
	require.Nil(t, err)
	require.Equal(t, int64(2), result.Allowed)
	require.Equal(t, int64(7), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, 300*time.Millisecond, result.ResetAfter, float64(10*time.Millisecond))

	result, err = l.AllowN(ctx, key, rate, 7)
	require.Nil(t, err)
	require.Equal(t, int64(7), result.Allowed)
	require.Equal(t, int64(0), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, time.Second, result.ResetAfter, float64(10*time.Millisecond))

	result, err = l.AllowN(ctx, key, rate, 1000)
	require.Nil(t, err)
	require.Equal(t, int64(0), result.Allowed)
	require.Equal(t, int64(0), result.Remaining)
	require.InDelta(t, 100*time.Second, result.RetryAfter, float64(time.Second))
	require.InDelta(t, time.Second, result.ResetAfter, float64(10*time.Millisecond))
}

func TestAllowN_IncrementZero(t *testing.T) {
	ctx := context.Background()

	key := "TestAllowN_IncrementZero"
	rate := PerSecond(10)

	// check for a row that's not there
	result, err := l.AllowN(ctx, key, rate, 0)
	require.Nil(t, err)
	require.Equal(t, int64(0), result.Allowed)
	require.Equal(t, int64(10), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.Equal(t, time.Duration(0), result.ResetAfter)

	// now increment it
	result, err = l.Allow(ctx, key, rate)
	require.Nil(t, err)
	require.Equal(t, int64(1), result.Allowed)
	require.Equal(t, int64(9), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, 100*time.Millisecond, result.ResetAfter, float64(10*time.Millisecond))

	// peek again
	result, err = l.AllowN(ctx, key, rate, 0)
	require.Nil(t, err)
	require.Equal(t, int64(0), result.Allowed)
	require.Equal(t, int64(9), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, 100*time.Millisecond, result.ResetAfter, float64(10*time.Millisecond))
}

func TestRetryAfter(t *testing.T) {
	ctx := context.Background()

	key := "TestRetryAfter"
	rate := PerDuration(1, time.Millisecond)

	for i := 0; i < 1000; i++ {
		res, err := l.Allow(ctx, key, rate)
		require.Nil(t, err)

		if res.Allowed > 0 {
			continue
		}

		require.LessOrEqual(t, int64(res.RetryAfter), int64(time.Millisecond))
	}
}

func TestAllowAtMost(t *testing.T) {
	ctx := context.Background()

	key := "TestAllowAtMost"
	rate := PerSecond(10)

	result, err := l.Allow(ctx, key, rate)
	require.Nil(t, err)
	require.Equal(t, int64(1), result.Allowed)
	require.Equal(t, int64(9), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, 100*time.Millisecond, result.ResetAfter, float64(10*time.Millisecond))

	result, err = l.AllowAtMost(ctx, key, rate, 2)
	require.Nil(t, err)
	require.Equal(t, int64(2), result.Allowed)
	require.Equal(t, int64(7), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, 300*time.Millisecond, result.ResetAfter, float64(10*time.Millisecond))

	result, err = l.AllowN(ctx, key, rate, 0)
	require.Nil(t, err)
	require.Equal(t, int64(0), result.Allowed)
	require.Equal(t, int64(7), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, 300*time.Millisecond, result.ResetAfter, float64(10*time.Millisecond))

	result, err = l.AllowAtMost(ctx, key, rate, 10)
	require.Nil(t, err)
	require.Equal(t, int64(7), result.Allowed)
	require.Equal(t, int64(0), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, time.Second, result.ResetAfter, float64(10*time.Millisecond))

	result, err = l.AllowN(ctx, key, rate, 0)
	require.Nil(t, err)
	require.Equal(t, int64(0), result.Allowed)
	require.Equal(t, int64(0), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, time.Second, result.ResetAfter, float64(10*time.Millisecond))

	result, err = l.AllowAtMost(ctx, key, rate, 1000)
	require.Nil(t, err)
	require.Equal(t, int64(0), result.Allowed)
	require.Equal(t, int64(0), result.Remaining)
	require.InDelta(t, 100*time.Millisecond, result.RetryAfter, float64(10*time.Millisecond))
	require.InDelta(t, time.Second, result.ResetAfter, float64(10*time.Millisecond))

	result, err = l.AllowN(ctx, key, rate, 1000)
	require.Nil(t, err)
	require.Equal(t, int64(0), result.Allowed)
	require.Equal(t, int64(0), result.Remaining)
	require.InDelta(t, 100*time.Second, result.RetryAfter, float64(time.Second))
	require.InDelta(t, time.Second, result.ResetAfter, float64(10*time.Millisecond))
}

func TestAllowAtMost_IncrementZero(t *testing.T) {
	ctx := context.Background()

	key := "TestAllowAtMost_IncrementZero"
	rate := PerSecond(10)

	// check for a row that isn't there
	result, err := l.AllowAtMost(ctx, key, rate, 0)
	require.Nil(t, err)
	require.Equal(t, int64(0), result.Allowed)
	require.Equal(t, int64(10), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.Equal(t, time.Duration(0), result.ResetAfter)

	// now increment it
	result, err = l.Allow(ctx, key, rate)
	require.Nil(t, err)
	require.Equal(t, int64(1), result.Allowed)
	require.Equal(t, int64(9), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, 100*time.Millisecond, result.ResetAfter, float64(10*time.Millisecond))

	// peek again
	result, err = l.AllowAtMost(ctx, key, rate, 0)
	require.Nil(t, err)
	require.Equal(t, int64(0), result.Allowed)
	require.Equal(t, int64(9), result.Remaining)
	require.Equal(t, time.Duration(-1), result.RetryAfter)
	require.InDelta(t, 100*time.Millisecond, result.ResetAfter, float64(10*time.Millisecond))
}
