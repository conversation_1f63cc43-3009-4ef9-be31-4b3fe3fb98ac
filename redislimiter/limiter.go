package redislimiter

import (
	"context"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
)

// https://en.wikipedia.org/wiki/Generic_cell_rate_algorithm
var (
	allowN = redis.NewScript(
		`
local limit = ARGV[1]
local burst = ARGV[2]
local period = ARGV[3]
local cost = tonumber(ARGV[4])

local emission_interval = period / limit
local increment = emission_interval * cost
local burst_offset = emission_interval * burst

-- redis returns time as an array containing two integers: seconds of the epoch
-- time (10 digits) and microseconds (6 digits). for convenience we need to
-- convert them to a floating point number. the resulting number is 16 digits,
-- bordering on the limits of a 64-bit double-precision floating point number.
-- adjust the epoch to be relative to Jan 1, 2017 00:00:00 GMT to avoid floating
-- point problems. this approach is good until "now" is 2,483,228,799 (Wed, 09
-- Sep 2048 01:46:39 GMT), when the adjusted value is 16 digits.
local jan_1_2017 = 1483228800
local now = redis.call("TIME")
now = (now[1] - jan_1_2017) + (now[2] / 1000000)

-- TAT: Theoretical Arrival Time
local tat = redis.call("GET", KEYS[1])

if not tat then
  tat = now
else
  tat = tonumber(tat)
end

tat = math.max(tat, now)

local new_tat = tat + increment
local allow_at = new_tat - burst_offset

local diff = now - allow_at
local remaining = diff / emission_interval

local retry_after = -1
local reset_after

if remaining < 0 then
  retry_after = diff * -1
  reset_after = tat - now
  
  return {
    0, -- allowed
    0, -- remaining
    tostring(retry_after),
    tostring(reset_after),
  }
end

reset_after = new_tat - now
if reset_after > 0 then
  redis.call("SET", KEYS[1], new_tat, "EX", math.ceil(reset_after))
end

return {cost, remaining, tostring(retry_after), tostring(reset_after)}
`,
	)

	allowAtMost = redis.NewScript(
		`
local limit = ARGV[1]
local burst = ARGV[2]
local period = ARGV[3]
local cost = tonumber(ARGV[4])

local emission_interval = period / limit
local burst_offset = emission_interval * burst

-- redis returns time as an array containing two integers: seconds of the epoch
-- time (10 digits) and microseconds (6 digits). for convenience we need to
-- convert them to a floating point number. the resulting number is 16 digits,
-- bordering on the limits of a 64-bit double-precision floating point number.
-- adjust the epoch to be relative to Jan 1, 2017 00:00:00 GMT to avoid floating
-- point problems. this approach is good until "now" is 2,483,228,799 (Wed, 09
-- Sep 2048 01:46:39 GMT), when the adjusted value is 16 digits.
local jan_1_2017 = 1483228800
local now = redis.call("TIME")
now = (now[1] - jan_1_2017) + (now[2] / 1000000)

-- TAT: Theoretical Arrival Time
local tat = redis.call("GET", KEYS[1])

if not tat then
  tat = now
else
  tat = tonumber(tat)
end

tat = math.max(tat, now)

local diff = now - (tat - burst_offset)
local remaining = diff / emission_interval

local retry_after = -1
local reset_after

if remaining < 1 then
  retry_after = emission_interval - diff
  reset_after = tat - now
  
  return {
    0, -- allowed
    0, -- remaining
    tostring(retry_after),
    tostring(reset_after),
  }
end

if remaining < cost then
  cost = remaining
  remaining = 0
else
  remaining = remaining - cost
end

local increment = emission_interval * cost
local new_tat = tat + increment

reset_after = new_tat - now
if reset_after > 0 then
  redis.call("SET", KEYS[1], new_tat, "EX", math.ceil(reset_after))
end

return {cost, remaining, tostring(retry_after), tostring(reset_after)}
`,
	)
)

// Limiter controls how frequently events are allowed to happen.
type Limiter struct {
	rdb redis.UniversalClient
}

// NewLimiter returns a new Limiter.
func NewLimiter(rdb redis.UniversalClient) *Limiter {
	return &Limiter{
		rdb: rdb,
	}
}

// Allow is a shortcut for AllowN(ctx, key, limit, 1).
func (l *Limiter) Allow(ctx context.Context, key string, rate Rate) (*Result, error) {
	return l.AllowN(ctx, key, rate, 1)
}

// AllowN reports whether n events may happen at time now.
func (l *Limiter) AllowN(ctx context.Context, key string, rate Rate, n int64) (*Result, error) {
	return l.allow(ctx, allowN, key, rate, n)
}

// AllowAtMost reports whether at most n events may happen at time now.
// It returns number of allowed events that is less than or equal to n.
func (l *Limiter) AllowAtMost(ctx context.Context, key string, rate Rate, n int64) (*Result, error) {
	return l.allow(ctx, allowAtMost, key, rate, n)
}

func (l *Limiter) allow(ctx context.Context, script *redis.Script, key string, rate Rate, n int64) (*Result, error) {
	v, err := script.Run(ctx, l.rdb, []string{l.genKey(key)}, rate.limit, rate.burst, rate.period.Seconds(), n).Result()
	if err != nil {
		return nil, err
	}

	vs, ok := v.([]any)
	if !ok {
		return nil, errors.Errorf("invalid type of result, expected []any, but got %T", v)
	} else if len(vs) != 4 {
		return nil, errors.Errorf("the number of result values must be 4")
	}

	allowed, ok := vs[0].(int64)
	if !ok {
		return nil, errors.Errorf("invalid type of \"allowed\", expected: int64, but got %T", vs[0])
	}
	remaining, ok := vs[1].(int64)
	if !ok {
		return nil, errors.Errorf("invalid type of \"remaining\", expected: int64, but got %T", vs[1])
	}
	v2, ok := vs[2].(string)
	if !ok {
		return nil, errors.Errorf("invalid type of \"retry_after\", expected: string, but got: %T", vs[2])
	}
	v3, ok := vs[3].(string)
	if !ok {
		return nil, errors.Errorf("invalid type of \"reset_after\", expected: string, but got: %T", vs[3])
	}

	retryAfter, err := strconv.ParseFloat(v2, 64)
	if err != nil {
		return nil, err
	}

	resetAfter, err := strconv.ParseFloat(v3, 64)
	if err != nil {
		return nil, err
	}

	return &Result{
		Rate:       rate,
		Allowed:    allowed,
		Remaining:  remaining,
		RetryAfter: duration(retryAfter),
		ResetAfter: duration(resetAfter),
	}, nil
}

// Reset gets a key and reset all limitations and previous usages.
func (l *Limiter) Reset(ctx context.Context, key string) error {
	return l.rdb.Del(ctx, l.genKey(key)).Err()
}

func (l *Limiter) genKey(key string) string {
	return constRedisLimiterPrefix + key
}

func duration(f float64) time.Duration {
	if f == -1 {
		return time.Duration(-1)
	}

	return time.Duration(f * float64(time.Second))
}
