package redislimiter

import (
	"fmt"
	"time"
)

type Rate struct {
	limit  int64
	burst  int64
	period time.Duration
}

func (r Rate) Limit() int64 {
	return r.limit
}

func (r Rate) Period() time.Duration {
	return r.period
}

func (r Rate) String() string {
	return fmt.Sprintf("%d req/%s (burst %d)", r.limit, fmtDuration(r.period), r.burst)
}

func (r Rate) IsZero() bool {
	return r.limit == 0 && r.burst == 0 && r.period == 0
}

func fmtDuration(d time.Duration) string {
	switch d {
	case time.Second:
		return constDurationUnitSecond
	case time.Minute:
		return constDurationUnitMinute
	case time.Hour:
		return constDurationUnitHour
	case time.Hour * 24:
		return constDurationUnitDay
	default:
		return d.String()
	}
}

// PerSecond represents a number of requests per second.
func PerSecond(limit int64) Rate {
	return Rate{
		limit:  limit,
		burst:  limit,
		period: time.Second,
	}
}

// PerMinute represents a number of requests per minute.
func PerMinute(limit int64) Rate {
	return Rate{
		limit:  limit,
		burst:  limit,
		period: time.Minute,
	}
}

// PerHour represents a number of requests per hour.
func PerHour(limit int64) Rate {
	return Rate{
		limit:  limit,
		burst:  limit,
		period: time.Hour,
	}
}

// PerDay represents a number of requests per day.
func PerDay(limit int64) Rate {
	return Rate{
		limit:  limit,
		burst:  limit,
		period: time.Hour * 24,
	}
}

// PerDuration represents a number of requests per provided duration.
func PerDuration(limit int64, duration time.Duration) Rate {
	return Rate{
		limit:  limit,
		burst:  limit,
		period: duration,
	}
}
