run:
  go: '1.23'
  issues-exit-code: 1
  modules-download-mode: readonly
  tests: false
  timeout: 5m

linters-settings:
  copyloopvar:
    check-alias: true
  depguard:
    rules:
      prevent_unmaintained_packages:
        listMode: lax # allow unless explicitely denied
        files:
          - $all
          - "!$test"
        deny:
          - pkg: io/ioutil
            desc: "replaced by io and os packages since Go 1.16: https://tip.golang.org/doc/go1.16#ioutil"
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport # https://github.com/go-critic/go-critic/issues/845
      - ifElseChain
      - octalLiteral
      - whyNoLint
  gocyclo:
    min-complexity: 24
  gofmt:
    rewrite-rules:
      - pattern: 'interface{}'
        replacement: 'any'
      - pattern: 'a[b:len(a)]'
        replacement: 'a[b:]'
  gofumpt:
    module-path: gitlab.ttyuyin.com/TestDevelopment/probe-backend
    extra-rules: true
  goimports:
    local-prefixes: gitlab.ttyuyin.com/TestDevelopment
  gomnd:
    # don't include the "operation" and "assign"
    checks:
      - argument
      - case
      - condition
      - return
    ignored-numbers:
      - '0'
      - '1'
      - '2'
      - '3'
    ignored-functions:
      - strings.SplitN
  govet:
    enable-all: true
    disable:
      - asmdecl
      - atomicalign
      - cgocall
      - fieldalignment
      - shadow
  lll:
    line-length: 200
  misspell:
    locale: US
  #  nakedret:
  #    command: nakedret
  #    pattern: ^(?P<path>.*?\\.go):(?P<line>\\d+)\\s*(?P<message>.*)$
  stylecheck:
    checks:
      - '-ST1003' # disable the rule ST1003

linters:
  disable-all: true
  enable:
    - asciicheck
    - bodyclose
    - copyloopvar
    - depguard
    - dogsled
    - durationcheck
    - errcheck
    - gocyclo
    - gofmt
    - goimports
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - makezero
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - predeclared
    - staticcheck
    - stylecheck
    - unconvert
    - unparam
    - whitespace

#  disable:
#    - dupl
#    - deadcode
#    - funlen
#    - gochecknoinits
#    - goconst
#    - gocritic
#    - gomnd
#    - lll
#    - prealloc
#    - structcheck
#    - typecheck
#    - unused
#    - varcheck

issues:
  # 参考 https://github.com/golangci/golangci-lint/issues/1684
  # Maximum issues count per one linter. Set to 0 to disable. Default is 50.
  max-issues-per-linter: 0
  # Maximum count of issues with the same text. Set to 0 to disable. Default is 3.
  max-same-issues: 0

  # The default exclusion rules are a bit too permissive, so copying the relevant ones below
  exclude-use-default: false

  exclude:
    - parameter .* always receives

  exclude-dirs:
    - goctltemplate
    - .*/sql

  exclude-rules:
    # These are copied from the default exclude rules, except for "ineffective break statement"
    # and GoDoc checks.
    # https://github.com/golangci/golangci-lint/blob/0cc87df732aaf1d5ad9ce9ca538d38d916918b36/pkg/config/config.go#L36
    - text: "Error return value of .((os\\.)?std(out|err)\\..*|.*Close|.*Flush|os\\.Remove(All)?|.*printf?|os\\.(Un)?Setenv). is not checked"
      linters:
        - errcheck
    - text: "func name will be used as test\\.Test.* by other packages, and that stutters; consider calling this"
      linters:
        - golint
    - text: "G103: Use of unsafe calls should be audited"
      linters:
        - gosec
    - text: "G104: Errors unhandled"
      linters:
        - gosec
    - text: "G115: integer overflow conversion "
      linters:
        - gosec
    - text: "(G201|G202): SQL string (formatting|concatenation)"
      linters:
        - gosec
    - text: "G204: Subprocess launch(ed with (variable|function call)|ing should be audited)"
      linters:
        - gosec
    - text: "(G301|G302): (Expect directory permissions to be 0750 or less|Expect file permissions to be 0600 or less)"
      linters:
        - gosec
    - text: "G304: Potential file inclusion via variable"
      linters:
        - gosec
    - text: "G401: Use of weak cryptographic primitive"
      linters:
        - gosec
    - text: "G404: Use of weak random number generator"
      linters:
        - gosec
    - text: "G501: Blocklisted import crypto/md5: weak cryptographic primitive"
      linters:
        - gosec
    - text: "SA1019: "
      linters:
        - staticcheck
    - text: "SA5008: unknown JSON option "
      linters:
        - staticcheck
    - text: "The copy of the 'for' variable "
      linters:
        - copyloopvar
