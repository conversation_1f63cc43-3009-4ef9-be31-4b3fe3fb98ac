package middlewares

import (
	"fmt"
	"net/http"
	"regexp"
	"sync"
	"time"

	jwt4 "github.com/golang-jwt/jwt/v4"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/jwt"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

var skipUrls sync.Map

func AuthHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		var claims *jwt.UniqueClaims
		var err error

		// 判断url，如登录、刷新则跳过
		_, ok := skipUrls.Load(r.URL.Path)
		if ok {
			claims = &jwt.UniqueClaims{
				TokenUserInfo: userinfo.TokenUserInfo{Account: userinfo.System().Account},
			}
		} else {
			tokenStr := r.Header.Get("X-AUTH")
			switch tokenStr {
			case "":
				account := r.Header.Get("X-ACCOUNT")
				if account == "" {
					httpx.WriteJson(w, http.StatusUnauthorized, response.CommonResponse{
						Code:    uint32(errorx.NeedLogin),
						Message: "Header does not have X-AUTH",
					})
					return
				}

				isMatch, _ := regexp.MatchString("^[S|T]\\d+$", account)
				if !isMatch {
					httpx.WriteJson(w, http.StatusUnauthorized, response.CommonResponse{
						Code:    uint32(errorx.NeedLogin),
						Message: "The format of X-ACCOUNT is incorrect",
					})
					return
				}

				now := jwt4.TimeFunc()
				var g int64 = 365 * 24 * 60 * 60

				claims = &jwt.UniqueClaims{
					RegisteredClaims: jwt4.RegisteredClaims{
						ExpiresAt: jwt4.NewNumericDate(now.Add(time.Duration(g) * time.Second)),
						NotBefore: jwt4.NewNumericDate(now),
						IssuedAt:  jwt4.NewNumericDate(now),
						ID:        utils.GenNanoId(""),
					},
					TokenUserInfo: userinfo.TokenUserInfo{Account: account},
					RefreshAfter:  jwt4.NewNumericDate(now.Add(time.Duration(g) * time.Second)),
				}

			default:
				claims, err = jwt.ParseToken(tokenStr)
				if err != nil {
					httpx.WriteJson(w, http.StatusUnauthorized, response.CommonResponse{
						Code:    uint32(errorx.NeedLogin),
						Message: fmt.Sprintf("Failed to parse token, error: %s", err.Error()),
					})
					return
				}

				err2 := claims.Check()
				if err2 != nil {
					httpx.WriteJson(w, http.StatusUnauthorized, response.CommonResponse{
						Code:    uint32(err2.Code()),
						Message: err2.Message(),
					})
					return
				}
			}
		}

		ctx := userinfo.WithContext(r.Context(), &claims.TokenUserInfo)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func AuthMiddleware() rest.Middleware {
	return rest.ToMiddleware(AuthHandler)
}

func SkipForUrl(url string) {
	skipUrls.Store(url, lang.Placeholder)
}
