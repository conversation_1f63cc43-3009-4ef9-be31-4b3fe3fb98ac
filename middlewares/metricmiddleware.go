package middlewares

import (
	"net/http"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/zeromicro/go-zero/core/timex"
	"github.com/zeromicro/go-zero/rest"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/metrics"
)

type MetricResponseWriter struct {
	Writer       http.ResponseWriter
	Code         int
	ProbeCode    int
	ProbeMessage string
}

// Flush flushes the response writer.
func (w *MetricResponseWriter) Flush() {
	if flusher, ok := w.Writer.(http.Flusher); ok {
		flusher.Flush()
	}
}

// Header returns the http header.
func (w *MetricResponseWriter) Header() http.Header {
	return w.Writer.Header()
}

// Write writes bytes into w.
func (w *MetricResponseWriter) Write(bytes []byte) (int, error) {
	return w.Writer.Write(bytes)
}

// WriteHeader writes code into w, and not sealing the writer.
func (w *MetricResponseWriter) WriteHeader(code int) {
	w.Writer.WriteHeader(code)
	w.Code = code
}

const serverNamespace = "qet"

var (
	metricServerReqDur = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: serverNamespace,
			Subsystem: "http",
			Name:      "request_duration_seconds",
			Help:      "http server requests duration(seconds)!",
			Buckets:   []float64{5, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000},
		}, []string{"path"},
	)

	metricServerReqCodeTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: serverNamespace,
			Subsystem: "http",
			Name:      "request_total",
			Help:      "http server requests count!",
		}, []string{"path", "code", "error_type"},
	)
)

func MetricHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(
		func(w http.ResponseWriter, r *http.Request) {
			path := r.RequestURI
			startTime := timex.Now()
			metricObj := new(metrics.MetricObj)

			defer func() {
				metricServerReqDur.WithLabelValues(path).Observe(float64(timex.Since(startTime) / time.Second))
				if result := recover(); result != nil {
					metricServerReqCodeTotal.WithLabelValues(
						path, strconv.Itoa(int(errorx.InternalError)), constants.SystemError,
					).Inc()
					panic(result)
				}
			}()

			next.ServeHTTP(w, r.WithContext(metrics.WithMetricObject(r.Context(), metricObj)))

			if int(metricObj.Code) == http.StatusOK {
				metricServerReqCodeTotal.WithLabelValues(
					path, strconv.Itoa(int(metricObj.Code)), constants.EmptyError,
				).Inc()
			} else {
				errorType := constants.BusinessError
				if errorx.IsSystemError(errorx.Code(metricObj.Code)) {
					errorType = constants.SystemError
				}
				metricServerReqCodeTotal.WithLabelValues(path, strconv.Itoa(int(metricObj.Code)), errorType).Inc()
			}
		},
	)
}

func MetricMiddleware() rest.Middleware {
	return rest.ToMiddleware(MetricHandler)
}
