package middlewares

import (
	"net/http"
	"runtime/debug"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"
)

// RecoverHandler returns a middleware that recovers if panic happens.
func RecoverHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if result := recover(); result != nil {
				logx.WithContext(r.Context()).Errorf("(%s - %s) %v\n%s", r.RequestURI, httpx.GetRemoteAddr(r), result, debug.Stack())
				httpx.WriteJson(w, http.StatusInternalServerError, response.InternalError())
			}
		}()

		next.ServeHTTP(w, r)
	})
}

// RecoverMiddleware returns a middleware that recovers if panic happens.
func RecoverMiddleware() rest.Middleware {
	return rest.ToMiddleware(RecoverHandler)
}
