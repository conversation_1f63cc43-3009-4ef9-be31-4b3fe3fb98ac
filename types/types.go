package types

import "time"

type ValidatorConfig struct {
	Locale string `json:",default=en,options=en|zh"`
}

type DBConfig struct {
	DataSource string
}

type DBModel interface {
	Table() string
	Fields() []string
}

type Scheduler interface {
	// Next 返回下一次执行的时间
	Next(time.Time) time.Time
}

// GitLabConfig GitLab配置
type GitLabConfig struct {
	BaseURL string `json:",default=https://gitlab.ttyuyin.com"`
	Token   string
}

// LarkCustomApp 飞书自建应用配置
type LarkCustomApp struct {
	AppID             string
	AppSecret         string
	VerificationToken string `json:",optional"`
	EventEncryptKey   string `json:",optional"`
}
