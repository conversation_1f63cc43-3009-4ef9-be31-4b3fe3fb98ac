
MAKEFILE_DIR := $(patsubst %/,%,$(dir $(abspath $(lastword $(MAKEFILE_LIST)))))
GITLAB_GROUP := "gitlab.ttyuyin.com/TestDevelopment"

# check if `gofumpt` command exists
GOFUMPT_EXISTS := $(shell command -v gofumpt >/dev/null 2>&1 && echo 1 || echo 0)

GO_FORMAT_CMD := gofmt -s -w
ifeq (1, $(GOFUMPT_EXISTS))
GO_FORMAT_CMD = gofumpt -l -w -extra
endif

.PHONY: fmt
fmt:
	@go list -f {{.Dir}} $(MAKEFILE_DIR)/... | xargs $(GO_FORMAT_CMD)
	@goimports -l -w -local $(GITLAB_GROUP) $(MAKEFILE_DIR)

.PHONY: lint
lint:
	@golangci-lint run -c $(MAKEFILE_DIR)/.golangci.yaml
