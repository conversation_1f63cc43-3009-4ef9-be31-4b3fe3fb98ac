package jwt

import (
	"time"

	"github.com/golang-jwt/jwt/v4"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var jwtSecret = []byte("here and tt xxxxx no")

type UniqueClaims struct {
	jwt.RegisteredClaims

	userinfo.TokenUserInfo
	RefreshAfter *jwt.NumericDate `json:"refresh_after"`
}

func (c *UniqueClaims) Check() *errorx.Error {
	var err *errorx.Error
	now := jwt.TimeFunc()

	// 是否过期
	if !c.VerifyExpiresAt(now, false) {
		// 是否可刷新
		if !c.VerifyRefreshAfter(now, false) {
			err = errorx.New(errorx.NeedLogin, "token cannot be refreshed, you need to log in again")
		} else {
			err = errorx.New(errorx.NeedRefreshToken, "token is expired, you need to refresh it again")
		}
	}

	if !c.VerifyIssuedAt(now, false) {
		err = errorx.New(errorx.AuthError, "token used before issued")
	}

	if !c.VerifyNotBefore(now, false) {
		err = errorx.New(errorx.AuthError, "token is not valid yet")
	}
	return err
}

func (c *UniqueClaims) Valid() error {
	return nil
}

func (c *UniqueClaims) VerifyRefreshAfter(cmp time.Time, req bool) bool {
	if c.RefreshAfter == nil {
		return verifyExp(nil, cmp, req)
	}

	return verifyExp(&c.RefreshAfter.Time, cmp, req)
}

func verifyExp(exp *time.Time, now time.Time, required bool) bool {
	if exp == nil {
		return !required
	}
	return now.Before(*exp)
}

//func verifyExp(exp int64, now int64, required bool) bool {
//	if exp == 0 {
//		return !required
//	}
//	return now <= exp
//}

func GenerateToken(claims *UniqueClaims) (string, error) {
	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	token, err := tokenClaims.SignedString(jwtSecret)

	return token, err
}

func ParseToken(token string) (*UniqueClaims, error) {
	claims := &UniqueClaims{}
	tokenClaims, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (any, error) {
		return jwtSecret, nil
	})
	if err != nil {
		return nil, err
	}

	if tokenClaims != nil {
		if v, ok := tokenClaims.Claims.(*UniqueClaims); ok && tokenClaims.Valid {
			return v, nil
		}
	}
	return claims, err
}
