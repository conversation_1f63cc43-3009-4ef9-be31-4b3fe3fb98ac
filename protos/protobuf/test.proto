syntax = "proto3";

package test;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf";

import "protobuf/options.proto";


enum TestEnum {
  TE_NULL = 0;
  TE_RED = 1 [(options.enum_value_alias) = "red"];
  TE_BLUE = 2 [(options.enum_value_alias) = "Blue"];
  TE_YELLOW = 3 [(options.enum_value_alias) = "YELLOW"];
  TE_GREEN = 4 [(options.enum_value_alias) = "PASS"];
}
