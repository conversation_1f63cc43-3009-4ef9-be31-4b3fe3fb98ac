project=$(basename $(dirname $(realpath $0)))
base_path=$(dirname $(dirname $(dirname $(realpath $0))))
echo "Project: ${project}\nBase Path: ${base_path}\n"

module=""

function current_module() {
    module=$(grep -E "^module" "${base_path}/go.mod" | awk '{print $2}')
    if [ "${module}" == "" ]; then
      echo "current module not found, please check the file[${base_path}/go.mod]"
      exit 1
    fi
    echo "current module: ${module}"
}

function rpc() {
  input_path="${base_path}/protos/${project}"
  output_path="${base_path}/${project}"
  module_prefix="${module}/${project}"

  third_party_proto_paths=$(go list -m -f '{{.Dir}}' 'github.com/envoyproxy/protoc-gen-validate' | xargs -I {} echo -n "--proto_path='{}' ")
  private_proto_paths=$(go list -m -f '{{.Dir}}' ${module} | xargs -I {} echo -n "--proto_path='{}/protos' ")

  protoc_cmd="protoc ${third_party_proto_paths} ${private_proto_paths} --go_out=${output_path} --go_opt=module=${module_prefix} ${input_path}/*.proto"
  format_cmd="go list -f {{.Dir}} ${output_path}/... | xargs gofmt -s -w; goimports -l -w -local ${module%[\\/]*} ${output_path}/"

  eval ${protoc_cmd}
  eval ${format_cmd}
}

current_module
rpc
