syntax = "proto3";

package sqlbuilder;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc";

import "validate/validate.proto";


message Condition {
  SingleCondition single = 1;
  GroupCondition group = 2;
}

message SingleCondition {
  string field = 1 [(validate.rules).string.min_len = 1]; // 字段名称
  string compare = 2 [(validate.rules).string = {ignore_empty: true, in: ["EQ", "NE", "LT", "LE", "GT", "GE", "IN", "NOT_IN", "LIKE", "NOT_LIKE", "BETWEEN", "JSON_CONTAINS", "NOT_JSON_CONTAINS", "IS_NULL", "IS_NOT_NULL"]}]; // 比较操作
  repeated string in = 3 [(validate.rules).repeated.ignore_empty = true];
  Between between = 4;
  Other other = 5;
}
message GroupCondition {
  string relationship = 1 [(validate.rules).string = {in: ["AND", "OR"]}]; // 关系（条件组中各条件的关系）
  repeated Condition conditions = 2 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 条件
}
message Between {
  string start = 1;
  string end = 2;
}
message Other {
  string value = 1;
}

// Pagination 分页
message Pagination {
  uint64 current_page = 1 [(validate.rules).uint64 = {ignore_empty: true, gte: 1}]; // 当前页码
  uint64 page_size = 2 [(validate.rules).uint64 = {ignore_empty: true, gte: 1}]; // 每页的大小
}

// SortField 排序字段
message SortField {
  string field = 1 [(validate.rules).string.min_len = 1]; // 排序字段名称
  string order = 2 [(validate.rules).string = {ignore_empty: true, in: ["ASC", "DESC"]}]; // 排序方式，ASC：升序、DESC：降序
}
