package redislock

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

const (
	defaultExpire   = 5 * time.Second
	defaultInterval = 500 * time.Millisecond
)

type Option func(l *RedisLock)

func WithExpire(expire time.Duration) Option {
	return func(l *RedisLock) {
		l.expire = expire
	}
}

func WithValue(value string) Option {
	return func(l *RedisLock) {
		l.value = value
	}
}

func WithTimeout(timeout time.Duration) Option {
	return func(l *RedisLock) {
		l.timeout = timeout
	}
}

func WithInterval(interval time.Duration) Option {
	return func(l *RedisLock) {
		l.interval = interval
	}
}

type RedisLock struct {
	key      string
	value    string
	expire   time.Duration
	timeout  time.Duration
	interval time.Duration

	lock *redis.RedisLock
}

func NewRedisLock(r *redis.Redis, key string, options ...Option) *RedisLock {
	l := &RedisLock{
		key:      key,
		expire:   defaultExpire,
		interval: defaultInterval,
	}

	for _, option := range options {
		option(l)
	}

	opts := make([]redis.RLOption, 0, len(options))
	if l.value != "" {
		opts = append(opts, redis.WithValue(l.value))
	}
	if l.expire > 0 {
		opts = append(opts, redis.WithExpire(int(l.expire/time.Second)))
	}
	l.lock = redis.NewRedisLockWithOptions(r, key, opts...)

	return l
}

func NewRedisLockAndAcquire(r *redis.Redis, key string, options ...Option) (lock *RedisLock, err error) {
	lock = NewRedisLock(r, key, options...)
	err = lock.Acquire()
	return
}

func (l *RedisLock) Acquire() error {
	if l.timeout > 0 {
		timer := time.NewTimer(l.timeout)
		defer timer.Stop()

		// 降速
		if l.interval == 0 {
			l.interval = defaultInterval
		}

		for {
			select {
			case <-timer.C:
				return errors.WithStack(
					errorx.Err(
						errorx.AcquireRedisLockFailure,
						fmt.Sprintf("timeout to acquire the redis lock with key[%s], please try it later", l.key),
					),
				)
			default:
				if ok, err := l.lock.Acquire(); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.RedisError, err.Error()),
						"failed to acquire the redis lock with key[%s], error: %+v", l.key, err,
					)
				} else if !ok {
					logx.Warnf(
						"cannot acquire the redis lock with key[%s], it will be retried in %s", l.key,
						l.interval.String(),
					)
					time.Sleep(l.interval)
				} else {
					return nil
				}
			}
		}
	} else {
		if ok, err := l.lock.Acquire(); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.RedisError, err.Error()),
				"failed to acquire the redis lock with key[%s], error: %+v", l.key, err,
			)
		} else if !ok {
			return errors.WithStack(
				errorx.Err(
					errorx.AcquireRedisLockFailure,
					fmt.Sprintf("cannot acquire the redis lock with key[%s], please try it later", l.key),
				),
			)
		}

		return nil
	}
}

func (l *RedisLock) Release() error {
	if ok, err := l.lock.Release(); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.RedisError, err.Error()),
			"failed to release the redis lock with key[%s], error: %+v", l.key, err,
		)
	} else if !ok {
		return errors.WithStack(
			errorx.Err(
				errorx.ReleaseRedisLockFailure,
				fmt.Sprintf("cannot release the reids lock with key[%s], the lock may has been released", l.key),
			),
		)
	}

	return nil
}
