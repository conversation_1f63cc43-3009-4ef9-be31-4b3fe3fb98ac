package db

import (
	"sync"

	"github.com/pkg/errors"
)

var (
	adapterMap      = make(map[string]Adapter)
	adapterMapMutex sync.RWMutex

	missingAdapterNameError = errors.New("missing adapter name when register adapter")
)

// Adapter interface defines an adapter
type Adapter interface {
	Open(DSN) (Session, error)
}

// RegisterAdapter registers a generic database adapter.
func RegisterAdapter(name string, adapter Adapter) error {
	adapterMapMutex.Lock()
	defer adapterMapMutex.Unlock()

	if name == "" {
		return missingAdapterNameError
	}
	if _, ok := adapterMap[name]; ok {
		return errors.Errorf("this adapter[%s] is repeatedly registered", name)
	}
	adapterMap[name] = adapter

	return nil
}

// LookupAdapter returns a previously registered adapter by name.
func LookupAdapter(name string) (Adapter, error) {
	adapterMapMutex.RLock()
	defer adapterMapMutex.RUnlock()

	if adapter, ok := adapterMap[name]; ok {
		return adapter, nil
	}
	return nil, errors.Errorf("this adapter[%s] was not found", name)
}

// Open create a connection with a database.
func Open(adapterName string, dsn DSN) (Session, error) {
	adapter, err := LookupAdapter(adapterName)
	if err != nil {
		return nil, err
	}

	return adapter.Open(dsn)
}
