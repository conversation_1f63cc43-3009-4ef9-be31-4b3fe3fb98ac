package db

import (
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

// Session is an interface that defines methods for database adapters.
type Session interface {
	sqlx.SqlConn

	// DSN returns the DSN that was used to set up the adapter.
	DSN() DSN

	// Name returns the name of the database.
	Name() string

	// Ping returns an error if the DBMS could not be reached.
	Ping() error

	// Close terminates the currently active connection to the DBMS and clears
	// all caches.
	Close() error

	// Driver returns the underlying driver of the adapter as an interface.
	//
	// In order to actually use the driver, the `interface{}` value needs to be cast into the appropriate type.
	//
	// Example:
	//  internalSQLDriver := sess.Driver().(*sql.DB)
	Driver() any
}
