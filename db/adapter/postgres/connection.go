package postgres

import (
	"fmt"
	"net"
	"net/url"
	"sort"
	"strings"
	"time"
	"unicode"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/db"
)

const (
	defaultServerPort = "5432"
)

// scanner implements a tokenizer for libpq-style option strings.
type scanner struct {
	s []rune
	i int
}

// Next returns the next rune.  It returns 0, false if the end of the text has
// been reached.
func (s *scanner) Next() (rune, bool) {
	if s.i >= len(s.s) {
		return 0, false
	}
	r := s.s[s.i]
	s.i++
	return r, true
}

// SkipSpaces returns the next non-whitespace rune.  It returns 0, false if the
// end of the text has been reached.
func (s *scanner) SkipSpaces() (rune, bool) {
	r, ok := s.Next()
	for unicode.IsSpace(r) && ok {
		r, ok = s.Next()
	}
	return r, ok
}

type values map[string]string

func (vs values) Set(k, v string) {
	vs[k] = v
}

func (vs values) Get(k string) (v string) {
	return vs[k]
}

func (vs values) Isset(k string) bool {
	_, ok := vs[k]
	return ok
}

type ConnectionURL struct {
	db.ConnectionURL

	Socket  string            `json:"socket"`
	Options map[string]string `json:"options"`

	timezone *time.Location
}

var escaper = strings.NewReplacer(` `, `\ `, `'`, `\'`, `\`, `\\`)

// ParseURL parses the given DSN into a ConnectionURL struct.
// A typical PostgreSQL connection URL looks like:
//
// **********************************/mydb?sslmode=verify-full
func ParseURL(s string) (conn ConnectionURL, err error) {
	o := make(values)

	if strings.HasPrefix(s, "postgres://") || strings.HasPrefix(s, "postgresql://") {
		s, err = parseURL(s)
		if err != nil {
			return conn, err
		}
	}

	if err = parseOpts(s, o); err != nil {
		return conn, err
	}

	conn.User = o.Get("user")
	conn.Password = o.Get("password")

	h := o.Get("host")
	p := o.Get("port")

	if strings.HasPrefix(h, "/") {
		conn.Socket = h
	} else {
		conn.Host = h
		if p == "" {
			conn.Port = defaultServerPort
		} else {
			conn.Port = p
		}
	}

	conn.Database = o.Get("dbname")

	conn.Options = make(map[string]string)

	for k := range o {
		switch k {
		case "user", "password", "host", "port", "dbname":
			// Skip
		default:
			conn.Options[k] = o[k]
		}
	}

	if timezone, ok := conn.Options["timezone"]; ok {
		conn.timezone, _ = time.LoadLocation(timezone)
	}

	return conn, err
}

// parseOpts parses the options from name and adds them to the values.
//
// The parsing code is based on conninfo_parse from libpq's fe-connect.c
func parseOpts(name string, o values) error {
	s := newScanner(name)

	for {
		var (
			keyRunes, valRunes []rune
			r                  rune
			ok                 bool
		)

		if r, ok = s.SkipSpaces(); !ok {
			break
		}

		// Scan the key
		for !unicode.IsSpace(r) && r != '=' {
			keyRunes = append(keyRunes, r)
			if r, ok = s.Next(); !ok {
				break
			}
		}

		// Skip any whitespace if we're not at the = yet
		if r != '=' {
			r, ok = s.SkipSpaces()
		}

		// The current character should be =
		if r != '=' || !ok {
			return fmt.Errorf(`missing "=" after %q in connection info string"`, string(keyRunes))
		}

		// Skip any whitespace after the =
		if r, ok = s.SkipSpaces(); !ok {
			// If we reach the end here, the last value is just an empty string as per libpq.
			o.Set(string(keyRunes), "")
			break
		}

		if r != '\'' {
			for !unicode.IsSpace(r) {
				if r == '\\' {
					if r, ok = s.Next(); !ok {
						return fmt.Errorf(`missing character after backslash`)
					}
				}
				valRunes = append(valRunes, r)

				if r, ok = s.Next(); !ok {
					break
				}
			}
		} else {
		quote:
			for {
				if r, ok = s.Next(); !ok {
					return fmt.Errorf(`unterminated quoted string literal in connection string`)
				}
				switch r {
				case '\'':
					break quote
				case '\\':
					r, _ = s.Next()
					fallthrough
				default:
					valRunes = append(valRunes, r)
				}
			}
		}

		o.Set(string(keyRunes), string(valRunes))
	}

	return nil
}

// newScanner returns a new scanner initialized with the option string s.
func newScanner(s string) *scanner {
	return &scanner{[]rune(s), 0}
}

// ParseURL no longer needs to be used by clients of this library since supplying a URL as a
// connection string to sql.Open() is now supported:
//
// sql.Open("postgres", "**********************************/mydb?sslmode=verify-full")
//
// It remains exported here for backwards-compatibility.
//
// ParseURL converts a url to a connection string for driver.Open.
// Example:
//
// "**********************************/mydb?sslmode=verify-full"
//
// converts to:
//
// "user=bob password=secret host=******* port=5432 dbname=mydb sslmode=verify-full"
//
// A minimal example:
//
// "postgres://"
//
// this will be blank, causing driver.Open to use all the defaults
//
// NOTE: vendored/copied from github.com/lib/pq
func parseURL(uri string) (string, error) {
	u, err := url.Parse(uri)
	if err != nil {
		return "", err
	}

	if u.Scheme != "postgres" && u.Scheme != "postgresql" {
		return "", fmt.Errorf("invalid connection protocol: %s", u.Scheme)
	}

	var kvs []string
	escaper := strings.NewReplacer(` `, `\ `, `'`, `\'`, `\`, `\\`)
	accrue := func(k, v string) {
		if v != "" {
			kvs = append(kvs, k+"="+escaper.Replace(v))
		}
	}

	if u.User != nil {
		v := u.User.Username()
		accrue("user", v)

		v, _ = u.User.Password()
		accrue("password", v)
	}

	if host, port, err := net.SplitHostPort(u.Host); err != nil {
		accrue("host", u.Host)
	} else {
		accrue("host", host)
		accrue("port", port)
	}

	if u.Path != "" {
		accrue("dbname", u.Path[1:])
	}

	q := u.Query()
	for k := range q {
		accrue(k, q.Get(k))
	}

	sort.Strings(kvs) // Makes testing easier (not a performance concern)
	return strings.Join(kvs, " "), nil
}

// String reassembles the parsed PostgreSQL connection URL into a valid DSN.
func (c ConnectionURL) String() (s string) {
	var u []string

	// TODO: This surely needs some sort of escaping.
	if c.User != "" {
		u = append(u, "user="+escaper.Replace(c.User))
	}

	if c.Password != "" {
		u = append(u, "password="+escaper.Replace(c.Password))
	}

	if c.Host != "" {
		u = append(u, "host="+escaper.Replace(c.Host))
	}

	if c.Port != "" {
		u = append(u, "port="+escaper.Replace(c.Port))
	} else {
		u = append(u, "port="+escaper.Replace(defaultServerPort))
	}

	if c.Socket != "" {
		u = append(u, "host="+escaper.Replace(c.Socket))
	}

	if c.Database != "" {
		u = append(u, "dbname="+escaper.Replace(c.Database))
	}

	// Is there actually any connection data?
	if len(u) == 0 {
		return ""
	}

	if c.Options == nil {
		c.Options = map[string]string{}
	}

	// If not present, SSL mode is assumed "prefer".
	if sslMode, ok := c.Options["sslmode"]; !ok || sslMode == "" {
		c.Options["sslmode"] = "prefer"
	}

	// Disabled by default
	c.Options["statement_cache_capacity"] = "0"

	for k, v := range c.Options {
		u = append(u, escaper.Replace(k)+"="+escaper.Replace(v))
	}

	sort.Strings(u)

	return strings.Join(u, " ")
}
