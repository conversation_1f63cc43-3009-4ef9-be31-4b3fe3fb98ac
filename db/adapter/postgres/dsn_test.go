package postgres

import (
	"reflect"
	"testing"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/db"
)

func TestConnectionURL_FormatDSN(t *testing.T) {
	type fields struct {
		ConnectionURL db.ConnectionURL
		Options       map[string]string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "",
			fields: fields{
				ConnectionURL: db.ConnectionURL{
					Host:     "localhost",
					Port:     defaultServerPort,
					User:     "anakin",
					Password: "skywalker",
					Database: "jedis",
				},
			},
			// 默认添加：sslmode=prefer statement_cache_capacity=0
			want: "dbname=jedis host=localhost password=skywalker port=5432 sslmode=prefer statement_cache_capacity=0 user=anakin",
		},
		{
			name: "",
			fields: fields{
				ConnectionURL: db.ConnectionURL{
					Host:     "127.0.0.1",
					Port:     "5432",
					User:     "root",
					Password: "Quwan@2020",
					Database: "manager",
				},
				Options: map[string]string{
					"sslmode": "verify-full",
				},
			},
			// 默认添加：statement_cache_capacity=0
			want: "dbname=manager host=127.0.0.1 password=Quwan@2020 port=5432 sslmode=verify-full statement_cache_capacity=0 user=root",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := ConnectionURL{
				ConnectionURL: tt.fields.ConnectionURL,
				Options:       tt.fields.Options,
			}
			if got := c.FormatDSN(); got != tt.want {
				t.Errorf("FormatDSN() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParseDSN(t *testing.T) {
	type args struct {
		dsn string
	}
	tests := []struct {
		name    string
		args    args
		want    ConnectionURL
		wantErr bool
	}{
		{
			name: "",
			args: args{"postgres://anakin:skywalker@localhost/jedis"},
			want: ConnectionURL{
				ConnectionURL: db.ConnectionURL{
					Host:     "localhost",
					Port:     defaultServerPort,
					User:     "anakin",
					Password: "skywalker",
					Database: "jedis",
				},
				Options: map[string]string{},
			},
			wantErr: false,
		},
		{
			name: "",
			args: args{"postgres://root:Quwan@2020@127.0.0.1:5432/manager?sslmode=verify-full"},
			want: ConnectionURL{
				ConnectionURL: db.ConnectionURL{
					Host:     "127.0.0.1",
					Port:     "5432",
					User:     "root",
					Password: "Quwan@2020",
					Database: "manager",
				},
				Options: map[string]string{
					"sslmode": "verify-full",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseDSN(tt.args.dsn)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseDSN() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseDSN() got = %v, want %v", got, tt.want)
			}
		})
	}
}
