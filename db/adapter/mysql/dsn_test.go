package mysql

import (
	"reflect"
	"testing"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/db"
)

func TestConnectionURL_FormatDSN(t *testing.T) {
	type fields struct {
		ConnectionURL db.ConnectionURL
		Socket        string
		Options       map[string]string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "",
			fields: fields{
				ConnectionURL: db.ConnectionURL{
					Host:     "address",
					User:     "username",
					Password: "password",
					Database: "dbname",
				},
				Options: map[string]string{
					"param": "value",
				},
			},
			want: "username:password@tcp(address:3306)/dbname?charset=utf8mb4&param=value&parseTime=true",
		},
		{
			name: "",
			fields: fields{
				ConnectionURL: db.ConnectionURL{
					Host:     "127.0.0.1",
					Port:     "3306",
					User:     "root",
					Password: "Quwan@2020",
					Database: "manager",
				},
				Options: map[string]string{
					"charset":   "utf8mb4",
					"parseTime": "true",
					"loc":       "Asia/Shanghai",
				},
			},
			want: "root:Quwan@2020@tcp(127.0.0.1:3306)/manager?charset=utf8mb4&loc=Asia%2FShanghai&parseTime=true",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := ConnectionURL{
				ConnectionURL: tt.fields.ConnectionURL,
				Socket:        tt.fields.Socket,
				Options:       tt.fields.Options,
			}
			if got := c.FormatDSN(); got != tt.want {
				t.Errorf("FormatDSN() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParseDSN(t *testing.T) {
	type args struct {
		dsn string
	}
	tests := []struct {
		name    string
		args    args
		want    ConnectionURL
		wantErr bool
	}{
		{
			name: "",
			args: args{"username:password@unix(/path/to/socket)/dbname?param=value"},
			want: ConnectionURL{
				ConnectionURL: db.ConnectionURL{
					User:     "username",
					Password: "password",
					Database: "dbname",
				},
				Socket: "/path/to/socket",
				Options: map[string]string{
					"param": "value",
				},
			},
			wantErr: false,
		},
		{
			name: "",
			args: args{"user:password@tcp(localhost:5555)/dbname?charset=utf8mb4,utf8&tls=skip-verify"},
			want: ConnectionURL{
				ConnectionURL: db.ConnectionURL{
					Host:     "localhost",
					Port:     "5555",
					User:     "user",
					Password: "password",
					Database: "dbname",
				},
				Options: map[string]string{
					"charset": "utf8mb4,utf8",
					"tls":     "skip-verify",
				},
			},
			wantErr: false,
		},
		{
			name: "",
			args: args{"root:Quwan@2020@tcp(127.0.0.1:3306)/manager?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai"},
			want: ConnectionURL{
				ConnectionURL: db.ConnectionURL{
					Host:     "127.0.0.1",
					Port:     "3306",
					User:     "root",
					Password: "Quwan@2020",
					Database: "manager",
				},
				Options: map[string]string{
					"charset":   "utf8mb4",
					"parseTime": "true",
					"loc":       "Asia/Shanghai",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseDSN(tt.args.dsn)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseDSN() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseDSN() got = %v, want %v", got, tt.want)
			}
		})
	}
}
