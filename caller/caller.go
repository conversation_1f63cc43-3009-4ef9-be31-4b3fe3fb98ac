package caller

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"time"

	"github.com/hashicorp/go-multierror"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
)

const (
	MaxMultiCount  = 128
	MaxRetryCount  = 3
	LockExpireTime = 5

	constIntervalDoKey          = "interval_do"
	constIntervalDoValidSeconds = 24 * 60 * 60
)

// MultiDo 并发做
func MultiDo(ctx context.Context, array any, do func(item any) error, multiCount int) (err error) {
	if multiCount > MaxMultiCount {
		multiCount = MaxMultiCount
	}

	rv := reflect.ValueOf(array)
	if rv.Kind() != reflect.Slice && rv.Kind() != reflect.Array {
		return fmt.Errorf("参数必须是数组类型")
	}

	// 并发 + 重试
	mr.ForEach(
		func(source chan<- any) {
			for i := 0; i < rv.Len(); i++ {
				source <- rv.Index(i).Interface()
			}
		}, func(item any) {
			nerr := do(item)
			if nerr != nil {
				err = multierror.Append(err, nerr)
			}
		}, mr.WithContext(ctx), mr.WithWorkers(multiCount),
	)
	if err != nil {
		return
	}

	return nil
}

// RetryDo 重试
func RetryDo(retry int, do func() error) (err error) {
	if retry > MaxRetryCount {
		retry = MaxRetryCount
	}

	var times int
	for times < retry {
		times++

		err = do()
		if err == nil {
			return nil
		}
		time.Sleep(time.Millisecond * 100)
	}
	return err
}

// LockDo 上锁做（一次只能做一个）
func LockDo(r *redis.Redis, key string, do func() error, expires ...int) (err error) {
	expire := LockExpireTime
	if len(expires) > 0 {
		expire = expires[0]
	}

	lock, err := redislock.NewRedisLockAndAcquire(r, key, redislock.WithExpire(time.Duration(expire)*time.Second))
	if err != nil {
		return err
	}
	defer func() {
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return do()
}

// IntervalDo 间隔做，一段时间内，最多只能做一次
func IntervalDo(ctx context.Context, r *redis.Redis, key string, do func() error, interval time.Duration) (err error) {
	tKey := fmt.Sprintf("%s::%s", key, constIntervalDoKey)
	intervalDo := func() error {
		err1 := do()
		if err1 != nil {
			return err1
		}
		_ = r.SetexCtx(ctx, tKey, fmt.Sprintf("%d", time.Now().Unix()), constIntervalDoValidSeconds) // 保留1天有效期
		return nil
	}

	var lastTime int64
	tVal, err := r.GetCtx(ctx, tKey)
	if err != nil {
		// 查找失败，直接执行
		return intervalDo()
	}

	// 当前时间距离上一次执行时间的间隔大于等于指定值才运行
	lastTime, _ = strconv.ParseInt(tVal, 10, 64)
	sec := int64(interval.Seconds())
	if lastTime <= time.Now().Unix()-sec {
		return intervalDo()
	}

	// 不需要执行，默认返回空
	return nil
}

type (
	RetryOption func(o *retryOption)

	retryOption struct {
		retry    int
		interval time.Duration
	}
)

const (
	ConstDefaultRetryTimes    = 3
	ConstDefaultRetryInterval = 100 * time.Millisecond
)

func WithRetry(retry int) RetryOption {
	return func(o *retryOption) {
		o.retry = retry
	}
}

func WithInterval(interval time.Duration) RetryOption {
	return func(o *retryOption) {
		o.interval = interval
	}
}

// RetryWithOptionDo execute specified function with retry options.
func RetryWithOptionDo(do func() error, opts ...RetryOption) error {
	o := &retryOption{
		retry:    ConstDefaultRetryTimes,
		interval: ConstDefaultRetryInterval,
	}
	for _, opt := range opts {
		opt(o)
	}

	var (
		times int
		err   error
	)

	for times < o.retry {
		times++

		err = do()
		if err == nil {
			return nil
		}

		time.Sleep(o.interval)
	}

	return err
}

// LockWithOptionDo execute specified function after acquiring Redis distributed lock.
func LockWithOptionDo(r *redis.Redis, key string, do func() error, opts ...redislock.Option) (err error) {
	lock, err := redislock.NewRedisLockAndAcquire(r, key, opts...)
	if err != nil {
		return err
	}

	defer func() {
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return do()
}
