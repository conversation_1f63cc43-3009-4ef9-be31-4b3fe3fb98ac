package caller

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/stores/redis"
)

func TestMultiDo(t *testing.T) {
	array := []string{
		"abc",
		"123",
	}

	err := MultiDo(
		context.Background(), array, func(item any) error {
			key, ok := item.(string)
			if !ok {
				return fmt.Erro<PERSON>("无效的key类型: %T", item)
			}

			return RetryDo(
				3, func() error {
					_, perr := fmt.Println(key)
					return perr
				},
			)
		}, 16,
	)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("%s", err)
		t.<PERSON>ail<PERSON>ow()
	}
}

func TestInternalDo(t *testing.T) {
	n := 10
	for {
		err := IntervalDo(
			context.Background(),
			redis.MustNewRedis(
				redis.RedisConf{
					Host: "127.0.0.1:6379",
					Type: "node",
					Pass: "Quwan@2020",
				}, redis.WithDB(0),
			),
			"internal_test",
			func() error {
				fmt.Println("hello")
				return nil
			},
			time.Second*5,
		)
		if err != nil {
			t.<PERSON><PERSON><PERSON>("%s", err)
			t.<PERSON>ail<PERSON><PERSON>()
		}

		time.Sleep(3 * time.Second)

		if n == 0 {
			break
		}
		n--
	}
}
