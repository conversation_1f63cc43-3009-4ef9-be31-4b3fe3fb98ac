package log

import (
	"github.com/hibiken/asynq"
	"github.com/zeromicro/go-zero/core/logx"
)

type InternalLog struct {
	logger logx.Logger
}

func NewInternalLog(logger logx.Logger) asynq.Logger {
	return InternalLog{
		logger: logger,
	}
}

// 内部

func (i InternalLog) Debug(args ...any) {
	i.logger.Debug(args...)
}

func (i InternalLog) Info(args ...any) {
	i.logger.Info(args...)
}

func (i InternalLog) Warn(args ...any) {
	i.logger.Warn(args...)
}

func (i InternalLog) Error(args ...any) {
	i.logger.Error(args...)
}

func (i InternalLog) Fatal(args ...any) {
	i.logger.Slow(args...)
}
