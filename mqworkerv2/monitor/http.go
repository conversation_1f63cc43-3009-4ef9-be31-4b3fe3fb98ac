package monitor

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gorilla/mux"
	"github.com/hibiken/asynq"
	"github.com/hibiken/asynqmon"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
)

func Start(opt asynq.RedisConnOpt) {
	h := asynqmon.New(
		asynqmon.Options{
			RootPath:     "/monitoring", // RootPath specifies the root for asynqmon app
			RedisConnOpt: opt,
			// PrometheusAddress: "http://***************:9090",
		},
	)

	r := mux.NewRouter()
	r.PathPrefix(h.RootPath()).Handler(h)
	srv := &http.Server{
		Addr:              ":8091",
		Handler:           r,
		ReadHeaderTimeout: 2 * time.Second,
	}

	logx.Infof("asynq monitor start port/path[ http://localhost:8091/monitoring ]")

	threading.GoSafe(
		func() {
			defer func(srv *http.Server) {
				err := srv.Close()
				if err != nil {
					fmt.Println(err)
				}
			}(srv)
			// Go to http://localhost:8091/monitoring to see asynqmon homepage.
			logx.Must(srv.ListenAndServe())
		},
	)
}
