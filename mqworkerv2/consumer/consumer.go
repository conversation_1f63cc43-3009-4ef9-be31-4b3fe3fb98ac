package consumer

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/RichardKnop/machinery/v2/tasks"
	"github.com/hibiken/asynq"
	loggozero "github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/monitor"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/prometheus"
)

var _one sync.Once

type Config struct {
	Broker                  string
	Backend                 string
	Queue                   string
	ConsumerTag             string
	Db                      int
	MaxWorker               int  // 最大并发数, 当等于0时并发数为cpu数*2
	IsEnableMetricsExporter bool // 是否开启监控采集
	IsMonitorHttp           bool // 是否开启监控服务
}

type Consumer struct {
	conf       Config
	opt        asynq.RedisClientOpt
	server     *asynq.Server
	scheduler  *asynq.Scheduler
	cleaner    func()
	mapHandler map[string]asynq.Handler
}

func NewConsumer(conf Config) *Consumer {
	consumer := &Consumer{
		conf: conf,
		opt: asynq.RedisClientOpt{
			Addr:         conf.Backend,
			DB:           conf.Db,
			DialTimeout:  time.Second * 15,
			ReadTimeout:  time.Second * 15,
			WriteTimeout: time.Second * 15,
		},
	}
	internalLog := log.NewInternalLog(loggozero.WithContext(context.Background()).WithCallerSkip(2))
	level := asynq.InfoLevel
	srv := asynq.NewServer(
		consumer.opt,
		asynq.Config{
			Concurrency: conf.MaxWorker,
			Queues: map[string]int{
				conf.Queue + "_" + base.QueuePriorityDefault: 15,
				conf.Queue + "_" + base.QueuePriorityHigh:    38,
				conf.Queue + "_" + base.QueuePriorityUltra:   50,
				conf.Queue + "_" + base.QueuePriorityLow:     7,
			},
			StrictPriority: true,
			Logger:         internalLog,
			LogLevel:       level,
		},
	)
	consumer.scheduler = asynq.NewScheduler(
		consumer.opt, &asynq.SchedulerOpts{
			Logger:   internalLog,
			LogLevel: level,
			Location: time.Local,
		},
	)
	consumer.server = srv
	return consumer
}

type (
	TaskHandler    func() (name string, call base.Handler)
	TaskHandlerOjb struct {
		Name string
		Call base.Handler
	}
)

func NewTaskHandlerOjb(name string, call base.Handler) *TaskHandlerOjb {
	return &TaskHandlerOjb{
		Name: name,
		Call: call,
	}
}

func (c *Consumer) RegisterHandlers(taskHandlers ...*TaskHandlerOjb) error {
	m := make(map[string]asynq.Handler, len(taskHandlers))
	for _, f := range taskHandlers {
		s, handler := f.Name, f.Call
		m[s] = asynq.HandlerFunc(
			func(ctx context.Context, task *asynq.Task) error {
				taskArg := &base.Task{
					Payload:  task.Payload(),
					Typename: task.Type(),
				}
				if w := task.ResultWriter(); w != nil {
					taskArg.Queue = w.Queue()
				}

				result, err := handler.ProcessTask(ctx, taskArg)
				if err != nil {
					return err
				}

				if w := task.ResultWriter(); result != nil && w != nil {
					if _, err := w.Write(result); err != nil {
						loggozero.WithContext(context.Background()).Error(err)
						return err
					}
					task.SetOnSuccessPayload(result)
				}
				return nil
			},
		)
	}

	c.mapHandler = m
	return nil
}

func (c *Consumer) RegisterPeriodicTask(spec, name string, task *tasks.Signature) (entryID string, err error) {
	return c.scheduler.Register(
		spec, asynq.NewTask(
			task.Name, base.GetPayloadBySignature(task),
		),
		asynq.Queue(c.conf.Queue+"_"+base.QueuePriorityDefault),
		asynq.Retention(time.Second*3600),
	)
}

func (c *Consumer) RemovePeriodicTask(entryID string) {
	_ = c.scheduler.Unregister(entryID)
}

func (c *Consumer) Start() {
	// 暂时不初始化中间件
	err := c.start(true)
	if err != nil {
		loggozero.WithContext(context.Background()).Error(err)
		panic(err)
	}
}

func (c *Consumer) Stop() {
	// c.server.Stop()
	// c.server.Shutdown()
	fmt.Println("Stopped")
}

func (c *Consumer) start(listen bool, mws ...asynq.MiddlewareFunc) error {
	if c.conf.IsEnableMetricsExporter {
		_one.Do(
			func() {
				prometheus.StartPM(c.opt)
			},
		)
	}
	if c.conf.IsMonitorHttp {
		monitor.Start(c.opt)
	}
	err := c.scheduler.Start()
	defer c.scheduler.Shutdown()
	if err != nil {
		panic(err)
	}
	mux := asynq.NewServeMux()
	for k, v := range c.mapHandler {
		mux.Handle(k, v)
	}
	mux.Use(mws...)
	if !listen {
		return c.server.Start(mux)
	}
	return c.server.Run(mux)
}

func (c *Consumer) StartWithoutListen() {
	// 暂时不初始化中间件
	err := c.start(false)
	if err != nil {
		loggozero.WithContext(context.Background()).Error(err)
		panic(err)
	}
}

func (c *Consumer) Shutdown() {
	c.server.Shutdown()
}
