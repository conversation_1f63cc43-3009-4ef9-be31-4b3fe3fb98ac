package consumer

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"github.com/hibiken/asynq"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
)

func TestConsumer(t *testing.T) {
	err := os.Setenv("OTEL_RESOURCE_ATTRIBUTES", "token=YKasGxIXZtyEfEiCvjUi")
	if err != nil {
		return
	}
	trace.StartAgent(
		trace.Config{
			Name:     "main",
			Endpoint: "ap-beijing.apm.tencentcs.com:4317",
			Batcher:  "otlpgrpc",
			Sampler:  1.0,
		},
	)
	defer trace.StopAgent()
	consumer := NewConsumer(
		Config{
			Broker:  "192.168.118.128:6379",
			Backend: "192.168.118.128:6379",
			Queue:   "topic_simple_test",
			// ConsumerTag: "test_consumer",
			Db:        5,
			MaxWorker: 1024,
		},
	)
	err = consumer.RegisterHandlers(
		NewTaskHandlerOjb("simple", NewMqProcessor()),
		NewTaskHandlerOjb("callback", NewCallbackProcessor()),
	)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}
	/*marshal, err := json.Marshal(
		&struct {
			Name string `json:"Name"`
		}{
			Name: "yangbo",
		},
	)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	entryID, err := consumer.RegisterPeriodicTask(
		"@every 10s", "simple", &tasks.Signature{
			Name: "simple",
			Args: []tasks.Arg{
				tasks.Arg{
					Type:  "[]byte",
					Value: marshal,
				},
			},
		},
	)
	go func() {
		time.AfterFunc(
			time.Second*30, func() {
				t.Logf("ex: %s", "30s end")
				consumer.RemovePeriodicTask(entryID)
			},
		)
	}()*/

	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	consumer.Start()
	defer consumer.Stop()
	if err != nil {
		t.Errorf("start err: %s", err)
		t.FailNow()
	}
}

// ImageProcessor implements asynq.Handler interface.
type MqProcessor struct {
	// ... fields for struct
}

func (processor *MqProcessor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	var p struct {
		Name string `json:"Name"`
	}
	if err := json.Unmarshal(task.Payload, &p); err != nil {
		return nil, err
	}
	id, _ := asynq.GetTaskID(ctx)
	QueueName, _ := asynq.GetQueueName(ctx)
	fmt.Println("============ProcessTask============")
	fmt.Println(fmt.Sprintf("QueueName:%s", QueueName))
	fmt.Println(fmt.Sprintf("Type:%s", task.Typename))
	fmt.Println(fmt.Sprintf("GetTaskID:%s", id))
	fmt.Println(fmt.Sprintf("TraceID:%s", trace.TraceIDFromContext(ctx)))
	fmt.Println(fmt.Sprintf("SpanID:%s", trace.SpanIDFromContext(ctx)))
	fmt.Println("========================================")
	marshal, _ := json.Marshal(
		struct {
			Text   string `json:"text"`
			Iphone string `json:"iphone"`
		}{
			Iphone: "1233736467",
			Text:   "today",
		},
	)
	// 主动返回 记录结果 消费成功如果有使用回调结果作为回调消息的入参数
	return marshal, errorx.New(403, "动返回 记录结果 消费成功如果有使用回调结果作为回调消息的入参数")
}

func NewMqProcessor() (call base.Handler) {
	return &MqProcessor{}
}

// ImageProcessor implements asynq.Handler interface.
type CallbackProcessor struct {
	// ... fields for struct
}

func (processor *CallbackProcessor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	var p struct {
		Name string `json:"Name"`
	}
	if err := json.Unmarshal(task.Payload, &p); err != nil {
		return nil, err
	}
	id, _ := asynq.GetTaskID(ctx)
	QueueName, _ := asynq.GetQueueName(ctx)
	fmt.Println("============CallbackProcessor============")
	fmt.Println(fmt.Sprintf("QueueName:%s", QueueName))
	fmt.Println(fmt.Sprintf("Type:%s", task.Typename))
	fmt.Println(fmt.Sprintf("GetTaskID:%s", id))
	fmt.Println(fmt.Sprintf("TraceID:%s", trace.TraceIDFromContext(ctx)))
	fmt.Println(fmt.Sprintf("SpanID:%s", trace.SpanIDFromContext(ctx)))
	fmt.Println("========================================")

	return nil, nil
}

func NewCallbackProcessor() (call base.Handler) {
	// 名字使用枚举
	return &CallbackProcessor{}
}
