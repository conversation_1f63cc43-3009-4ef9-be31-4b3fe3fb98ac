package producer

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/RichardKnop/machinery/v2/tasks"
	"github.com/hibiken/asynq"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type EmailTaskPayload struct {
	// ID for the email recipient.
	UserID int
}

func TestAsyncPush(t *testing.T) {
	err := os.Setenv("OTEL_RESOURCE_ATTRIBUTES", "token=YKasGxIXZtyEfEiCvjUi")
	if err != nil {
		return
	}
	trace.StartAgent(
		trace.Config{
			Name:     "main",
			Endpoint: "ap-beijing.apm.tencentcs.com:4317",
			Batcher:  "otlpgrpc",
			Sampler:  1.0,
		},
	)
	defer trace.StopAgent()
	producer := NewProducer(
		Config{
			Broker:  "***************:6379",
			Backend: "***************:6379",
			Queue:   "topic_simple_test",
			Db:      5,
		},
	)
	marshal, _ := json.Marshal(
		struct {
			Name string `json:"Name"`
		}{
			Name: "yangbo",
		},
	)
	task := tasks.Signature{
		Name: "simple",
		UUID: utils.GenNanoId("task_id:"),
		Args: []tasks.Arg{
			{Value: marshal, Type: "[]byte"},
		},
		OnSuccess: []*tasks.Signature{
			{
				Name:       "callback",
				RoutingKey: "topic_simple_test",
			},
		},
	}

	_, err = producer.AsyncPush(context.Background(), &task, "test_productor")
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}
	time.Sleep(time.Second * 3)
}

func TestSend(t *testing.T) {
	err := os.Setenv("OTEL_RESOURCE_ATTRIBUTES", "token=YKasGxIXZtyEfEiCvjUi")
	if err != nil {
		return
	}
	trace.StartAgent(
		trace.Config{
			Name:     "main",
			Endpoint: "ap-beijing.apm.tencentcs.com:4317",
			Batcher:  "otlpgrpc",
			Sampler:  1.0,
		},
	)
	defer trace.StopAgent()
	producer := NewProducer(
		Config{
			Broker:  "***************:6379",
			Backend: "***************:6379",
			Queue:   "topic_simple_test",
			Db:      5,
		},
	)
	payload, _ := json.Marshal(
		struct {
			Name string `json:"Name"`
		}{
			Name: "yangbo",
		},
	)

	newTask := base.NewTask(
		"simple", payload,
		base.WithMaxRetryOptions(100),
		base.WithRetentionOptions(time.Second*300),
	)
	newTask.SetCallback("callback", "topic_simple_test")
	for i := 0; i < 100000; i++ {
		send, err := producer.Send(context.Background(), newTask, base.QueuePriorityHigh)
		if err != nil {
			t.Errorf("err: %s", err)
			t.FailNow()
		}
		fmt.Println(send)
	}

	time.Sleep(time.Second * 3)
}

func TestSendDelay(t *testing.T) {
	err := os.Setenv("OTEL_RESOURCE_ATTRIBUTES", "token=YKasGxIXZtyEfEiCvjUi")
	if err != nil {
		return
	}
	trace.StartAgent(
		trace.Config{
			Name:     "main",
			Endpoint: "ap-beijing.apm.tencentcs.com:4317",
			Batcher:  "otlpgrpc",
			Sampler:  1.0,
		},
	)
	defer trace.StopAgent()
	producer := NewProducer(
		Config{
			Broker:  "***************:6379",
			Backend: "***************:6379",
			Queue:   "topic_simple_test",
			Db:      5,
		},
	)

	payload, _ := json.Marshal(
		struct {
			Name string `json:"Name"`
		}{
			Name: "yangbo",
		},
	)

	newTask := base.NewTask(
		"simple", payload,
		base.WithMaxRetryOptions(100),
		base.WithRetentionOptions(time.Second*300),
	)
	newTask.SetCallback("callback", "topic_callback_test")

	send, err := producer.SendDelay(context.Background(), newTask, time.Minute*30, base.QueuePriorityDefault)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}
	fmt.Println(send)

	time.Sleep(time.Second * 3)
}

func TestAsyncPushReporter(t *testing.T) {
	err := os.Setenv("OTEL_RESOURCE_ATTRIBUTES", "token=YKasGxIXZtyEfEiCvjUi")
	if err != nil {
		return
	}
	trace.StartAgent(
		trace.Config{
			Name:     "main",
			Endpoint: "ap-beijing.apm.tencentcs.com:4317",
			Batcher:  "otlpgrpc",
			Sampler:  1.0,
		},
	)
	defer trace.StopAgent()
	producer := NewProducer(
		Config{
			Broker:  "***************:6379",
			Backend: "***************:6379",
			Queue:   "mqc:reporter",
			Db:      5,
		},
	)
	marshal, _ := json.Marshal(
		struct {
			Name string `json:"Name"`
		}{
			Name: "yangbo",
		},
	)
	task := tasks.Signature{
		Name: "split_clean_task",
		UUID: utils.GenNanoId("task_id:"),
		Args: []tasks.Arg{
			{Value: marshal, Type: "[]byte"},
		},
		OnSuccess: []*tasks.Signature{
			{
				Name:       "callback",
				RoutingKey: "mqc:reporter",
			},
		},
	}

	_, err = producer.AsyncPush(context.Background(), &task, "test_productor")
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}
	time.Sleep(time.Second * 3)
}

func TestSendReporter(t *testing.T) {
	err := os.Setenv("OTEL_RESOURCE_ATTRIBUTES", "token=YKasGxIXZtyEfEiCvjUi")
	if err != nil {
		return
	}
	trace.StartAgent(
		trace.Config{
			Name:     "main",
			Endpoint: "ap-beijing.apm.tencentcs.com:4317",
			Batcher:  "otlpgrpc",
			Sampler:  1.0,
		},
	)
	defer trace.StopAgent()
	producer := NewProducer(
		Config{
			Broker:  "***************:6379",
			Backend: "***************:6379",
			Queue:   "mqc:reporter",
			Db:      5,
		},
	)

	payload, _ := json.Marshal(
		struct {
			Name string `json:"Name"`
		}{
			Name: "yangbo",
		},
	)

	newTask := base.NewTask(
		"split_clean_task", payload,
		base.WithMaxRetryOptions(100),
		base.WithRetentionOptions(time.Second*300),
	)
	newTask.SetCallback("callback", "topic_callback_test")

	send, err := producer.Send(context.Background(), newTask, base.QueuePriorityUltra)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}
	fmt.Println(send)

	time.Sleep(time.Second * 3)
}

var redisConnOpt asynq.RedisClientOpt

func init() {
	redisConnOpt = asynq.RedisClientOpt{
		Addr: "***************:6379",
		// Omit if no password is required
		// Password: "mypassword",
		// Use a dedicated db number for asynq.
		// By default, Redis offers 16 databases (0..15)
		DB: 5,
	}
}
