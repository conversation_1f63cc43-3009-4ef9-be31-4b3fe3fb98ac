package producer

import (
	"context"
	"time"

	"github.com/hibiken/asynq"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"

	"github.com/RichardKnop/machinery/v2/backends/result"
	"github.com/RichardKnop/machinery/v2/tasks"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type Config struct {
	Broker  string
	Backend string
	Queue   string
	Db      int
}

type Producer struct {
	svr   *asynq.Client
	queue string
}

func NewProducer(conf Config) *Producer {
	return &Producer{
		svr:   newClient(conf),
		queue: conf.Queue,
	}
}

func newClient(conf Config) *asynq.Client {
	opt := asynq.RedisClientOpt{
		// Network:      "",
		Addr: conf.Backend,
		// Username:     "",
		// Password:     "",
		DB:           conf.Db,
		DialTimeout:  time.Second * 15,
		ReadTimeout:  time.Second * 15,
		WriteTimeout: time.Second * 15,
		// PoolSize:     0,
		// TLSConfig:    nil,
	}

	return asynq.NewClient(opt)
}

// AsyncPush pushes a task onto the queue for asynchronous execution.compatible with the old version, please use the new api.
// Deprecated: use new api.
// ctx context.Context, task *tasks.Signature, operationName ...string
// *result.AsyncResult, error
// retention:3600s
// Queue:QueuePriorityDefault
func (producer *Producer) AsyncPush(ctx context.Context, task *tasks.Signature, operationName ...string) (
	*result.AsyncResult, error,
) {
	if task == nil {
		return nil, nil
	}
	// 走默认队列 使用外部的taskId
	queue := producer.queue
	if task.RoutingKey != "" {
		queue = task.RoutingKey
	}
	taskid := utils.GenNanoId(task.UUID + ":mqworkerv2:asyncpush:")
	asynqTask := asynq.NewTask(
		task.Name, base.GetPayloadBySignature(task),
		asynq.TaskID(taskid),
		asynq.MaxRetry(task.RetryCount),
		asynq.Queue(queue+"_"+base.QueuePriorityDefault),
		asynq.Retention(time.Second*3600),
	)
	// callback
	if len(task.OnSuccess) != 0 {
		signature := task.OnSuccess[0]
		asynqTask.SetOnSuccess(
			signature.Name, []asynq.Option{
				asynq.TaskID(taskid),
				asynq.MaxRetry(task.RetryCount),
				asynq.Queue(signature.RoutingKey + "_" + base.QueuePriorityDefault),
				asynq.Retention(time.Second * 3600),
			}...,
		)
	}

	_, err := producer.svr.EnqueueContext(ctx, asynqTask)
	if err != nil {
		logx.WithContext(ctx).Error("AsyncPush: %s", err)
		return nil, err
	}
	r := new(result.AsyncResult)
	r.Signature = task
	return r, nil
}

// Send 立即消息 优先什么队列
func (producer *Producer) Send(ctx context.Context, task *base.Task, priorityType base.QueuePriorityType) (
	*asynq.TaskInfo, error,
) {
	if task == nil {
		return nil, nil
	}

	res, err := producer.svr.EnqueueContext(ctx, producer.CreateTask(task, priorityType, 0))
	if err != nil {
		logx.WithContext(ctx).Errorf("failed to send task, name: %s, error: %+v", task.Typename, err)
		return nil, err
	}

	return res, nil
}

// SendAsync 立即消息 优先什么队列
func (producer *Producer) SendAsync(ctx context.Context, task *base.Task, priorityType base.QueuePriorityType) {
	threading.GoSafe(
		func() {
			_, err := producer.Send(ctx, task, priorityType)
			if err != nil {
				logx.WithContext(ctx).Error("SendAsync: %s,task: %v", err, task)
				return
			}
		},
	)
}

// SendDelay 延迟消息(delay)
func (producer *Producer) SendDelay(
	ctx context.Context, task *base.Task, delay time.Duration, priorityType base.QueuePriorityType,
) (*asynq.TaskInfo, error) {
	if task == nil {
		return nil, nil
	}

	res, err := producer.svr.EnqueueContext(ctx, producer.CreateTask(task, priorityType, delay))
	if err != nil {
		logx.WithContext(ctx).Errorf("failed to send delay task, name: %s, error: %+v", task.Typename, err)
		return nil, err
	}

	return res, nil
}

func (producer *Producer) CreateTask(
	task *base.Task, priorityType base.QueuePriorityType, delay time.Duration,
) *asynq.Task {
	// 走默认队列 使用外部的taskId
	var (
		asynqTask *asynq.Task
		options   = make([]asynq.Option, 0, 8)
	)

	if task.MaxRetry >= 0 {
		options = append(options, asynq.MaxRetry(task.MaxRetry))
	}
	if !task.Deadline.IsZero() {
		options = append(options, asynq.Deadline(task.Deadline))
	}
	if task.Timeout > 0 {
		options = append(options, asynq.Timeout(task.Timeout))
	}
	if task.Unique > 0 {
		options = append(options, asynq.Unique(task.Unique))
	}
	if task.Retention > 0 {
		options = append(options, asynq.Retention(task.Retention))
	}
	if task.Queue != "" {
		options = append(options, asynq.Queue(task.Queue+"_"+priorityType))
	} else {
		options = append(options, asynq.Queue(producer.queue+"_"+priorityType))
	}
	if delay > 0 {
		options = append(options, asynq.ProcessIn(delay))
	} else if task.Delay > 0 {
		options = append(options, asynq.ProcessIn(task.Delay))
	}

	asynqTask = asynq.NewTask(
		task.Typename, task.Payload, options...,
	)

	// callback
	if task.Callback != nil {
		callbackOptions := make([]asynq.Option, 0, len(options)+1)
		// 继承父类的优先级和相关参数
		callbackOptions = append(callbackOptions, options...)
		callbackOptions = append(callbackOptions, asynq.Queue(task.CallbackQueue+"_"+priorityType))
		asynqTask.SetOnSuccess(task.Callback.Typename, callbackOptions...)
	}

	return asynqTask
}

/* 指定消费时间
func (producer *Producer) SendAppoint(ctx context.Context, task Task ,time time.Time) (asynq.TaskInfo, error,) {
	panic("")
}*/
