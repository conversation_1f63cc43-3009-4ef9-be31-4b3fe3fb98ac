package base

import (
	"context"
	"time"

	"github.com/RichardKnop/machinery/v2/tasks"
)

type Handler interface {
	ProcessTask(context.Context, *Task) (result []byte, err error)
}

type Task struct {
	Typename string // typename indicates the type of task to be performed.
	Payload  []byte // payload holds data needed to perform the task.

	Queue string // 队列名称

	Callback      *Task // 成功回调
	CallbackQueue string

	MaxRetry  int           // 最大重试次数
	Timeout   time.Duration // 超时时间
	Deadline  time.Time     // 任务消费的截止日期
	Unique    time.Duration // 唯一的占用时间
	Retention time.Duration // 保留时间
	Delay     time.Duration // 延迟时间
}

func NewTask(typename string, payload []byte, options ...TaskOption) *Task {
	t := &Task{
		Typename: typename,
		Payload:  payload,
	}

	// 应用所有选项
	for _, option := range options {
		option(t)
	}

	return t
}

func (receiver *Task) SetCallback(typename, callbackQueue string) {
	t := new(Task)
	t.Typename = typename
	receiver.Callback = t
	receiver.CallbackQueue = callbackQueue
}

type TaskOption func(t *Task)

// WithQueueOptions 设置 Queue 字段的选项
func WithQueueOptions(queue string) TaskOption {
	return func(t *Task) {
		t.Queue = queue
	}
}

// WithCallbackOptions 设置 Callback 字段的选项
func WithCallbackOptions(callback *Task) TaskOption {
	return func(t *Task) {
		t.Callback = callback
	}
}

// WithCallbackQueueOptions 设置 CallbackQueue 字段的选项
func WithCallbackQueueOptions(queue string) TaskOption {
	return func(t *Task) {
		t.CallbackQueue = queue
	}
}

// WithMaxRetryOptions 设置 MaxRetry 字段的选项
func WithMaxRetryOptions(retry int) TaskOption {
	if retry < 0 {
		retry = 0
	}
	return func(t *Task) {
		t.MaxRetry = retry
	}
}

// WithTimeoutOptions 设置 Timeout 字段的选项
func WithTimeoutOptions(timeout time.Duration) TaskOption {
	return func(t *Task) {
		t.Timeout = timeout
	}
}

// WithDeadlineOptions 设置 Deadline 字段的选项
func WithDeadlineOptions(deadline time.Time) TaskOption {
	return func(t *Task) {
		t.Deadline = deadline
	}
}

// WithUniqueOptions 设置 Unique 字段的选项
func WithUniqueOptions(unique time.Duration) TaskOption {
	return func(t *Task) {
		t.Unique = unique
	}
}

// WithRetentionOptions 设置 Retention 字段的选项
func WithRetentionOptions(retention time.Duration) TaskOption {
	return func(t *Task) {
		t.Retention = retention
	}
}

// WithDelayOptions 设置 Delay 字段的选项
func WithDelayOptions(delay time.Duration) TaskOption {
	return func(t *Task) {
		t.Delay = delay
	}
}

func GetPayloadBySignature(task *tasks.Signature) []byte {
	var arg []byte
	if len(task.Args) != 0 {
		a := task.Args[0]
		if bytes, ok := a.Value.([]byte); ok {
			arg = bytes
		}
	}
	return arg
}
