package metrics

import (
	"github.com/zeromicro/go-zero/core/metric"
)

type (
	ConstSystemMetricType     string
	ConstSystemMetricUnitType string
)

const (
	_namespace = "qet"
	_business  = "business"
	_universal = "universal"

	SystemMetricTypeHistogram ConstSystemMetricType = "_histogram"
	SystemMetricTypeCounter   ConstSystemMetricType = "_counter"
	SystemMetricTypeSummary   ConstSystemMetricType = "_summary"
	SystemMetricTypeGauge     ConstSystemMetricType = "_gauge"

	ConstSystemMetricUnitTypeSeconds      ConstSystemMetricUnitType = "_seconds"
	ConstSystemMetricUnitTypeMilliseconds ConstSystemMetricUnitType = "_ms"
	ConstSystemMetricUnitTypeTotal        ConstSystemMetricUnitType = "_total"
	ConstSystemMetricUnitTypeCounts       ConstSystemMetricUnitType = "_counts"
	ConstSystemMetricUnitTypeBytes        ConstSystemMetricUnitType = "_bytes"
	ConstSystemMetricUnitTypeObjects      ConstSystemMetricUnitType = "_objects"
	ConstSystemMetricUnitTypeInfo         ConstSystemMetricUnitType = "_info"
)

type (
	MetricGaugeHandler interface {
		Set(v float64, labels ...string)
		Inc(labels ...string)
		Dec(labels ...string)
		Add(v float64, labels ...string)
		Sub(v float64, labels ...string)
	}
	MetricCounterHandler interface {
		Inc(labels ...string)
		Add(v float64, labels ...string)
	}
	MetricHistogramHandler interface {
		Observe(v int64, labels ...string)
	}
	MetricSummaryHandler interface {
		Observe(v float64, labels ...string)
	}

	MetricGauge struct {
		opt  *metric.GaugeVecOpts
		hand metric.GaugeVec
	}
	MetricCounter struct {
		opt  *metric.CounterVecOpts
		hand metric.CounterVec
	}
	MetricHistogram struct {
		opt  *metric.HistogramVecOpts
		hand metric.HistogramVec
	}
	MetricSummary struct {
		opt  *metric.SummaryVecOpts
		hand metric.SummaryVec
	}
)

// for example:
// 每个自定义指标有自己的侵入方式 有的需要用中间件有的需要代码中侵入
// 本身gozero对已经对client做了封装,不对以下内容做复杂性封装
// 「统一」在自己服务根目录下创建metrics包
// name 配置自己的指标key
// 创建自己需要的指标类型
// 需要中间件增加中间件中,需要在代码中增加在具体代码中增加

// 根据命名规范权衡
// 方案一
// for example:qet-business-pool-username-residue-counts 自身拥有label
// Namespace使用:qet
// Subsystem使用:business
// name使用:业务指标的名称_单位(可选)
// Labels使用:自定义定义自己需要的
// Help使用:自定义定义自己需要的
// 业务需要自己对Labels(描述进行封装)对外只暴露操作方法
var (
	MetricGaugeVecOpts = metric.GaugeVecOpts{
		Namespace: _namespace,
		Subsystem: _business,
		// Name:      "xxxx",
		// Help:      "pool username residue gauge.",
		// Labels:    []string{"pool_username", "env", "..."},
	}
	MetricCounterVecOpts = metric.CounterVecOpts{
		Namespace: _namespace,
		Subsystem: _business,
		// Name:      "xxxx",
		// Help:      "pool username total gauge.",
		// Labels:    []string{"pool_username", "env", "..."},
	}
	MetricHistogramVecOpts = metric.HistogramVecOpts{
		Namespace: _namespace,
		Subsystem: _business,
		// Name:      "xxxx",
		// Help:      "xxx duration(ms).",
		// Labels:    []string{"command_xxx"},
		// Buckets:   []float64{5, 10, 25, 50, 100, 250, 500, 1000, 2500},
	}

	MetricSummaryVecOpts = metric.SummaryVecOpts{
		VecOpt: metric.VectorOpts{
			Namespace: _namespace,
			Subsystem: _business,
			// Name:      "xxx_total" + SystemMetricTypeGauge.String(),
			// Help:      "xxx count.",
			// Labels:    []string{"command", "error", "xxxxx"},
		},
	}
)

// 方案二
//
// for example:qet-universal-business 业务性在labels
//
// Namespace使用:qet
// Subsystem使用:universal
// name使用:business
//
// Labels使用:
//
//	Labels(
//	 固定:服务名[service_name],
//	 固定:业务指标名[business_name],
//	 自定义tag:(前两个common固定,自己在添加自己需要的label)...
//	)
//	业务需要自己对Labels(描述进行封装)对外只暴露操作方法
//
// Help:自己描述当前指标的含义
var (
	MetricUniversalGaugeVecOpts = metric.GaugeVecOpts{
		Namespace: _namespace,
		Subsystem: _universal,
		Name:      _business,
		// Help:      "user custom",
		Labels: []string{"service_name", "business_name"}, // "user after custom "
	}
	MetricUniversalCounterVecOpts = metric.CounterVecOpts{
		Namespace: _namespace,
		Subsystem: _universal,
		Name:      _business,
		// Help:      "user custom",
		Labels: []string{"service_name", "business_name"}, // "user after custom "
	}
	MetricUniversalHistogramVecOpts = metric.HistogramVecOpts{
		Namespace: _namespace,
		Subsystem: _universal,
		Name:      _business,
		// Help:      "user custom",
		Labels: []string{"service_name", "business_name"}, // "user after custom "
		// Buckets:   []float64{5, 10, 25, 50, 100, 250, 500, 1000, 2500}, // "user after custom "
	}

	MetricUniversalSummaryVecOpts = metric.SummaryVecOpts{
		VecOpt: metric.VectorOpts{
			Namespace: _namespace,
			Subsystem: _universal,
			Name:      _business,
			// Help:      "user custom",
			Labels: []string{"service_name", "business_name"}, // "user after custom "
		},
	}
)

func (receiver ConstSystemMetricType) String() string {
	return string(receiver)
}

func NewMetricGauge(opt *metric.GaugeVecOpts) MetricGaugeHandler {
	if opt == nil {
		opt = new(metric.GaugeVecOpts)
	}
	return &MetricGauge{
		opt:  opt,
		hand: metric.NewGaugeVec(opt),
	}
}

func (m *MetricGauge) Set(v float64, labels ...string) {
	m.hand.Set(v, labels[0:len(m.opt.Labels)]...)
}

func (m *MetricGauge) Inc(labels ...string) {
	m.hand.Inc(labels[0:len(m.opt.Labels)]...)
}

func (m *MetricGauge) Dec(labels ...string) {
	m.hand.Dec(labels[0:len(m.opt.Labels)]...)
}

func (m *MetricGauge) Add(v float64, labels ...string) {
	m.hand.Add(v, labels[0:len(m.opt.Labels)]...)
}

func (m *MetricGauge) Sub(v float64, labels ...string) {
	m.hand.Sub(v, labels[0:len(m.opt.Labels)]...)
}

func NewMetricCounter(opt *metric.CounterVecOpts) MetricCounterHandler {
	if opt == nil {
		opt = new(metric.CounterVecOpts)
	}
	return &MetricCounter{
		opt:  opt,
		hand: metric.NewCounterVec(opt),
	}
}

func (m *MetricCounter) Inc(labels ...string) {
	m.hand.Inc(labels[0:len(m.opt.Labels)]...)
}

func (m *MetricCounter) Add(v float64, labels ...string) {
	m.hand.Add(v, labels[0:len(m.opt.Labels)]...)
}

func NewMetricHistogram(opt *metric.HistogramVecOpts) *MetricHistogram {
	if opt == nil {
		opt = new(metric.HistogramVecOpts)
	}
	return &MetricHistogram{
		opt:  opt,
		hand: metric.NewHistogramVec(opt),
	}
}

func (m *MetricHistogram) Observe(v int64, labels ...string) {
	m.hand.Observe(v, labels[0:len(m.opt.Labels)]...)
}

func NewMetricSummary(opt *metric.SummaryVecOpts) *MetricSummary {
	if opt == nil {
		opt = new(metric.SummaryVecOpts)
	}
	return &MetricSummary{
		opt:  opt,
		hand: metric.NewSummaryVec(opt),
	}
}

func (m *MetricSummary) Observe(v float64, labels ...string) {
	m.hand.Observe(v, labels[0:len(m.opt.VecOpt.Labels)]...)
}
