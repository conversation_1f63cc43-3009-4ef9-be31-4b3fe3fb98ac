package metrics

import (
	"context"
)

type contextKey string

func (c contextKey) String() string {
	return "rest/internal context key " + string(c)
}

var MetricKey = contextKey("qet_api_metric")

type MetricObj struct {
	Code uint32
}

// WithMetricObject returns a new context with MetricObj.
func WithMetricObject(ctx context.Context, obj *MetricObj) context.Context {
	return context.WithValue(ctx, MetricKey, obj)
}

// MetricObjectFromContext returns MetricObj from context.
func MetricObjectFromContext(ctx context.Context) *MetricObj {
	val := ctx.Value(MetricKey)
	if val == nil {
		return nil
	}

	obj, ok := val.(*MetricObj)
	if !ok {
		return nil
	}

	return obj
}
