# 命名规范
用于配置指标的一般选项。它可能是用于在代码中定义和配置指标时的一种可选方式，
用于传递关于指标的命名空间、子系统、名称、帮助文本和标签信息。
## 名称
* Namespace（命名空间）：
指标的命名空间，通常用于对指标进行分组或标识所属的模块或组件。它可以帮助您更好地组织指标并防止指标名称冲突。
* Subsystem（子系统）：
用于更进一步区分指标的子系统，例如某个服务中的不同组件或子模块。这有助于更细粒度地标识指标。
* Name（名称）：
指标的名称，描述指标所测量的内容。它应该是清晰、简洁且有意义的名称。

1. 必须符合数据模型中的有效字符
2. 应具有与度量标准所属域相关的(单个单词)应用程序前缀。客户端库有时将该前缀称为namespace。对于特定于应用程序的数据指标，前缀通常是应用程序名称本身。但是，有时数据指标更为通用，例如客户端库导出的标准化数据指标。例如:
   * prometheus_notifications_total(特指 Prometheus 服务)
   * process_cpu_seconds_total(由许多客户端库导出)
   * http_request_duration_seconds(对于所有的 HTTP 请求)
3. 必须由一个单位(如，不要将秒与毫秒或秒与字节混合)
4. 应当使用基本单位(如. seconds, bytes, meters - 而不是 milliseconds, megabytes, kilometers).参见以下基本单位列表.
5. 应以单位的复数形式进行描述。请注意，如果适用，除单位外，累加计数还具有total作为后缀
   * http_request_duration_seconds
   * node_memory_usage_bytes
   * http_requests_total(用于无单位的累计计数)
   * process_cpu_seconds_total(用于带单位的累加计数)
   * foobar_build_info((用于提供有关正在运行的二进制文件的元数据的伪度量)

## 扩展
* Labels（标签）：
这里列出了指标可能拥有的标签列表。标签用于提供更多的维度信息，以便更好地区分和分类指标。
* 使用标签来区分被测物的特征:
  1. api_http_requests_total - 区分请求类型: operation="create|update|delete"
  2. api_request_duration_seconds - 区分请求阶段: stage="extract|transform|load"
  3. 不要将标签名称放在度量标准名称中，这会引入冗余，且如果将各个标签汇总在一起会引起混淆。
  
* 警告: 请记住，键值标签对的每个唯一组合都代表一个新的时间序列，这可以大大增加存储的数据量。请勿使用标签存储具有高基数(许多不同的标签值)的维度，例如用户 ID，电子邮件地址或其他无限制的值集。
## 备注
* Help（帮助文本）：
  对指标的描述性帮助文本，解释了指标测量的含义和用途。这对于其他开发人员理解指标至关重要。
# 分类