package singleflight

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
)

// ExampleDistributedSingleFlight 展示如何使用分布式SingleFlight
func ExampleDistributedSingleFlight() {
	// 创建miniredis实例用于示例
	mr, err := miniredis.Run()
	if err != nil {
		log.Fatal(err)
	}
	defer mr.Close()

	// 创建Redis客户端
	rdb := redis.NewClient(
		&redis.Options{
			Addr: mr.Addr(),
		},
	)
	defer rdb.Close()

	// 创建分布式SingleFlight实例
	sf := NewDistributedSingleFlight(rdb)

	ctx := context.Background()

	// 模拟一个耗时的数据库查询
	expensiveDBQuery := func() (any, error) {
		fmt.Println("执行耗时的数据库查询...")
		time.Sleep(2 * time.Second) // 模拟耗时操作
		return map[string]any{
			"user_id": 123,
			"name":    "<PERSON>",
			"email":   "<EMAIL>",
		}, nil
	}

	// 并发调用相同的查询
	var wg sync.WaitGroup
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			start := time.Now()
			result, err := sf.Do(ctx, "user:123", expensiveDBQuery,
				WithLockExpire(30*time.Second),
				WithWaitTimeout(60*time.Second),
				WithResultExpire(5*time.Minute),
				WithSerializer(NewJSONSerializer()),
			)
			duration := time.Since(start)

			if err != nil {
				log.Printf("Goroutine %d: Error: %v", id, err)
				return
			}

			fmt.Printf("Goroutine %d: 结果: %+v, 耗时: %v\n", id, result, duration)
		}(i)
	}

	wg.Wait()
	fmt.Println("所有查询完成")
}

// ExampleDistributedSingleFlight_WithTimeout 展示超时控制
func ExampleDistributedSingleFlight_WithTimeout() {
	mr, err := miniredis.Run()
	if err != nil {
		log.Fatal(err)
	}
	defer mr.Close()

	rdb := redis.NewClient(
		&redis.Options{
			Addr: mr.Addr(),
		},
	)
	defer rdb.Close()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	// 模拟一个可能超时的操作
	slowOperation := func() (any, error) {
		fmt.Println("开始执行慢操作...")
		time.Sleep(5 * time.Second) // 模拟很慢的操作
		return "完成", nil
	}

	// 设置2秒超时
	result, err := sf.Do(ctx, "slow-operation", slowOperation, WithFunctionTimeout(2*time.Second))
	if err != nil {
		fmt.Printf("操作超时: %v\n", err)
	} else {
		fmt.Printf("操作结果: %v\n", result)
	}
}

// ExampleDistributedSingleFlight_ErrorHandling 展示错误处理
func ExampleDistributedSingleFlight_ErrorHandling() {
	mr, err := miniredis.Run()
	if err != nil {
		log.Fatal(err)
	}
	defer mr.Close()

	rdb := redis.NewClient(
		&redis.Options{
			Addr: mr.Addr(),
		},
	)
	defer func() {
		_ = rdb.Close()
	}()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	// 模拟一个会出错的操作
	failingOperation := func() (any, error) {
		fmt.Println("执行可能失败的操作...")
		return nil, fmt.Errorf("数据库连接失败")
	}

	var wg sync.WaitGroup
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			result, err := sf.Do(ctx, "failing-operation", failingOperation)
			if err != nil {
				fmt.Printf("Goroutine %d: 错误: %v\n", id, err)
			} else {
				fmt.Printf("Goroutine %d: 结果: %v\n", id, result)
			}
		}(i)
	}

	wg.Wait()
}

// ExampleDistributedSingleFlight_DifferentSerializers 展示不同序列化器的使用
func ExampleDistributedSingleFlight_DifferentSerializers() {
	mr, err := miniredis.Run()
	if err != nil {
		log.Fatal(err)
	}
	defer mr.Close()

	rdb := redis.NewClient(
		&redis.Options{
			Addr: mr.Addr(),
		},
	)
	defer rdb.Close()

	// 创建SingleFlight实例
	sf := NewDistributedSingleFlight(rdb)

	ctx := context.Background()

	complexData := func() (any, error) {
		return map[string]any{
			"numbers": []int{1, 2, 3, 4, 5},
			"nested": map[string]string{
				"key1": "value1",
				"key2": "value2",
			},
		}, nil
	}

	// 使用JSON序列化器
	result1, err1 := sf.Do(ctx, "complex-data-json", complexData, WithSerializer(NewJSONSerializer()))
	if err1 != nil {
		fmt.Printf("JSON序列化器错误: %v\n", err1)
	} else {
		fmt.Printf("JSON序列化器结果: %+v\n", result1)
	}

	// 使用GOB序列化器
	result2, err2 := sf.Do(ctx, "complex-data-gob", complexData, WithSerializer(NewGOBSerializer()))
	if err2 != nil {
		fmt.Printf("GOB序列化器错误: %v\n", err2)
	} else {
		fmt.Printf("GOB序列化器结果: %+v\n", result2)
	}
}

// ExampleDistributedSingleFlight_Forget 展示如何清除缓存
func ExampleDistributedSingleFlight_Forget() {
	mr, err := miniredis.Run()
	if err != nil {
		log.Fatal(err)
	}
	defer mr.Close()

	rdb := redis.NewClient(
		&redis.Options{
			Addr: mr.Addr(),
		},
	)
	defer rdb.Close()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	counter := 0
	getData := func() (any, error) {
		counter++
		return fmt.Sprintf("数据版本 %d", counter), nil
	}

	// 第一次调用
	result1, _ := sf.Do(ctx, "cached-data", getData)
	fmt.Printf("第一次调用: %v\n", result1)

	// 第二次调用（应该返回缓存结果）
	result2, _ := sf.Do(ctx, "cached-data", getData)
	fmt.Printf("第二次调用: %v\n", result2)

	// 清除缓存
	sf.Forget(ctx, "cached-data")
	fmt.Println("缓存已清除")

	// 第三次调用（应该重新执行函数）
	result3, _ := sf.Do(ctx, "cached-data", getData)
	fmt.Printf("第三次调用: %v\n", result3)
}
