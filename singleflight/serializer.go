package singleflight

import (
	"bytes"
	"encoding/gob"
	"encoding/json"

	"github.com/pkg/errors"
)

// JSONSerializer JSON序列化器
type JSONSerializer struct{}

// NewJSONSerializer 创建JSON序列化器
func NewJSONSerializer() *JSONSerializer {
	return &JSONSerializer{}
}

// Serialize 序列化数据为JSON
func (s *JSONSerializer) Serialize(data any) ([]byte, error) {
	// 处理error类型的特殊序列化
	if result, ok := data.(*Result); ok {
		serializable := &SerializableResult{
			Data: result.Data,
		}
		if result.Error != nil {
			serializable.ErrorMsg = result.Error.Error()
		}
		return json.Marshal(serializable)
	}
	return json.Marshal(data)
}

// Deserialize 从JSON反序列化数据
func (s *JSONSerializer) Deserialize(data []byte, v any) error {
	// 处理Result类型的特殊反序列化
	if result, ok := v.(*Result); ok {
		var serializable SerializableResult
		if err := json.Unmarshal(data, &serializable); err != nil {
			return errors.Wrap(err, "failed to unmarshal serializable result")
		}
		result.Data = serializable.Data
		if serializable.ErrorMsg != "" {
			result.Error = errors.New(serializable.ErrorMsg)
		}
		return nil
	}
	return json.Unmarshal(data, v)
}

// SerializableResult 可序列化的结果结构体
type SerializableResult struct {
	Data     any    `json:"data"`
	ErrorMsg string `json:"error_msg,omitempty"`
}

// GOBSerializer GOB序列化器
type GOBSerializer struct{}

// NewGOBSerializer 创建GOB序列化器
func NewGOBSerializer() *GOBSerializer {
	return &GOBSerializer{}
}

// Serialize 序列化数据为GOB
func (s *GOBSerializer) Serialize(data any) ([]byte, error) {
	var buf bytes.Buffer
	encoder := gob.NewEncoder(&buf)

	// 处理error类型的特殊序列化
	if result, ok := data.(*Result); ok {
		serializable := &SerializableResult{
			Data: result.Data,
		}
		if result.Error != nil {
			serializable.ErrorMsg = result.Error.Error()
		}
		if err := encoder.Encode(serializable); err != nil {
			return nil, errors.Wrap(err, "failed to encode serializable result")
		}
	} else {
		if err := encoder.Encode(data); err != nil {
			return nil, errors.Wrap(err, "failed to encode data")
		}
	}

	return buf.Bytes(), nil
}

// Deserialize 从GOB反序列化数据
func (s *GOBSerializer) Deserialize(data []byte, v any) error {
	buf := bytes.NewBuffer(data)
	decoder := gob.NewDecoder(buf)

	// 处理Result类型的特殊反序列化
	if result, ok := v.(*Result); ok {
		var serializable SerializableResult
		if err := decoder.Decode(&serializable); err != nil {
			return errors.Wrap(err, "failed to decode serializable result")
		}
		result.Data = serializable.Data
		if serializable.ErrorMsg != "" {
			result.Error = errors.New(serializable.ErrorMsg)
		}
		return nil
	}

	return decoder.Decode(v)
}
