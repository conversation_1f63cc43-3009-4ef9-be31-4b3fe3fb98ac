package singleflight

import (
	"context"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestBroadcastPattern 测试新的广播模式
func TestBroadcastPattern(t *testing.T) {
	rdb, cleanup := getTestRedisClient(t)
	defer cleanup()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	var callCount int32
	testKey := "broadcast-test"
	expectedResult := "broadcast-result"

	fn := func() (any, error) {
		atomic.AddInt32(&callCount, 1)
		time.Sleep(200 * time.Millisecond) // 模拟耗时操作
		return expectedResult, nil
	}

	// 启动多个并发请求
	const numGoroutines = 10
	var wg sync.WaitGroup
	results := make([]any, numGoroutines)
	errors_ := make([]error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			result, err := sf.Do(ctx, testKey, fn)
			results[index] = result
			errors_[index] = err
		}(i)
	}

	wg.Wait()

	// 验证结果
	assert.Equal(t, int32(1), atomic.LoadInt32(&callCount), "函数应该只被调用一次")

	for i := 0; i < numGoroutines; i++ {
		assert.NoError(t, errors_[i], "不应该有错误")
		assert.Equal(t, expectedResult, results[i], "所有结果应该相同")
	}

	// 验证没有缓存结果残留
	// 等待一小段时间后再次调用，应该重新执行
	time.Sleep(100 * time.Millisecond)
	
	result2, err2 := sf.Do(ctx, testKey, fn)
	assert.NoError(t, err2)
	assert.Equal(t, expectedResult, result2)
	assert.Equal(t, int32(2), atomic.LoadInt32(&callCount), "第二次调用应该重新执行函数")
}

// TestNoCacheAfterExecution 测试执行完成后没有缓存
func TestNoCacheAfterExecution(t *testing.T) {
	rdb, cleanup := getTestRedisClient(t)
	defer cleanup()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	var callCount int32
	testKey := "no-cache-test"

	fn := func() (any, error) {
		count := atomic.AddInt32(&callCount, 1)
		return count, nil
	}

	// 第一次调用
	result1, err1 := sf.Do(ctx, testKey, fn)
	assert.NoError(t, err1)
	assert.Equal(t, int32(1), result1)

	// 立即第二次调用，应该重新执行
	result2, err2 := sf.Do(ctx, testKey, fn)
	assert.NoError(t, err2)
	assert.Equal(t, int32(2), result2)

	// 验证调用次数
	assert.Equal(t, int32(2), atomic.LoadInt32(&callCount))
}

// TestConcurrentDifferentKeys 测试不同key的并发执行
func TestConcurrentDifferentKeys(t *testing.T) {
	rdb, cleanup := getTestRedisClient(t)
	defer cleanup()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	var callCount1, callCount2 int32

	fn1 := func() (any, error) {
		atomic.AddInt32(&callCount1, 1)
		time.Sleep(100 * time.Millisecond)
		return "result1", nil
	}

	fn2 := func() (any, error) {
		atomic.AddInt32(&callCount2, 1)
		time.Sleep(100 * time.Millisecond)
		return "result2", nil
	}

	var wg sync.WaitGroup

	// 并发调用不同的key
	for i := 0; i < 5; i++ {
		wg.Add(2)
		go func() {
			defer wg.Done()
			result, err := sf.Do(ctx, "key1", fn1)
			assert.NoError(t, err)
			assert.Equal(t, "result1", result)
		}()
		go func() {
			defer wg.Done()
			result, err := sf.Do(ctx, "key2", fn2)
			assert.NoError(t, err)
			assert.Equal(t, "result2", result)
		}()
	}

	wg.Wait()

	// 每个key的函数应该只被调用一次
	assert.Equal(t, int32(1), atomic.LoadInt32(&callCount1))
	assert.Equal(t, int32(1), atomic.LoadInt32(&callCount2))
}

// TestErrorBroadcast 测试错误也能正确广播
func TestErrorBroadcast(t *testing.T) {
	rdb, cleanup := getTestRedisClient(t)
	defer cleanup()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	var callCount int32
	testKey := "error-broadcast-test"

	fn := func() (any, error) {
		atomic.AddInt32(&callCount, 1)
		time.Sleep(100 * time.Millisecond)
		return nil, assert.AnError
	}

	// 并发调用
	const numGoroutines = 5
	var wg sync.WaitGroup
	results := make([]any, numGoroutines)
	errors_ := make([]error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			result, err := sf.Do(ctx, testKey, fn)
			results[index] = result
			errors_[index] = err
		}(i)
	}

	wg.Wait()

	// 验证结果
	assert.Equal(t, int32(1), atomic.LoadInt32(&callCount), "函数应该只被调用一次")

	for i := 0; i < numGoroutines; i++ {
		assert.Error(t, errors_[i], "应该有错误")
		assert.Nil(t, results[i], "结果应该为nil")
	}
}
