# 分布式SingleFlight

基于Redis实现的分布式SingleFlight，用于防止缓存击穿和减少重复计算。

## 功能特性

- **分布式锁机制**：使用Redis的SET NX EX命令实现分布式锁
- **直接结果广播**：通过Redis Pub/Sub直接广播执行结果，无需缓存
- **实时性保证**：只有执行过程中的并发请求才能获得相同结果，避免过期缓存问题
- **超时控制**：支持函数执行超时和等待超时
- **多种序列化方式**：支持JSON和GOB两种序列化方式
- **错误处理**：正确处理和传播错误信息

## 使用方法

### 基本用法

```go
package main

import (
    "context"
    "fmt"
    "time"
    
    "github.com/redis/go-redis/v9"
    "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/singleflight"
)

func main() {
    // 创建Redis客户端
    rdb := redis.NewClient(&redis.Options{
        Addr: "localhost:6379",
        DB:   0,
    })
    defer rdb.Close()
    
    // 创建分布式SingleFlight实例
    sf := singleflight.NewDistributedSingleFlight(rdb)
    
    ctx := context.Background()
    
    // 定义一个耗时的函数
    expensiveFunction := func() (any, error) {
        time.Sleep(2 * time.Second) // 模拟耗时操作
        return "expensive result", nil
    }
    
    // 执行函数，相同key的并发调用只会执行一次
    result, err := sf.Do(ctx, "my-key", expensiveFunction)
    if err != nil {
        fmt.Printf("Error: %v\n", err)
        return
    }
    
    fmt.Printf("Result: %v\n", result)
}
```

### 执行时配置选项

新的设计将配置选项作为执行时参数，而不是实例属性，这样一个SingleFlight实例可以满足不同场景的需求：

```go
// 使用自定义配置执行
result, err := sf.Do(ctx, "my-key", expensiveFunction,
    singleflight.WithLockExpire(30*time.Second),      // 锁过期时间
    singleflight.WithWaitTimeout(60*time.Second),     // 等待超时时间
    singleflight.WithSerializer(singleflight.NewJSONSerializer()), // 序列化器
    singleflight.WithFunctionTimeout(10*time.Second), // 函数执行超时时间
)
```

### 超时控制

```go
// 设置函数执行超时时间
result, err := sf.Do(ctx, "my-key", expensiveFunction,
    singleflight.WithFunctionTimeout(5*time.Second))

// 向后兼容的方法
result, err := sf.DoWithTimeout(ctx, "my-key", expensiveFunction, 5*time.Second)
```

### 序列化器选择

```go
// 使用JSON序列化器（默认）
result1, err := sf.Do(ctx, "json-key", expensiveFunction,
    singleflight.WithSerializer(singleflight.NewJSONSerializer()))

// 使用GOB序列化器
result2, err := sf.Do(ctx, "gob-key", expensiveFunction,
    singleflight.WithSerializer(singleflight.NewGOBSerializer()))
```

### 不同场景使用不同配置

```go
// 场景1：快速缓存，短过期时间
quickResult, err := sf.Do(ctx, "quick-data", quickFunction,
    singleflight.WithResultExpire(1*time.Minute),
    singleflight.WithLockExpire(5*time.Second),
)

// 场景2：重要数据，长过期时间，GOB序列化
importantResult, err := sf.Do(ctx, "important-data", importantFunction,
    singleflight.WithResultExpire(1*time.Hour),
    singleflight.WithLockExpire(60*time.Second),
    singleflight.WithSerializer(singleflight.NewGOBSerializer()),
)

// 场景3：可能很慢的操作，设置超时
slowResult, err := sf.Do(ctx, "slow-operation", slowFunction,
    singleflight.WithFunctionTimeout(30*time.Second),
    singleflight.WithWaitTimeout(45*time.Second),
)
```

```go
// 使用JSON序列化器（默认）
sf1 := singleflight.NewDistributedSingleFlight(rdb, 
    singleflight.WithSerializer(singleflight.NewJSONSerializer()))

// 使用GOB序列化器
sf2 := singleflight.NewDistributedSingleFlight(rdb, 
    singleflight.WithSerializer(singleflight.NewGOBSerializer()))
```

### 缓存管理

```go
// 清除指定key的缓存
err := sf.Forget(ctx, "my-key")

// 清除所有缓存
err := sf.ForgetAll(ctx)
```

## 工作原理

1. **获取锁**：使用Redis的`SET key value NX EX expire`命令尝试获取分布式锁
2. **执行函数**：获得锁的请求执行函数，其他请求等待
3. **保存结果**：将执行结果和错误信息序列化后保存到Redis
4. **广播信号**：使用Redis Pub/Sub广播执行完成信号
5. **获取结果**：等待的请求收到信号后从Redis获取结果

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| LockExpire | 30秒 | 分布式锁的过期时间 |
| WaitTimeout | 60秒 | 等待结果的超时时间 |
| ResultExpire | 5分钟 | 结果在Redis中的过期时间 |
| Serializer | JSON | 序列化器类型 |

## 注意事项

1. **Redis连接**：确保Redis服务可用且网络连接稳定
2. **序列化兼容性**：JSON序列化器兼容性更好，GOB序列化器性能更高但只能在Go程序间使用
3. **错误处理**：函数执行的错误也会被缓存和传播
4. **内存使用**：大量的缓存结果会占用Redis内存，建议合理设置过期时间
5. **网络延迟**：分布式环境下的网络延迟可能影响性能

## 测试

本项目使用`github.com/alicebob/miniredis/v2`来创建内存中的Redis实例进行测试，无需依赖外部Redis服务。

运行测试：

```bash
go test -v ./common/singleflight/
```

运行测试并查看覆盖率：

```bash
go test -v ./common/singleflight/ -cover
```

运行示例：

```bash
go test -v ./common/singleflight/ -run Example
```

## 依赖

- `github.com/redis/go-redis/v9` - Redis客户端
- `github.com/pkg/errors` - 错误处理
- `github.com/zeromicro/go-zero/core/logx` - 日志记录
- `github.com/alicebob/miniredis/v2` - 测试用内存Redis（仅测试时）
