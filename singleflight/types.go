package singleflight

import "time"

// Result 执行结果结构体
type Result struct {
	Data  any   `json:"data"`
	Error error `json:"error"`
}

// Serializer 序列化器接口
type Serializer interface {
	// Serialize 序列化数据
	Serialize(data any) ([]byte, error)
	// Deserialize 反序列化数据
	Deserialize(data []byte, v any) error
}

type (
	// ExecuteFunc 执行函数
	ExecuteFunc func() (any, error)

	// ExecuteOption 执行选项函数
	ExecuteOption func(*ExecuteOptions)

	// ExecuteOptions 执行选项
	ExecuteOptions struct {
		// Serializer 序列化器，默认使用JSON
		Serializer Serializer
		// LockExpire 锁过期时间，默认30秒
		LockExpire time.Duration
		// WaitTimeout 等待超时时间，默认60秒
		WaitTimeout time.Duration
		// FunctionTimeout 函数执行超时时间，0表示不超时
		FunctionTimeout time.Duration
	}
)

// WithSerializer 设置序列化器
func WithSerializer(serializer Serializer) ExecuteOption {
	return func(opts *ExecuteOptions) {
		opts.Serializer = serializer
	}
}

// WithLockExpire 设置锁过期时间
func WithLockExpire(expire time.Duration) ExecuteOption {
	return func(opts *ExecuteOptions) {
		opts.LockExpire = expire
	}
}

// WithWaitTimeout 设置等待超时时间
func WithWaitTimeout(timeout time.Duration) ExecuteOption {
	return func(opts *ExecuteOptions) {
		opts.WaitTimeout = timeout
	}
}



// WithFunctionTimeout 设置函数执行超时时间
func WithFunctionTimeout(timeout time.Duration) ExecuteOption {
	return func(opts *ExecuteOptions) {
		opts.FunctionTimeout = timeout
	}
}

// getDefaultOptions 获取默认选项
func getDefaultOptions() *ExecuteOptions {
	return &ExecuteOptions{
		Serializer:      NewJSONSerializer(),
		LockExpire:      defaultLockExpire,
		WaitTimeout:     defaultWaitTimeout,
		FunctionTimeout: 0, // 默认不超时
	}
}

// applyOptions 应用选项
func applyOptions(opts ...ExecuteOption) *ExecuteOptions {
	options := getDefaultOptions()
	for _, opt := range opts {
		opt(options)
	}
	return options
}
