package singleflight

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// UserService 示例用户服务
type UserService struct {
	sf  *DistributedSingleFlight
	rdb redis.UniversalClient
}

// NewUserService 创建用户服务
func NewUserService(rdb redis.UniversalClient) *UserService {
	return &UserService{
		sf:  NewDistributedSingleFlight(rdb),
		rdb: rdb,
	}
}

// User 用户结构体
type User struct {
	ID    int    `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

// GetUser 获取用户信息，使用分布式SingleFlight防止缓存击穿
func (s *UserService) GetUser(ctx context.Context, userID int) (*User, error) {
	key := fmt.Sprintf("user:%d", userID)

	result, err := s.sf.Do(
		ctx, key, func() (any, error) {
			// 模拟从数据库查询用户信息
			fmt.Printf("从数据库查询用户 %d\n", userID)
			time.Sleep(100 * time.Millisecond) // 模拟数据库查询耗时

			// 这里应该是真实的数据库查询逻辑
			user := &User{
				ID:    userID,
				Name:  fmt.Sprintf("User%d", userID),
				Email: fmt.Sprintf("<EMAIL>", userID),
			}

			return user, nil
		},
	)
	if err != nil {
		return nil, err
	}

	return result.(*User), nil
}

// GetUserWithTimeout 获取用户信息，带超时控制
func (s *UserService) GetUserWithTimeout(ctx context.Context, userID int, timeout time.Duration) (*User, error) {
	key := fmt.Sprintf("user:%d", userID)

	result, err := s.sf.Do(
		ctx, key, func() (any, error) {
			// 模拟可能很慢的数据库查询
			fmt.Printf("从数据库查询用户 %d (可能很慢)\n", userID)
			time.Sleep(2 * time.Second) // 模拟慢查询

			user := &User{
				ID:    userID,
				Name:  fmt.Sprintf("User%d", userID),
				Email: fmt.Sprintf("<EMAIL>", userID),
			}

			return user, nil
		}, WithFunctionTimeout(timeout),
	)
	if err != nil {
		return nil, err
	}

	return result.(*User), nil
}

// InvalidateUser 使用户缓存失效
func (s *UserService) InvalidateUser(ctx context.Context, userID int) error {
	key := fmt.Sprintf("user:%d", userID)
	return s.sf.Forget(ctx, key)
}

// CacheService 示例缓存服务
type CacheService struct {
	sf *DistributedSingleFlight
}

// NewCacheService 创建缓存服务
func NewCacheService(rdb redis.UniversalClient) *CacheService {
	return &CacheService{
		sf: NewDistributedSingleFlight(rdb),
	}
}

// GetOrCompute 获取或计算值
func (s *CacheService) GetOrCompute(ctx context.Context, key string, computeFn func() (any, error), opts ...ExecuteOption) (any, error) {
	return s.sf.Do(ctx, key, computeFn, opts...)
}

// GetOrComputeWithTimeout 获取或计算值，带超时控制
func (s *CacheService) GetOrComputeWithTimeout(
	ctx context.Context, key string, computeFn func() (any, error), timeout time.Duration,
) (any, error) {
	return s.sf.Do(ctx, key, computeFn, WithFunctionTimeout(timeout))
}

// Invalidate 使缓存失效
func (s *CacheService) Invalidate(ctx context.Context, key string) error {
	return s.sf.Forget(ctx, key)
}

// InvalidateAll 使所有缓存失效
func (s *CacheService) InvalidateAll(ctx context.Context) error {
	return s.sf.ForgetAll(ctx)
}

// ExampleUsage 使用示例函数
func ExampleUsage() {
	// 创建Redis客户端
	rdb := redis.NewClient(
		&redis.Options{
			Addr: "localhost:6379",
			DB:   0,
		},
	)
	defer func() {
		_ = rdb.Close()
	}()

	// 创建用户服务
	userService := NewUserService(rdb)

	ctx := context.Background()

	// 并发获取同一个用户，只会查询数据库一次
	for i := 0; i < 5; i++ {
		go func(id int) {
			user, err := userService.GetUser(ctx, 123)
			if err != nil {
				fmt.Printf("Goroutine %d: Error: %v\n", id, err)
				return
			}
			fmt.Printf("Goroutine %d: User: %+v\n", id, user)
		}(i)
	}

	time.Sleep(1 * time.Second) // 等待所有goroutine完成

	// 使缓存失效
	_ = userService.InvalidateUser(ctx, 123)

	// 再次获取，会重新查询数据库
	user, _ := userService.GetUser(ctx, 123)
	fmt.Printf("After invalidation: %+v\n", user)
}

// ExampleAdvancedUsage 高级使用示例
func ExampleAdvancedUsage() {
	rdb := redis.NewClient(
		&redis.Options{
			Addr: "localhost:6379",
			DB:   0,
		},
	)
	defer func() {
		_ = rdb.Close()
	}()

	// 创建缓存服务
	cacheService := NewCacheService(rdb)

	ctx := context.Background()

	// 复杂数据的缓存，使用自定义选项
	complexData, err := cacheService.GetOrCompute(
		ctx, "complex-data", func() (any, error) {
			// 模拟复杂计算
			fmt.Println("执行复杂计算...")
			time.Sleep(500 * time.Millisecond)

			return map[string]any{
				"numbers": []int{1, 2, 3, 4, 5},
				"nested": map[string]string{
					"key1": "value1",
					"key2": "value2",
				},
				"timestamp": time.Now(),
			}, nil
		},
		WithLockExpire(60*time.Second),
		WithWaitTimeout(30*time.Second),
		WithSerializer(NewGOBSerializer()),
	)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("Complex data: %+v\n", complexData)

	// 带超时的操作
	result, err := cacheService.GetOrComputeWithTimeout(
		ctx, "timeout-test", func() (any, error) {
			time.Sleep(2 * time.Second) // 超过超时时间
			return "should not reach here", nil
		}, 1*time.Second,
	)

	if err != nil {
		fmt.Printf("Timeout error: %v\n", err)
	} else {
		fmt.Printf("Result: %v\n", result)
	}
}
