package singleflight

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
)

// DemoNewDesignAdvantages 演示新设计的优势
func DemoNewDesignAdvantages() {
	// 创建miniredis实例
	mr, err := miniredis.Run()
	if err != nil {
		log.Fatal(err)
	}
	defer mr.Close()

	// 创建Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})
	defer func() {
		_ = rdb.Close()
	}()

	// 创建一个SingleFlight实例，可以满足多种场景
	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	fmt.Println("=== 演示新设计的优势：一个实例，多种配置 ===")
	fmt.Println()

	// 场景1：用户信息缓存 - 需要长期缓存，使用JSON序列化
	fmt.Println("场景1：用户信息缓存")
	userResult, err := sf.Do(ctx, "user:123", func() (interface{}, error) {
		fmt.Println("  -> 从数据库查询用户信息...")
		time.Sleep(100 * time.Millisecond)
		return map[string]interface{}{
			"id":   123,
			"name": "张三",
			"role": "admin",
		}, nil
	},
		WithResultExpire(30*time.Minute),        // 长期缓存
		WithSerializer(NewJSONSerializer()),     // JSON序列化，便于调试
		WithLockExpire(10*time.Second),         // 适中的锁时间
	)
	if err != nil {
		fmt.Printf("  错误: %v\n", err)
	} else {
		fmt.Printf("  结果: %+v\n", userResult)
	}
	fmt.Println()

	// 场景2：临时计算结果 - 短期缓存，快速过期
	fmt.Println("场景2：临时计算结果")
	tempResult, err := sf.Do(ctx, "temp:calc", func() (interface{}, error) {
		fmt.Println("  -> 执行复杂计算...")
		time.Sleep(50 * time.Millisecond)
		return "计算结果", nil
	},
		WithResultExpire(1*time.Minute),         // 短期缓存
		WithLockExpire(3*time.Second),          // 短锁时间
		WithWaitTimeout(5*time.Second),         // 短等待时间
	)
	if err != nil {
		fmt.Printf("  错误: %v\n", err)
	} else {
		fmt.Printf("  结果: %v\n", tempResult)
	}
	fmt.Println()

	// 场景3：可能很慢的外部API调用 - 需要超时控制
	fmt.Println("场景3：外部API调用（带超时）")
	apiResult, err := sf.Do(ctx, "api:external", func() (interface{}, error) {
		fmt.Println("  -> 调用外部API...")
		time.Sleep(200 * time.Millisecond) // 模拟API调用
		return "API响应数据", nil
	},
		WithFunctionTimeout(150*time.Millisecond), // 函数执行超时
		WithWaitTimeout(10*time.Second),           // 等待超时
		WithResultExpire(5*time.Minute),           // 中等缓存时间
	)
	if err != nil {
		fmt.Printf("  错误: %v\n", err)
	} else {
		fmt.Printf("  结果: %v\n", apiResult)
	}
	fmt.Println()

	// 场景4：重要业务数据 - 使用GOB序列化，性能更好
	fmt.Println("场景4：重要业务数据（GOB序列化）")
	businessResult, err := sf.Do(ctx, "business:data", func() (interface{}, error) {
		fmt.Println("  -> 查询重要业务数据...")
		time.Sleep(80 * time.Millisecond)
		return "重要业务数据", nil
	},
		WithSerializer(NewGOBSerializer()),      // GOB序列化，性能更好
		WithResultExpire(1*time.Hour),          // 长期缓存
		WithLockExpire(30*time.Second),         // 较长的锁时间
	)
	if err != nil {
		fmt.Printf("  错误: %v\n", err)
	} else {
		fmt.Printf("  结果: %v\n", businessResult)
	}
	fmt.Println()

	fmt.Println("=== 演示完成 ===")
	fmt.Println("优势总结：")
	fmt.Println("1. 一个SingleFlight实例可以处理多种不同的场景")
	fmt.Println("2. 每次调用可以根据具体需求设置不同的配置")
	fmt.Println("3. 不需要为不同场景创建多个实例")
	fmt.Println("4. 配置更加灵活和精细化")
}

// DemoOldVsNewDesign 对比旧设计和新设计
func DemoOldVsNewDesign() {
	fmt.Println("=== 设计对比演示 ===")
	fmt.Println()

	fmt.Println("旧设计（实例级配置）的问题：")
	fmt.Println("```go")
	fmt.Println("// 需要为不同场景创建不同的实例")
	fmt.Println("userSF := NewDistributedSingleFlight(rdb, WithResultExpire(30*time.Minute))")
	fmt.Println("tempSF := NewDistributedSingleFlight(rdb, WithResultExpire(1*time.Minute))")
	fmt.Println("apiSF := NewDistributedSingleFlight(rdb, WithFunctionTimeout(5*time.Second))")
	fmt.Println("```")
	fmt.Println("问题：")
	fmt.Println("- 需要管理多个实例")
	fmt.Println("- 内存占用更多")
	fmt.Println("- 配置不够灵活")
	fmt.Println()

	fmt.Println("新设计（执行时配置）的优势：")
	fmt.Println("```go")
	fmt.Println("// 一个实例处理所有场景")
	fmt.Println("sf := NewDistributedSingleFlight(rdb)")
	fmt.Println()
	fmt.Println("// 根据场景使用不同配置")
	fmt.Println("userResult, _ := sf.Do(ctx, \"user:123\", userFn, WithResultExpire(30*time.Minute))")
	fmt.Println("tempResult, _ := sf.Do(ctx, \"temp:data\", tempFn, WithResultExpire(1*time.Minute))")
	fmt.Println("apiResult, _ := sf.Do(ctx, \"api:call\", apiFn, WithFunctionTimeout(5*time.Second))")
	fmt.Println("```")
	fmt.Println("优势：")
	fmt.Println("- 单一实例，简化管理")
	fmt.Println("- 配置更加灵活")
	fmt.Println("- 内存使用更高效")
	fmt.Println("- 支持细粒度的配置控制")
}

// DemoFlexibleConfiguration 演示灵活配置的能力
func DemoFlexibleConfiguration() {
	mr, err := miniredis.Run()
	if err != nil {
		log.Fatal(err)
	}
	defer mr.Close()

	rdb := redis.NewClient(&redis.Options{Addr: mr.Addr()})
	defer rdb.Close()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	fmt.Println("=== 灵活配置演示 ===")
	fmt.Println()

	// 同一个key，不同时间使用不同配置
	key := "flexible-demo"

	fmt.Println("第一次调用：使用默认配置")
	result1, _ := sf.Do(ctx, key, func() (interface{}, error) {
		return "默认配置结果", nil
	})
	fmt.Printf("结果: %v\n\n", result1)

	// 清除缓存
	sf.Forget(ctx, key)

	fmt.Println("第二次调用：使用JSON序列化，短缓存")
	result2, _ := sf.Do(ctx, key, func() (interface{}, error) {
		return "JSON序列化结果", nil
	}, WithSerializer(NewJSONSerializer()), WithResultExpire(30*time.Second))
	fmt.Printf("结果: %v\n\n", result2)

	// 清除缓存
	sf.Forget(ctx, key)

	fmt.Println("第三次调用：使用GOB序列化，长缓存")
	result3, _ := sf.Do(ctx, key, func() (interface{}, error) {
		return "GOB序列化结果", nil
	}, WithSerializer(NewGOBSerializer()), WithResultExpire(10*time.Minute))
	fmt.Printf("结果: %v\n\n", result3)

	fmt.Println("演示完成：同一个实例，同一个key，可以在不同时间使用完全不同的配置！")
}
