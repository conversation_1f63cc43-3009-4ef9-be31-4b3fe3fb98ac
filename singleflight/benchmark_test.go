package singleflight

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
)

// BenchmarkDistributedSingleFlight_Sequential 顺序执行基准测试
func BenchmarkDistributedSingleFlight_Sequential(b *testing.B) {
	mr, err := miniredis.Run()
	if err != nil {
		b.<PERSON>al(err)
	}
	defer mr.Close()

	rdb := redis.NewClient(&redis.Options{Addr: mr.Addr()})
	defer rdb.Close()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		key := fmt.Sprintf("bench-key-%d", i)
		_, err := sf.Do(
			ctx, key, func() (any, error) {
				return "result", nil
			},
		)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkDistributedSingleFlight_Concurrent 并发执行基准测试
func BenchmarkDistributedSingleFlight_Concurrent(b *testing.B) {
	mr, err := miniredis.Run()
	if err != nil {
		b.Fatal(err)
	}
	defer mr.Close()

	rdb := redis.NewClient(&redis.Options{Addr: mr.Addr()})
	defer rdb.Close()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	b.ResetTimer()

	b.RunParallel(
		func(pb *testing.PB) {
			i := 0
			for pb.Next() {
				key := fmt.Sprintf("bench-key-%d", i%100) // 重复使用100个key
				_, err := sf.Do(
					ctx, key, func() (any, error) {
						time.Sleep(1 * time.Millisecond) // 模拟一些工作
						return "result", nil
					},
				)
				if err != nil {
					b.Fatal(err)
				}
				i++
			}
		},
	)
}

// BenchmarkDistributedSingleFlight_HighContention 高竞争基准测试
func BenchmarkDistributedSingleFlight_HighContention(b *testing.B) {
	mr, err := miniredis.Run()
	if err != nil {
		b.Fatal(err)
	}
	defer mr.Close()

	rdb := redis.NewClient(&redis.Options{Addr: mr.Addr()})
	defer rdb.Close()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	const numGoroutines = 100
	const sameKey = "high-contention-key"

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		var wg sync.WaitGroup
		wg.Add(numGoroutines)

		for j := 0; j < numGoroutines; j++ {
			go func() {
				defer wg.Done()
				_, err := sf.Do(
					ctx, sameKey, func() (any, error) {
						time.Sleep(10 * time.Millisecond) // 模拟耗时操作
						return "result", nil
					},
				)
				if err != nil {
					b.Error(err)
				}
			}()
		}

		wg.Wait()

		// 清理缓存以便下次测试
		sf.Forget(ctx, sameKey)
	}
}

// BenchmarkSerializers 序列化器性能基准测试
func BenchmarkSerializers(b *testing.B) {
	// 使用简单的字符串数据，避免GOB序列化复杂类型的问题
	testData := &Result{
		Data:  "This is a test string for benchmarking serialization performance",
		Error: nil,
	}

	b.Run(
		"JSON Serializer", func(b *testing.B) {
			serializer := NewJSONSerializer()

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				data, err := serializer.Serialize(testData)
				if err != nil {
					b.Fatal(err)
				}

				var result Result
				err = serializer.Deserialize(data, &result)
				if err != nil {
					b.Fatal(err)
				}
			}
		},
	)

	b.Run(
		"GOB Serializer", func(b *testing.B) {
			serializer := NewGOBSerializer()

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				data, err := serializer.Serialize(testData)
				if err != nil {
					b.Fatal(err)
				}

				var result Result
				err = serializer.Deserialize(data, &result)
				if err != nil {
					b.Fatal(err)
				}
			}
		},
	)
}

// BenchmarkDistributedSingleFlight_DifferentKeys 不同key的基准测试
func BenchmarkDistributedSingleFlight_DifferentKeys(b *testing.B) {
	mr, err := miniredis.Run()
	if err != nil {
		b.Fatal(err)
	}
	defer mr.Close()

	rdb := redis.NewClient(&redis.Options{Addr: mr.Addr()})
	defer rdb.Close()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	b.ResetTimer()

	b.RunParallel(
		func(pb *testing.PB) {
			i := 0
			for pb.Next() {
				key := fmt.Sprintf("unique-key-%d", i) // 每次使用不同的key
				_, err := sf.Do(
					ctx, key, func() (any, error) {
						return fmt.Sprintf("result-%d", i), nil
					},
				)
				if err != nil {
					b.Fatal(err)
				}
				i++
			}
		},
	)
}

// BenchmarkDistributedSingleFlight_WithTimeout 带超时的基准测试
func BenchmarkDistributedSingleFlight_WithTimeout(b *testing.B) {
	mr, err := miniredis.Run()
	if err != nil {
		b.Fatal(err)
	}
	defer mr.Close()

	rdb := redis.NewClient(&redis.Options{Addr: mr.Addr()})
	defer func() {
		_ = rdb.Close()
	}()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		key := fmt.Sprintf("timeout-key-%d", i)
		_, err := sf.Do(
			ctx, key, func() (any, error) {
				time.Sleep(1 * time.Millisecond)
				return "result", nil
			}, WithFunctionTimeout(100*time.Millisecond),
		)
		if err != nil {
			b.Fatal(err)
		}
	}
}
