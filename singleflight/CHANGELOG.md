# SingleFlight 更新日志

## v2.0.0 - 2025-06-18

### 🚀 重大改进：直接结果广播模式

#### 背景
原有实现采用"先缓存再广播信号"的模式：
1. 执行函数并将结果保存到Redis缓存
2. 通过Redis Pub/Sub广播完成信号
3. 等待的请求收到信号后从缓存中获取结果

这种方式存在一个问题：在执行结束到结果缓存失效的间隔内，新的请求可能直接获取到上一次执行的缓存结果，而不是重新执行函数。

#### 新实现
采用"直接结果广播"模式：
1. 执行函数
2. 将结果直接通过Redis Pub/Sub广播
3. 等待的请求直接从广播消息中获取结果

#### 改进优势

✅ **实时性保证**
- 只有在执行过程中的并发请求才能获得相同结果
- 避免了获取过期缓存结果的问题

✅ **减少Redis操作**
- 省去了结果缓存的读写操作
- 减少了Redis内存使用

✅ **避免缓存污染**
- 不会在Redis中留下大量缓存数据
- 消除了缓存失效间隔的问题

✅ **更好的并发安全性**
- 修复了超时机制中的竞态条件问题
- 通过channel传递结果，避免共享变量竞争

### 🔧 技术改进

#### 核心变更
- **移除结果缓存机制**：不再将结果保存到Redis
- **直接广播结果**：通过Pub/Sub直接传递序列化后的结果
- **修复竞态条件**：使用channel避免goroutine间的变量竞争
- **简化配置选项**：移除`WithResultExpire`选项

#### API变更
```go
// 移除的配置选项
WithResultExpire(time.Duration) // 已删除

// 保留的配置选项
WithLockExpire(time.Duration)      // 锁过期时间
WithWaitTimeout(time.Duration)     // 等待超时时间
WithFunctionTimeout(time.Duration) // 函数执行超时时间
WithSerializer(Serializer)         // 序列化器
```

#### 方法语义变更
```go
// Forget 和 ForgetAll 现在主要用于清理锁
sf.Forget(ctx, "key")    // 清理可能残留的锁
sf.ForgetAll(ctx)        // 清理所有可能残留的锁
```

### 🧪 测试改进

#### 新增测试用例
- `TestBroadcastPattern`: 验证新的广播模式
- `TestNoCacheAfterExecution`: 验证执行完成后无缓存
- `TestConcurrentDifferentKeys`: 验证不同key的并发执行
- `TestErrorBroadcast`: 验证错误也能正确广播

#### 竞态检测
- 修复了超时机制中的竞态条件
- 所有测试通过`go test -race`检测

### 📚 文档更新

#### README.md
- 更新了功能特性说明
- 修正了配置参数表
- 添加了新实现的优势说明
- 更新了使用示例

#### 新增文件
- `broadcast_test.go`: 新广播模式的专项测试
- `example_broadcast.go`: 演示新功能的示例代码
- `CHANGELOG.md`: 本更新日志

### 🔄 迁移指南

#### 从v1.x升级到v2.0

1. **移除ResultExpire配置**
```go
// v1.x (旧版本)
sf.Do(ctx, key, fn, 
    WithResultExpire(5*time.Minute), // 需要移除
    WithLockExpire(30*time.Second),
)

// v2.0 (新版本)
sf.Do(ctx, key, fn,
    WithLockExpire(30*time.Second), // 保持不变
)
```

2. **理解新的行为**
- 执行完成后不会有缓存残留
- 每次新的调用都会重新执行函数（除非有并发执行）
- `Forget`方法现在主要用于清理锁

3. **测试验证**
- 确保应用逻辑适应新的无缓存行为
- 验证并发场景下的正确性

### 🎯 性能影响

#### 正面影响
- 减少Redis内存使用
- 减少Redis读写操作
- 避免缓存管理开销

#### 注意事项
- 每次调用都会重新执行函数（符合预期）
- 依赖Redis Pub/Sub的可靠性
- 网络延迟可能影响广播效果

### 🔮 未来计划

- 考虑添加可选的短期缓存机制
- 优化序列化性能
- 增强错误处理和重试机制
- 支持更多的序列化格式

---

**注意**: 这是一个破坏性更新，请在升级前仔细阅读迁移指南并进行充分测试。
