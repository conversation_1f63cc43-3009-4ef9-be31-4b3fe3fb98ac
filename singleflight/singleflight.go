package singleflight

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
)

const (
	// 默认配置
	defaultLockExpire  = 30 * time.Second // 锁过期时间
	defaultWaitTimeout = 60 * time.Second // 等待超时时间

	// Redis key前缀
	lockKeyPrefix = "singleflight:lock:"
	channelPrefix = "singleflight:channel:"
)

// DistributedSingleFlight 分布式SingleFlight实现
type DistributedSingleFlight struct {
	rdb redis.UniversalClient
}

// NewDistributedSingleFlight 创建分布式SingleFlight实例
func NewDistributedSingleFlight(rdb redis.UniversalClient) *DistributedSingleFlight {
	return &DistributedSingleFlight{
		rdb: rdb,
	}
}

// Do 执行函数，确保相同key的函数只执行一次
func (sf *DistributedSingleFlight) Do(ctx context.Context, key string, fn ExecuteFunc, opts ...ExecuteOption) (
	any, error,
) {
	options := applyOptions(opts...)

	// 尝试获取分布式锁
	acquired, err := sf.tryLock(ctx, genLockKey(key), options.LockExpire)
	if err != nil {
		return nil, errors.Wrap(err, "failed to try lock")
	}

	if acquired {
		// 获得锁，执行函数
		return sf.executeFunction(ctx, key, fn, options)
	} else {
		// 未获得锁，等待结果
		return sf.waitForResult(ctx, key, options)
	}
}

// tryLock 尝试获取分布式锁
func (sf *DistributedSingleFlight) tryLock(ctx context.Context, lockKey string, lockExpire time.Duration) (
	bool, error,
) {
	result, err := sf.rdb.SetNX(ctx, lockKey, "1", lockExpire).Result()
	if err != nil {
		return false, errors.Wrap(err, "failed to set lock")
	}
	return result, nil
}

// executeFunction 执行函数并广播结果
func (sf *DistributedSingleFlight) executeFunction(
	ctx context.Context, key string, fn ExecuteFunc, options *ExecuteOptions,
) (any, error) {
	logger := logx.WithContext(ctx)

	// 确保释放锁
	defer func() {
		if err := sf.rdb.Del(ctx, genLockKey(key)).Err(); err != nil {
			logger.Errorf("failed to release the lock, key: %s, error: %v", key, err)
		}
	}()

	var (
		fnRet any
		fnErr error
	)

	// 执行函数，支持超时
	if options.FunctionTimeout > 0 {
		type result struct {
			data any
			err  error
		}
		resultChan := make(chan result, 1)

		threading.GoSafeCtx(
			ctx, func() {
				data, err := fn()
				resultChan <- result{data: data, err: err}
			},
		)

		timer := time.NewTimer(options.FunctionTimeout)
		defer timer.Stop()

		select {
		case res := <-resultChan:
			// 函数执行完成
			fnRet, fnErr = res.data, res.err
		case <-timer.C:
			fnErr = errors.New("function execution timeout")
		case <-ctx.Done():
			fnErr = ctx.Err()
		}
	} else {
		fnRet, fnErr = fn()
	}

	// 直接通过Redis pub/sub广播结果
	result := &Result{
		Data:  fnRet,
		Error: fnErr,
	}

	// 序列化结果
	serializedResult, err := options.Serializer.Serialize(result)
	if err != nil {
		logger.Errorf("failed to serialize result for key %s: %v", key, err)
		// 即使序列化失败，也要广播一个错误结果
		errorResult := &Result{
			Data:  nil,
			Error: errors.Wrap(err, "failed to serialize result"),
		}
		if serializedError, serErr := options.Serializer.Serialize(errorResult); serErr == nil {
			serializedResult = serializedError
		} else {
			// 如果连错误都无法序列化，发送一个简单的错误消息
			serializedResult = []byte(`{"data":null,"error":"serialization failed"}`)
		}
	}

	// 广播结果
	if err := sf.rdb.Publish(ctx, genChannelKey(key), string(serializedResult)).Err(); err != nil {
		logger.Errorf("failed to publish result for key %s: %v", key, err)
	}

	return fnRet, fnErr
}

// waitForResult 等待执行结果
func (sf *DistributedSingleFlight) waitForResult(
	ctx context.Context, key string, options *ExecuteOptions,
) (any, error) {
	// 订阅结果通道
	ps := sf.rdb.Subscribe(ctx, genChannelKey(key))
	defer func() {
		_ = ps.Close()
	}()

	// 等待结果或超时
	select {
	case msg := <-ps.Channel():
		// 收到结果消息，直接反序列化
		if msg == nil {
			return nil, errors.New("received nil message")
		}

		var result Result
		if err := options.Serializer.Deserialize([]byte(msg.Payload), &result); err != nil {
			return nil, errors.Wrap(err, "failed to deserialize result from message")
		}
		return result.Data, result.Error

	case <-time.After(options.WaitTimeout):
		return nil, errors.New("wait for result timeout")

	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// Forget 删除指定key的锁（如果存在）
// 注意：由于结果不再缓存，此方法主要用于清理可能残留的锁
func (sf *DistributedSingleFlight) Forget(ctx context.Context, key string) error {
	lockKey := genLockKey(key)
	return sf.rdb.Del(ctx, lockKey).Err()
}

// ForgetAll 删除所有锁（如果存在）
// 注意：由于结果不再缓存，此方法主要用于清理可能残留的锁
func (sf *DistributedSingleFlight) ForgetAll(ctx context.Context) error {
	pattern := lockKeyPrefix + "*"
	keys, err := sf.rdb.Keys(ctx, pattern).Result()
	if err != nil {
		return errors.Wrap(err, "failed to get keys")
	}

	if len(keys) == 0 {
		return nil
	}

	return sf.rdb.Del(ctx, keys...).Err()
}

func genLockKey(key string) string {
	return lockKeyPrefix + key
}

func genChannelKey(key string) string {
	return channelPrefix + key
}
