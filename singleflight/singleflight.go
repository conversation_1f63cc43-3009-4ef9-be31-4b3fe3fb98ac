package singleflight

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
)

const (
	// 默认配置
	defaultLockExpire  = 30 * time.Second // 锁过期时间
	defaultWaitTimeout = 60 * time.Second // 等待超时时间

	// Redis key前缀
	lockKeyPrefix = "singleflight:lock:"
	channelPrefix = "singleflight:channel:"
)

// DistributedSingleFlight 分布式SingleFlight实现
type DistributedSingleFlight struct {
	rdb redis.UniversalClient
}

// NewDistributedSingleFlight 创建分布式SingleFlight实例
func NewDistributedSingleFlight(rdb redis.UniversalClient) *DistributedSingleFlight {
	return &DistributedSingleFlight{
		rdb: rdb,
	}
}

// Do 执行函数，确保相同key的函数只执行一次
func (sf *DistributedSingleFlight) Do(ctx context.Context, key string, fn ExecuteFunc, opts ...ExecuteOption) (
	any, error,
) {
	options := applyOptions(opts...)

	// 尝试获取分布式锁
	acquired, err := sf.tryLock(ctx, genLockKey(key), options.LockExpire)
	if err != nil {
		return nil, errors.Wrap(err, "failed to try lock")
	}

	if acquired {
		// 获得锁，执行函数
		return sf.executeFunction(ctx, key, fn, options)
	} else {
		// 未获得锁，等待结果
		return sf.waitForResult(ctx, key, options)
	}
}

// tryLock 尝试获取分布式锁
func (sf *DistributedSingleFlight) tryLock(ctx context.Context, lockKey string, lockExpire time.Duration) (
	bool, error,
) {
	result, err := sf.rdb.SetNX(ctx, lockKey, "1", lockExpire).Result()
	if err != nil {
		return false, errors.Wrap(err, "failed to set lock")
	}
	return result, nil
}

// executeFunction 执行函数并保存结果
func (sf *DistributedSingleFlight) executeFunction(
	ctx context.Context, key string, fn ExecuteFunc, options *ExecuteOptions,
) (any, error) {
	logger := logx.WithContext(ctx)

	// 确保释放锁
	defer func() {
		lockKey := genLockKey(key)
		if err := sf.rdb.Del(ctx, lockKey).Err(); err != nil {
			logger.Errorf("failed to release the lock, key: %s, error: %v", lockKey, err)
		}
	}()

	var (
		fnRet any
		fnErr error
	)

	// 执行函数，支持超时
	if options.FunctionTimeout > 0 {
		done := make(chan lang.PlaceholderType)
		threading.GoSafeCtx(
			ctx, func() {
				defer close(done)
				fnRet, fnErr = fn()
			},
		)

		timer := time.NewTimer(options.FunctionTimeout)
		defer timer.Stop()

		select {
		case <-done:
			// 函数执行完成
		case <-timer.C:
			fnErr = errors.New("function execution timeout")
		case <-ctx.Done():
			fnErr = ctx.Err()
		}
	} else {
		fnRet, fnErr = fn()
	}

	// 保存结果到Redis
	resultKey := genResultKey(key)
	if err := sf.saveResult(ctx, resultKey, fnRet, fnErr, options); err != nil {
		logger.Errorf("failed to save the result, key %s, error: %v", resultKey, err)
	}

	// 广播执行完成信号
	if err := sf.rdb.Publish(ctx, genChannelKey(key), "done").Err(); err != nil {
		logger.Errorf("failed to publish completion signal for key %s: %v", key, err)
	}

	return fnRet, fnErr
}

// waitForResult 等待执行结果
func (sf *DistributedSingleFlight) waitForResult(
	ctx context.Context, key string, options *ExecuteOptions,
) (any, error) {
	// 订阅完成信号
	ps := sf.rdb.Subscribe(ctx, genChannelKey(key))
	defer func() {
		_ = ps.Close()
	}()

	resultKey := genResultKey(key)
	// 订阅后再次检查是否已有结果（避免竞态条件）
	if result, err := sf.loadResult(ctx, resultKey, options); err == nil {
		return result.Data, result.Error
	}

	// 等待信号或超时
	select {
	case <-ps.Channel():
		// 收到完成信号，获取结果
		result, err := sf.loadResult(ctx, resultKey, options)
		if err != nil {
			return nil, errors.Wrap(err, "failed to load result after signal")
		}
		return result.Data, result.Error

	case <-time.After(options.WaitTimeout):
		return nil, errors.New("wait for result timeout")

	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// saveResult 保存执行结果到Redis
func (sf *DistributedSingleFlight) saveResult(
	ctx context.Context, resultKey string, data any, err error, options *ExecuteOptions,
) error {
	result := &Result{
		Data:  data,
		Error: err,
	}

	serializedData, serErr := options.Serializer.Serialize(result)
	if serErr != nil {
		return errors.Wrap(serErr, "failed to serialize result")
	}

	return sf.rdb.Set(ctx, resultKey, serializedData, options.ResultExpire).Err()
}

// loadResult 从Redis加载执行结果
func (sf *DistributedSingleFlight) loadResult(ctx context.Context, resultKey string, options *ExecuteOptions) (
	*Result, error,
) {
	data, err := sf.rdb.Get(ctx, resultKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, errors.New("result not found")
		}
		return nil, errors.Wrap(err, "failed to get result from redis")
	}

	var result Result
	if err := options.Serializer.Deserialize([]byte(data), &result); err != nil {
		return nil, errors.Wrap(err, "failed to deserialize result")
	}

	return &result, nil
}

// Forget 删除指定key的缓存结果
func (sf *DistributedSingleFlight) Forget(ctx context.Context, key string) error {
	resultKey := resultKeyPrefix + key
	return sf.rdb.Del(ctx, resultKey).Err()
}

// ForgetAll 删除所有缓存结果
func (sf *DistributedSingleFlight) ForgetAll(ctx context.Context) error {
	pattern := resultKeyPrefix + "*"
	keys, err := sf.rdb.Keys(ctx, pattern).Result()
	if err != nil {
		return errors.Wrap(err, "failed to get keys")
	}

	if len(keys) == 0 {
		return nil
	}

	return sf.rdb.Del(ctx, keys...).Err()
}

func genLockKey(key string) string {
	return lockKeyPrefix + key
}

func genResultKey(key string) string {
	return resultKeyPrefix + key
}

func genChannelKey(key string) string {
	return channelPrefix + key
}
