package singleflight

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 测试用的Redis配置
func getTestRedisClient(t *testing.T) (redis.UniversalClient, func()) {
	// 创建miniredis实例
	mr, err := miniredis.Run()
	require.NoError(t, err, "Failed to start miniredis")

	// 创建Redis客户端连接到miniredis
	rdb := redis.NewClient(
		&redis.Options{
			Addr: mr.Addr(),
		},
	)

	// 返回客户端和清理函数
	cleanup := func() {
		_ = rdb.Close()
		mr.Close()
	}

	return rdb, cleanup
}

func TestDistributedSingleFlight_Basic(t *testing.T) {
	rdb, cleanup := getTestRedisClient(t)
	defer cleanup()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	var callCount int32
	testKey := "test-key"
	expectedResult := "test-result"

	fn := func() (any, error) {
		atomic.AddInt32(&callCount, 1)
		time.Sleep(100 * time.Millisecond) // 模拟耗时操作
		return expectedResult, nil
	}

	// 并发调用
	var wg sync.WaitGroup
	results := make([]any, 10)
	errors_ := make([]error, 10)

	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			result, err := sf.Do(ctx, testKey, fn)
			results[index] = result
			errors_[index] = err
		}(i)
	}

	wg.Wait()

	// 验证结果
	assert.Equal(t, int32(1), atomic.LoadInt32(&callCount), "函数应该只被调用一次")

	for i := 0; i < 10; i++ {
		assert.NoError(t, errors_[i], "不应该有错误")
		assert.Equal(t, expectedResult, results[i], "所有结果应该相同")
	}
}

func TestDistributedSingleFlight_WithError(t *testing.T) {
	rdb, cleanup := getTestRedisClient(t)
	defer cleanup()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	var callCount int32
	testKey := "test-error-key"
	expectedError := fmt.Errorf("test error")

	fn := func() (any, error) {
		atomic.AddInt32(&callCount, 1)
		time.Sleep(100 * time.Millisecond) // 模拟耗时操作
		return nil, expectedError
	}

	// 并发调用
	var wg sync.WaitGroup
	results := make([]any, 5)
	errors_ := make([]error, 5)

	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			result, err := sf.Do(ctx, testKey, fn)
			results[index] = result
			errors_[index] = err
		}(i)
	}

	wg.Wait()

	// 验证结果
	assert.Equal(t, int32(1), atomic.LoadInt32(&callCount), "函数应该只被调用一次")

	for i := 0; i < 5; i++ {
		assert.Error(t, errors_[i], "应该有错误")
		assert.Equal(t, expectedError.Error(), errors_[i].Error(), "错误信息应该相同")
		assert.Nil(t, results[i], "结果应该为nil")
	}
}

func TestDistributedSingleFlight_Timeout(t *testing.T) {
	rdb, cleanup := getTestRedisClient(t)
	defer cleanup()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	testKey := "test-timeout-key"
	timeout := 50 * time.Millisecond

	fn := func() (any, error) {
		time.Sleep(100 * time.Millisecond) // 超过超时时间
		return "should not reach here", nil
	}

	result, err := sf.Do(ctx, testKey, fn, WithFunctionTimeout(timeout))

	assert.Error(t, err, "应该有超时错误")
	assert.Contains(t, err.Error(), "timeout", "错误信息应该包含timeout")
	assert.Nil(t, result, "结果应该为nil")
}

func TestDistributedSingleFlight_Forget(t *testing.T) {
	rdb, cleanup := getTestRedisClient(t)
	defer cleanup()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	var callCount int32
	testKey := "test-forget-key"

	fn := func() (any, error) {
		atomic.AddInt32(&callCount, 1)
		return "result", nil
	}

	// 第一次调用
	result1, err1 := sf.Do(ctx, testKey, fn)
	assert.NoError(t, err1)
	assert.Equal(t, "result", result1)
	assert.Equal(t, int32(1), atomic.LoadInt32(&callCount))

	// 忘记锁（清理可能残留的锁）
	err := sf.Forget(ctx, testKey)
	assert.NoError(t, err)

	// 第二次调用应该重新执行（因为没有缓存，每次都会重新执行）
	result2, err2 := sf.Do(ctx, testKey, fn)
	assert.NoError(t, err2)
	assert.Equal(t, "result", result2)
	assert.Equal(t, int32(2), atomic.LoadInt32(&callCount))
}

func TestDistributedSingleFlight_ForgetAll(t *testing.T) {
	rdb, cleanup := getTestRedisClient(t)
	defer cleanup()

	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	// 创建多个缓存结果
	for i := 0; i < 3; i++ {
		key := fmt.Sprintf("test-key-%d", i)
		_, err := sf.Do(
			ctx, key, func() (any, error) {
				return fmt.Sprintf("result-%d", i), nil
			},
		)
		assert.NoError(t, err)
	}

	// 删除所有缓存
	err := sf.ForgetAll(ctx)
	assert.NoError(t, err)

	// 验证缓存已被删除（通过检查Redis中是否还有结果key）
	keys, err := rdb.Keys(ctx, resultKeyPrefix+"*").Result()
	assert.NoError(t, err)
	assert.Empty(t, keys, "所有结果key应该被删除")
}

func TestDistributedSingleFlight_ContextCancellation(t *testing.T) {
	rdb, cleanup := getTestRedisClient(t)
	defer cleanup()

	sf := NewDistributedSingleFlight(rdb)

	// 创建一个会被取消的context
	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
	defer cancel()

	// 执行一个会被context取消的操作
	_, err := sf.Do(
		ctx, "cancel-test", func() (any, error) {
			time.Sleep(200 * time.Millisecond) // 比context超时时间长
			return "should not reach here", nil
		}, WithFunctionTimeout(300*time.Millisecond),
	) // 函数超时时间比context超时时间长

	assert.Error(t, err)
	// 可能是context.DeadlineExceeded或者函数执行超时
	assert.True(t, errors.Is(err, context.DeadlineExceeded) || err.Error() == "function execution timeout")
}

func TestDistributedSingleFlight_WaitTimeout(t *testing.T) {
	rdb, cleanup := getTestRedisClient(t)
	defer cleanup()

	// 创建SingleFlight
	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	// 模拟一个长时间运行的函数
	longRunningFn := func() (any, error) {
		time.Sleep(500 * time.Millisecond)
		return "result", nil
	}

	// 启动第一个goroutine获取锁并执行函数
	go func() {
		_, _ = sf.Do(ctx, "timeout-test", longRunningFn)
	}()

	// 稍等一下确保第一个goroutine获得了锁
	time.Sleep(50 * time.Millisecond)

	// 第二个调用应该等待超时（使用短的等待超时时间）
	_, err := sf.Do(ctx, "timeout-test", longRunningFn, WithWaitTimeout(100*time.Millisecond))
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "timeout")
}

func TestSerializers(t *testing.T) {
	testData := &Result{
		Data:  "test data",
		Error: fmt.Errorf("test error"),
	}

	// 测试JSON序列化器
	t.Run(
		"JSON Serializer", func(t *testing.T) {
			serializer := NewJSONSerializer()

			data, err := serializer.Serialize(testData)
			require.NoError(t, err)

			var result Result
			err = serializer.Deserialize(data, &result)
			require.NoError(t, err)

			assert.Equal(t, testData.Data, result.Data)
			assert.Equal(t, testData.Error.Error(), result.Error.Error())
		},
	)

	// 测试GOB序列化器
	t.Run(
		"GOB Serializer", func(t *testing.T) {
			serializer := NewGOBSerializer()

			data, err := serializer.Serialize(testData)
			require.NoError(t, err)

			var result Result
			err = serializer.Deserialize(data, &result)
			require.NoError(t, err)

			assert.Equal(t, testData.Data, result.Data)
			assert.Equal(t, testData.Error.Error(), result.Error.Error())
		},
	)

	// 测试序列化非Result类型数据
	t.Run(
		"Non-Result Data", func(t *testing.T) {
			jsonSerializer := NewJSONSerializer()
			gobSerializer := NewGOBSerializer()

			testMap := map[string]any{
				"key1": "value1",
				"key2": 123,
			}

			// JSON序列化
			jsonData, err := jsonSerializer.Serialize(testMap)
			require.NoError(t, err)

			var jsonResult map[string]any
			err = jsonSerializer.Deserialize(jsonData, &jsonResult)
			require.NoError(t, err)
			assert.Equal(t, testMap["key1"], jsonResult["key1"])

			// GOB序列化
			gobData, err := gobSerializer.Serialize(testMap)
			require.NoError(t, err)

			var gobResult map[string]any
			err = gobSerializer.Deserialize(gobData, &gobResult)
			require.NoError(t, err)
			assert.Equal(t, testMap["key1"], gobResult["key1"])
		},
	)

	// 测试无错误的Result
	t.Run(
		"Result Without Error", func(t *testing.T) {
			testData := &Result{
				Data:  "test data without error",
				Error: nil,
			}

			serializer := NewJSONSerializer()
			data, err := serializer.Serialize(testData)
			require.NoError(t, err)

			var result Result
			err = serializer.Deserialize(data, &result)
			require.NoError(t, err)

			assert.Equal(t, testData.Data, result.Data)
			assert.Nil(t, result.Error)
		},
	)
}
