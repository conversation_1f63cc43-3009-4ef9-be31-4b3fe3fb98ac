package singleflight

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
)

// ExampleBroadcastPattern 演示新的广播模式
func ExampleBroadcastPattern() {
	// 创建内存Redis实例
	mr, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer mr.Close()

	// 创建Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})
	defer rdb.Close()

	// 创建SingleFlight实例
	sf := NewDistributedSingleFlight(rdb)
	ctx := context.Background()

	var callCount int32
	
	// 模拟一个耗时的数据库查询
	expensiveDBQuery := func() (any, error) {
		count := atomic.AddInt32(&callCount, 1)
		fmt.Printf("🔍 执行数据库查询 (第%d次调用)\n", count)
		
		// 模拟查询耗时
		time.Sleep(500 * time.Millisecond)
		
		return map[string]interface{}{
			"user_id": 123,
			"name":    "张三",
			"email":   "<EMAIL>",
			"timestamp": time.Now().Format("15:04:05.000"),
		}, nil
	}

	fmt.Println("🚀 演示新的广播模式 - 直接结果广播")
	fmt.Println("====================================================")

	// 启动多个并发请求
	const numRequests = 8
	var wg sync.WaitGroup
	results := make([]interface{}, numRequests)
	
	fmt.Printf("📡 启动 %d 个并发请求...\n", numRequests)
	
	startTime := time.Now()
	
	for i := 0; i < numRequests; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			
			reqStart := time.Now()
			result, err := sf.Do(ctx, "user:123", expensiveDBQuery)
			reqDuration := time.Since(reqStart)
			
			if err != nil {
				fmt.Printf("❌ 请求 %d 失败: %v\n", index+1, err)
				return
			}
			
			results[index] = result
			fmt.Printf("✅ 请求 %d 完成 (耗时: %v)\n", index+1, reqDuration)
		}(i)
	}
	
	wg.Wait()
	totalDuration := time.Since(startTime)
	
	fmt.Println("\n📊 执行结果:")
	fmt.Printf("   总耗时: %v\n", totalDuration)
	fmt.Printf("   函数调用次数: %d\n", atomic.LoadInt32(&callCount))
	fmt.Printf("   成功请求数: %d\n", numRequests)
	
	// 验证所有结果相同
	if len(results) > 0 && results[0] != nil {
		firstResult := results[0].(map[string]interface{})
		allSame := true
		
		for i := 1; i < len(results); i++ {
			if results[i] == nil {
				allSame = false
				break
			}
			currentResult := results[i].(map[string]interface{})
			if currentResult["timestamp"] != firstResult["timestamp"] {
				allSame = false
				break
			}
		}
		
		if allSame {
			fmt.Println("✅ 所有请求获得了相同的结果")
			fmt.Printf("   结果时间戳: %s\n", firstResult["timestamp"])
		} else {
			fmt.Println("❌ 请求结果不一致")
		}
	}
	
	fmt.Println("\n🔄 演示无缓存特性...")
	
	// 等待一段时间后再次调用，应该重新执行
	time.Sleep(100 * time.Millisecond)
	
	fmt.Println("📡 发起新的请求...")
	result2, err := sf.Do(ctx, "user:123", expensiveDBQuery)
	if err != nil {
		fmt.Printf("❌ 第二次请求失败: %v\n", err)
		return
	}
	
	fmt.Printf("✅ 第二次请求完成\n")
	fmt.Printf("   函数总调用次数: %d\n", atomic.LoadInt32(&callCount))
	
	if result2 != nil {
		secondResult := result2.(map[string]interface{})
		fmt.Printf("   新的时间戳: %s\n", secondResult["timestamp"])
	}
	
	fmt.Println("\n🎯 关键特性验证:")
	fmt.Println("   ✅ 并发请求只执行一次函数")
	fmt.Println("   ✅ 所有并发请求获得相同结果")
	fmt.Println("   ✅ 执行完成后不留缓存")
	fmt.Println("   ✅ 新请求会重新执行函数")
	
	// Output:
	// 🚀 演示新的广播模式 - 直接结果广播
	// ==================================================
	// 📡 启动 8 个并发请求...
	// 🔍 执行数据库查询 (第1次调用)
	// ✅ 请求 1 完成 (耗时: 500ms)
	// ✅ 请求 2 完成 (耗时: 500ms)
	// ✅ 请求 3 完成 (耗时: 500ms)
	// ✅ 请求 4 完成 (耗时: 500ms)
	// ✅ 请求 5 完成 (耗时: 500ms)
	// ✅ 请求 6 完成 (耗时: 500ms)
	// ✅ 请求 7 完成 (耗时: 500ms)
	// ✅ 请求 8 完成 (耗时: 500ms)
	//
	// 📊 执行结果:
	//    总耗时: 500ms
	//    函数调用次数: 1
	//    成功请求数: 8
	// ✅ 所有请求获得了相同的结果
	//    结果时间戳: 15:30:45.123
	//
	// 🔄 演示无缓存特性...
	// 📡 发起新的请求...
	// 🔍 执行数据库查询 (第2次调用)
	// ✅ 第二次请求完成
	//    函数总调用次数: 2
	//    新的时间戳: 15:30:45.625
	//
	// 🎯 关键特性验证:
	//    ✅ 并发请求只执行一次函数
	//    ✅ 所有并发请求获得相同结果
	//    ✅ 执行完成后不留缓存
	//    ✅ 新请求会重新执行函数
}
