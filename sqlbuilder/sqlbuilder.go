package sqlbuilder

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

func BackQuoteField(table, field string) string {
	if field == "" {
		return ""
	}
	if field[0] != '`' {
		field = "`" + field
	}
	if field[len(field)-1] != '`' {
		field = field + "`"
	}
	if table == "" {
		return field
	}
	return table + "." + field
}

type SearchOption func(builder squirrel.SelectBuilder, alias string) squirrel.SelectBuilder

type SqlizerFunc func(alias string, condition SingleCondition) squirrel.Sqlizer

var sqlizerFuncMap = map[constants.CompareOption]SqlizerFunc{
	constants.EQ: func(a string, c SingleCondition) squirrel.Sqlizer {
		other := c.GetSingleConditionOther()
		if c.<PERSON>() != constants.EQ || other == nil {
			return nil
		}
		return squirrel.Eq{BackQuoteField(a, c.GetField()): other.GetValue()}
	},
	constants.NE: func(a string, c SingleCondition) squirrel.Sqlizer {
		other := c.GetSingleConditionOther()
		if c.GetCompare() != constants.NE || other == nil {
			return nil
		}
		return squirrel.NotEq{BackQuoteField(a, c.GetField()): other.GetValue()}
	},
	constants.LT: func(a string, c SingleCondition) squirrel.Sqlizer {
		other := c.GetSingleConditionOther()
		if c.GetCompare() != constants.LT || other == nil {
			return nil
		}
		return squirrel.Lt{BackQuoteField(a, c.GetField()): other.GetValue()}
	},
	constants.LE: func(a string, c SingleCondition) squirrel.Sqlizer {
		other := c.GetSingleConditionOther()
		if c.GetCompare() != constants.LE || other == nil {
			return nil
		}
		return squirrel.LtOrEq{BackQuoteField(a, c.GetField()): other.GetValue()}
	},
	constants.GT: func(a string, c SingleCondition) squirrel.Sqlizer {
		other := c.GetSingleConditionOther()
		if c.GetCompare() != constants.GT || other == nil {
			return nil
		}
		return squirrel.Gt{BackQuoteField(a, c.GetField()): other.GetValue()}
	},
	constants.GE: func(a string, c SingleCondition) squirrel.Sqlizer {
		other := c.GetSingleConditionOther()
		if c.GetCompare() != constants.GE || other == nil {
			return nil
		}
		return squirrel.GtOrEq{BackQuoteField(a, c.GetField()): other.GetValue()}
	},
	constants.DBIn: func(a string, c SingleCondition) squirrel.Sqlizer {
		in := c.GetIn()
		if c.GetCompare() != constants.DBIn || in == nil || len(in) == 0 {
			return nil
		}
		return squirrel.Eq{BackQuoteField(a, c.GetField()): in}
	},
	constants.DBNotIn: func(a string, c SingleCondition) squirrel.Sqlizer {
		in := c.GetIn()
		if c.GetCompare() != constants.DBNotIn || in == nil || len(in) == 0 {
			return nil
		}
		return squirrel.NotEq{BackQuoteField(a, c.GetField()): in}
	},
	constants.DBLike: func(a string, c SingleCondition) squirrel.Sqlizer {
		other := c.GetSingleConditionOther()
		if c.GetCompare() != constants.DBLike || other == nil {
			return nil
		}
		return squirrel.Like{BackQuoteField(a, c.GetField()): fuzzyLikeValue(other.GetValue())}
	},
	constants.DBNotLike: func(a string, c SingleCondition) squirrel.Sqlizer {
		other := c.GetSingleConditionOther()
		if c.GetCompare() != constants.DBNotLike || other == nil {
			return nil
		}
		return squirrel.NotLike{BackQuoteField(a, c.GetField()): fuzzyLikeValue(other.GetValue())}
	},
	constants.DBBetween: func(a string, c SingleCondition) squirrel.Sqlizer {
		between := c.GetSingleConditionBetween()
		if c.GetCompare() != constants.DBBetween || between == nil {
			return nil
		}
		field := BackQuoteField(a, c.GetField())
		return squirrel.And{
			squirrel.GtOrEq{field: between.GetStart()},
			squirrel.LtOrEq{field: between.GetEnd()},
		}
	},
	constants.DBJsonContains: func(a string, c SingleCondition) squirrel.Sqlizer {
		other := c.GetSingleConditionOther()
		if c.GetCompare() != constants.DBJsonContains || other == nil {
			return nil
		}
		field := BackQuoteField(a, c.GetField())
		value := jsonx.MarshalToStringIgnoreError(other.GetValue())
		return squirrel.Expr(fmt.Sprintf("JSON_CONTAINS(%s, ?)", field), value)
	},
	constants.DBNotJsonContains: func(a string, c SingleCondition) squirrel.Sqlizer {
		other := c.GetSingleConditionOther()
		if c.GetCompare() != constants.DBNotJsonContains || other == nil {
			return nil
		}
		field := BackQuoteField(a, c.GetField())
		value := jsonx.MarshalToStringIgnoreError(other.GetValue())
		return squirrel.Expr(fmt.Sprintf("NOT JSON_CONTAINS(%s, ?)", field), value)
	},
	constants.IsNull: func(a string, c SingleCondition) squirrel.Sqlizer {
		if c.GetCompare() != constants.IsNull {
			return nil
		}
		return squirrel.Eq{BackQuoteField(a, c.GetField()): nil}
	},
	constants.IsNotNull: func(a string, c SingleCondition) squirrel.Sqlizer {
		if c.GetCompare() != constants.IsNotNull {
			return nil
		}
		return squirrel.NotEq{BackQuoteField(a, c.GetField()): nil}
	},
}

func fuzzyLikeValue(value string) string {
	if len(value) > 0 && (value[0] == '%' || value[len(value)-1] == '%') {
		return value
	} else {
		return "%" + value + "%"
	}
}

func WithCondition(dbModel types.DBModel, condition Condition) SearchOption {
	return func(builder squirrel.SelectBuilder, alias string) squirrel.SelectBuilder {
		if sqlizer := conditionGenerate(dbModel, alias, condition); sqlizer != nil {
			builder = builder.Where(sqlizer)
		}
		return builder
	}
}

func WithPagination(_ types.DBModel, pagination Pagination) SearchOption {
	return func(builder squirrel.SelectBuilder, _ string) squirrel.SelectBuilder {
		// pagination => nil: rv.IsValid() => false
		// pagination => *api.Pagination(nil) or *rpc.Pagination(nil): rv.IsValid() => true, rv.IsNil() => true

		rv := reflect.ValueOf(pagination)
		if rv.IsValid() && !rv.IsNil() {
			var offset uint64

			currentPage := pagination.GetCurrentPage()
			if currentPage == 0 {
				currentPage = ConstDefaultPaginationCurrentPage
			}

			pageSize := pagination.GetPageSize()
			if pageSize == 0 {
				pageSize = ConstDefaultPaginationPageSize
			}

			if currentPage <= 1 {
				offset = 0
			} else {
				offset = (currentPage - 1) * pageSize
			}
			return builder.Offset(offset).Limit(pageSize)
		}
		return builder
	}
}

func WithSort(dbModel types.DBModel, sort any) SearchOption {
	return func(builder squirrel.SelectBuilder, alias string) squirrel.SelectBuilder {
		// sort => nil: rv.IsValid() => false
		// sort => []*api.SortField(nil) or []*rpc.SortField(nil): rv.IsValid() => true, rv.IsNil() => true

		rv := reflect.ValueOf(sort)
		if rv.IsValid() && !rv.IsNil() && (rv.Kind() == reflect.Slice || rv.Kind() == reflect.Array) {
			for i := 0; i < rv.Len(); i++ {
				if s, ok := rv.Index(i).Interface().(SortField); s != nil && ok {
					field := s.BackQuoteField()
					if !stringx.Contains(dbModel.Fields(), field) {
						continue
					}

					order := strings.ToUpper(s.GetOrder())
					if order != constants.DESC {
						order = ConstDefaultSortOrder
					}

					if alias != "" && alias != dbModel.Table() {
						field = BackQuoteField(alias, field)
					}

					builder = builder.OrderBy(field + " " + order)
				}
			}
		}
		return builder
	}
}

func SearchOptions(builder squirrel.SelectBuilder, opts ...SearchOption) squirrel.SelectBuilder {
	return SearchOptionsWithAlias(builder, "", opts...)
}

func SearchOptionsWithAlias(builder squirrel.SelectBuilder, alias string, opts ...SearchOption) squirrel.SelectBuilder {
	for _, opt := range opts {
		builder = opt(builder, alias)
	}
	return builder
}

func conditionGenerate(dbModel types.DBModel, alias string, condition Condition) squirrel.Sqlizer {
	var sqlizer squirrel.Sqlizer

	// condition => nil: rv.IsValid() => false
	// condition => *api.Condition(nil) or *rpc.Condition(nil): rv.IsValid() => true, rv.IsNil() => true
	// condition => *api.Condition or *rpc.Condition: rv.IsValid() => true, rv.IsNil() => false

	rv := reflect.ValueOf(condition)
	if rv.IsValid() && !rv.IsNil() {
		single := condition.GetSingleCondition()
		group := condition.GetGroupCondition()
		if single != nil {
			if !stringx.Contains(dbModel.Fields(), single.BackQuoteField()) {
				return sqlizer
			}
			if fn, ok := sqlizerFuncMap[single.GetCompare()]; ok {
				return fn(alias, single)
			} else {
				return sqlizer
			}
		} else if group != nil {
			if group.GetRelationship() == constants.OR {
				sqlizer = groupGenerate(squirrel.Or{}, dbModel, alias, group.GetGroupConditions())
			} else {
				sqlizer = groupGenerate(squirrel.And{}, dbModel, alias, group.GetGroupConditions())
			}
		}
	}
	return sqlizer
}

func groupGenerate[R squirrel.And | squirrel.Or](r R, dbModel types.DBModel, alias string, conditions []Condition) R {
	for _, condition := range conditions {
		if sqlizer := conditionGenerate(dbModel, alias, condition); sqlizer != nil {
			r = append(r, sqlizer)
		}
	}
	return r
}
