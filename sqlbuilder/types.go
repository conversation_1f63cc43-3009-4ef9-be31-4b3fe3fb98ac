package sqlbuilder

// Condition 条件接口
type Condition interface {
	GetSingleCondition() SingleCondition
	GetGroupCondition() GroupCondition
}

type SingleCondition interface {
	GetField() string
	GetCompare() string
	GetIn() []string
	GetSingleConditionBetween() SingleConditionBetween
	GetSingleConditionOther() SingleConditionOther
	BackQuoteField() string
}
type GroupCondition interface {
	GetRelationship() string
	GetGroupConditions() []Condition
}
type SingleConditionBetween interface {
	GetStart() string
	GetEnd() string
}
type SingleConditionOther interface {
	GetValue() string
}

// Pagination 分页接口
type Pagination interface {
	GetCurrentPage() uint64
	GetPageSize() uint64
}

// Sort 排序接口
type Sort interface {
	GetSortSortFields() []SortField
}

type SortField interface {
	GetField() string
	GetOrder() string
	BackQuoteField() string
}
