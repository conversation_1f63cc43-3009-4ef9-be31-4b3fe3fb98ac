package utils

import (
	"bytes"
	"errors"
	"fmt"
	"strings"

	"github.com/Masterminds/squirrel"
	"github.com/lann/builder"
)

func init() {
	builder.Register(UnionBuilder{}, unionData{})
}

type unionSelect struct {
	op       string // e.g. "UNION"
	selector squirrel.SelectBuilder
}

type unionData struct {
	Selects           []*unionSelect
	Limit             string
	Offset            string
	OrderBy           []string
	PlaceholderFormat squirrel.PlaceholderFormat
}

// UnionBuilder is a (rather hack) implementation of Unions for squirrel query builder. They
// currently don't offer this feature. When they do, this code should be trashed
type UnionBuilder builder.Builder

func (u UnionBuilder) setProp(key string, value any) UnionBuilder {
	return builder.Set(u, key, value).(UnionBuilder)
}

func (u UnionBuilder) ToSql() (sql string, args []any, err error) {
	data := builder.GetStruct(u).(unionData)

	if len(data.Selects) == 0 {
		err = errors.New("require a minimum of 1 select clause in UnionBuilder")
		return sql, args, err
	}

	sqlBuf := &bytes.Buffer{}
	var selArgs []any
	var selSql string

	for index, selector := range data.Selects {
		selSql, selArgs, err = selector.selector.ToSql()
		if err != nil {
			return sql, args, err
		}

		if index == 0 {
			sqlBuf.WriteString(selSql) // no operator for first select-clause
		} else {
			sqlBuf.WriteString(" " + selector.op + " ( " + selSql + " ) ")
		}

		args = append(args, selArgs...)
	}

	if len(data.OrderBy) > 0 {
		sqlBuf.WriteString(" ORDER BY ")
		sqlBuf.WriteString(strings.Join(data.OrderBy, ","))
	}

	if data.Limit != "" {
		sqlBuf.WriteString(" LIMIT ")
		sqlBuf.WriteString(data.Limit)
	}

	if data.Offset != "" {
		sqlBuf.WriteString(" OFFSET ")
		sqlBuf.WriteString(data.Offset)
	}

	sql = sqlBuf.String()

	return sql, args, err
}

func (u UnionBuilder) Union(selector squirrel.SelectBuilder) UnionBuilder {
	// use ? in children to prevent numbering issues
	selector = selector.PlaceholderFormat(squirrel.Question)

	return builder.Append(u, "Selects", &unionSelect{op: "UNION", selector: selector}).(UnionBuilder)
}

func (u UnionBuilder) setFirstSelect(selector squirrel.SelectBuilder) UnionBuilder {
	// copy the PlaceholderFormat value from children since we don't know what it should be
	value, _ := builder.Get(selector, "PlaceholderFormat")
	bld := u.setProp("PlaceholderFormat", value)

	// use ? in children to prevent numbering issues
	selector = selector.PlaceholderFormat(squirrel.Question)

	return builder.Append(bld, "Selects", &unionSelect{op: "", selector: selector}).(UnionBuilder)
}

func (u UnionBuilder) Limit(limit uint64) UnionBuilder {
	return u.setProp("Limit", fmt.Sprintf("%d", limit))
}

func (u UnionBuilder) Offset(offset uint64) UnionBuilder {
	return u.setProp("Offset", fmt.Sprintf("%d", offset))
}

func (u UnionBuilder) OrderBy(orderBys ...string) UnionBuilder {
	return u.setProp("OrderBy", orderBys)
}

func (u UnionBuilder) PlaceholderFormat(fmt squirrel.PlaceholderFormat) UnionBuilder {
	return u.setProp("PlaceholderFormat", fmt)
}

func Union(a, b squirrel.SelectBuilder) UnionBuilder {
	ub := UnionBuilder{}
	ub = ub.setFirstSelect(a)
	return ub.Union(b)
}

func NewUnion() UnionBuilder {
	return UnionBuilder{}
}

func SelectFromUnion(selectBuilder squirrel.SelectBuilder, union UnionBuilder, alias string) squirrel.SelectBuilder {
	// use ? in child to prevent numbering issues
	union = union.PlaceholderFormat(squirrel.Question)

	return builder.Set(selectBuilder, "From", squirrel.Alias(union, alias)).(squirrel.SelectBuilder)
}
