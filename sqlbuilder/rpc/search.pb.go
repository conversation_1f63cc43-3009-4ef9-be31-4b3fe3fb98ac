// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: sqlbuilder/search.proto

package rpc

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Condition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Single        *SingleCondition       `protobuf:"bytes,1,opt,name=single,proto3" json:"single,omitempty"`
	Group         *GroupCondition        `protobuf:"bytes,2,opt,name=group,proto3" json:"group,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Condition) Reset() {
	*x = Condition{}
	mi := &file_sqlbuilder_search_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Condition) ProtoMessage() {}

func (x *Condition) ProtoReflect() protoreflect.Message {
	mi := &file_sqlbuilder_search_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Condition.ProtoReflect.Descriptor instead.
func (*Condition) Descriptor() ([]byte, []int) {
	return file_sqlbuilder_search_proto_rawDescGZIP(), []int{0}
}

func (x *Condition) GetSingle() *SingleCondition {
	if x != nil {
		return x.Single
	}
	return nil
}

func (x *Condition) GetGroup() *GroupCondition {
	if x != nil {
		return x.Group
	}
	return nil
}

type SingleCondition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`     // 字段名称
	Compare       string                 `protobuf:"bytes,2,opt,name=compare,proto3" json:"compare,omitempty"` // 比较操作
	In            []string               `protobuf:"bytes,3,rep,name=in,proto3" json:"in,omitempty"`
	Between       *Between               `protobuf:"bytes,4,opt,name=between,proto3" json:"between,omitempty"`
	Other         *Other                 `protobuf:"bytes,5,opt,name=other,proto3" json:"other,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SingleCondition) Reset() {
	*x = SingleCondition{}
	mi := &file_sqlbuilder_search_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SingleCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SingleCondition) ProtoMessage() {}

func (x *SingleCondition) ProtoReflect() protoreflect.Message {
	mi := &file_sqlbuilder_search_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SingleCondition.ProtoReflect.Descriptor instead.
func (*SingleCondition) Descriptor() ([]byte, []int) {
	return file_sqlbuilder_search_proto_rawDescGZIP(), []int{1}
}

func (x *SingleCondition) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *SingleCondition) GetCompare() string {
	if x != nil {
		return x.Compare
	}
	return ""
}

func (x *SingleCondition) GetIn() []string {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *SingleCondition) GetBetween() *Between {
	if x != nil {
		return x.Between
	}
	return nil
}

func (x *SingleCondition) GetOther() *Other {
	if x != nil {
		return x.Other
	}
	return nil
}

type GroupCondition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Relationship  string                 `protobuf:"bytes,1,opt,name=relationship,proto3" json:"relationship,omitempty"` // 关系（条件组中各条件的关系）
	Conditions    []*Condition           `protobuf:"bytes,2,rep,name=conditions,proto3" json:"conditions,omitempty"`     // 条件
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroupCondition) Reset() {
	*x = GroupCondition{}
	mi := &file_sqlbuilder_search_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupCondition) ProtoMessage() {}

func (x *GroupCondition) ProtoReflect() protoreflect.Message {
	mi := &file_sqlbuilder_search_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupCondition.ProtoReflect.Descriptor instead.
func (*GroupCondition) Descriptor() ([]byte, []int) {
	return file_sqlbuilder_search_proto_rawDescGZIP(), []int{2}
}

func (x *GroupCondition) GetRelationship() string {
	if x != nil {
		return x.Relationship
	}
	return ""
}

func (x *GroupCondition) GetConditions() []*Condition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

type Between struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Start         string                 `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	End           string                 `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Between) Reset() {
	*x = Between{}
	mi := &file_sqlbuilder_search_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Between) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Between) ProtoMessage() {}

func (x *Between) ProtoReflect() protoreflect.Message {
	mi := &file_sqlbuilder_search_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Between.ProtoReflect.Descriptor instead.
func (*Between) Descriptor() ([]byte, []int) {
	return file_sqlbuilder_search_proto_rawDescGZIP(), []int{3}
}

func (x *Between) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *Between) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

type Other struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Other) Reset() {
	*x = Other{}
	mi := &file_sqlbuilder_search_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Other) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Other) ProtoMessage() {}

func (x *Other) ProtoReflect() protoreflect.Message {
	mi := &file_sqlbuilder_search_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Other.ProtoReflect.Descriptor instead.
func (*Other) Descriptor() ([]byte, []int) {
	return file_sqlbuilder_search_proto_rawDescGZIP(), []int{4}
}

func (x *Other) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// Pagination 分页
type Pagination struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CurrentPage   uint64                 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"` // 当前页码
	PageSize      uint64                 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`          // 每页的大小
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	mi := &file_sqlbuilder_search_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_sqlbuilder_search_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_sqlbuilder_search_proto_rawDescGZIP(), []int{5}
}

func (x *Pagination) GetCurrentPage() uint64 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *Pagination) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// SortField 排序字段
type SortField struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"` // 排序字段名称
	Order         string                 `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"` // 排序方式，ASC：升序、DESC：降序
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SortField) Reset() {
	*x = SortField{}
	mi := &file_sqlbuilder_search_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SortField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortField) ProtoMessage() {}

func (x *SortField) ProtoReflect() protoreflect.Message {
	mi := &file_sqlbuilder_search_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortField.ProtoReflect.Descriptor instead.
func (*SortField) Descriptor() ([]byte, []int) {
	return file_sqlbuilder_search_proto_rawDescGZIP(), []int{6}
}

func (x *SortField) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *SortField) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

var File_sqlbuilder_search_proto protoreflect.FileDescriptor

var file_sqlbuilder_search_proto_rawDesc = []byte{
	0x0a, 0x17, 0x73, 0x71, 0x6c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x73, 0x71, 0x6c, 0x62, 0x75,
	0x69, 0x6c, 0x64, 0x65, 0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x72,
	0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x06, 0x73,
	0x69, 0x6e, 0x67, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x73, 0x71,
	0x6c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65,
	0x12, 0x30, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x73, 0x71, 0x6c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x22, 0xbc, 0x02, 0x0a, 0x0f, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x97, 0x01, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x7d, 0xfa, 0x42, 0x7a, 0x72, 0x78, 0x52, 0x02,
	0x45, 0x51, 0x52, 0x02, 0x4e, 0x45, 0x52, 0x02, 0x4c, 0x54, 0x52, 0x02, 0x4c, 0x45, 0x52, 0x02,
	0x47, 0x54, 0x52, 0x02, 0x47, 0x45, 0x52, 0x02, 0x49, 0x4e, 0x52, 0x06, 0x4e, 0x4f, 0x54, 0x5f,
	0x49, 0x4e, 0x52, 0x04, 0x4c, 0x49, 0x4b, 0x45, 0x52, 0x08, 0x4e, 0x4f, 0x54, 0x5f, 0x4c, 0x49,
	0x4b, 0x45, 0x52, 0x07, 0x42, 0x45, 0x54, 0x57, 0x45, 0x45, 0x4e, 0x52, 0x0d, 0x4a, 0x53, 0x4f,
	0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x52, 0x11, 0x4e, 0x4f, 0x54, 0x5f,
	0x4a, 0x53, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x52, 0x07, 0x49,
	0x53, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x52, 0x0b, 0x49, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4e,
	0x55, 0x4c, 0x4c, 0xd0, 0x01, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x12,
	0x18, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x2d, 0x0a, 0x07, 0x62, 0x65, 0x74,
	0x77, 0x65, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73, 0x71, 0x6c,
	0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2e, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x52,
	0x07, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x12, 0x27, 0x0a, 0x05, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x73, 0x71, 0x6c, 0x62, 0x75, 0x69,
	0x6c, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x22, 0x8c, 0x01, 0x0a, 0x0e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x68, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x72,
	0x09, 0x52, 0x03, 0x41, 0x4e, 0x44, 0x52, 0x02, 0x4f, 0x52, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x12, 0x46, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73,
	0x71, 0x6c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x31, 0x0a, 0x07, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x65, 0x6e, 0x64, 0x22, 0x1d, 0x0a, 0x05, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x62, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2c, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x32, 0x04, 0x28, 0x01, 0x40,
	0x01, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0x26,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x32, 0x04, 0x28, 0x01, 0x40, 0x01, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x55, 0x0a, 0x09, 0x53, 0x6f, 0x72, 0x74, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x12, 0x1d, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x12, 0x29, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x72, 0x0e, 0x52, 0x03, 0x41, 0x53, 0x43, 0x52, 0x04, 0x44,
	0x45, 0x53, 0x43, 0xd0, 0x01, 0x01, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x46, 0x5a,
	0x44, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x71, 0x65, 0x74, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2d,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x73, 0x71, 0x6c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65,
	0x72, 0x2f, 0x72, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_sqlbuilder_search_proto_rawDescOnce sync.Once
	file_sqlbuilder_search_proto_rawDescData = file_sqlbuilder_search_proto_rawDesc
)

func file_sqlbuilder_search_proto_rawDescGZIP() []byte {
	file_sqlbuilder_search_proto_rawDescOnce.Do(func() {
		file_sqlbuilder_search_proto_rawDescData = protoimpl.X.CompressGZIP(file_sqlbuilder_search_proto_rawDescData)
	})
	return file_sqlbuilder_search_proto_rawDescData
}

var file_sqlbuilder_search_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_sqlbuilder_search_proto_goTypes = []any{
	(*Condition)(nil),       // 0: sqlbuilder.Condition
	(*SingleCondition)(nil), // 1: sqlbuilder.SingleCondition
	(*GroupCondition)(nil),  // 2: sqlbuilder.GroupCondition
	(*Between)(nil),         // 3: sqlbuilder.Between
	(*Other)(nil),           // 4: sqlbuilder.Other
	(*Pagination)(nil),      // 5: sqlbuilder.Pagination
	(*SortField)(nil),       // 6: sqlbuilder.SortField
}
var file_sqlbuilder_search_proto_depIdxs = []int32{
	1, // 0: sqlbuilder.Condition.single:type_name -> sqlbuilder.SingleCondition
	2, // 1: sqlbuilder.Condition.group:type_name -> sqlbuilder.GroupCondition
	3, // 2: sqlbuilder.SingleCondition.between:type_name -> sqlbuilder.Between
	4, // 3: sqlbuilder.SingleCondition.other:type_name -> sqlbuilder.Other
	0, // 4: sqlbuilder.GroupCondition.conditions:type_name -> sqlbuilder.Condition
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_sqlbuilder_search_proto_init() }
func file_sqlbuilder_search_proto_init() {
	if File_sqlbuilder_search_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_sqlbuilder_search_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_sqlbuilder_search_proto_goTypes,
		DependencyIndexes: file_sqlbuilder_search_proto_depIdxs,
		MessageInfos:      file_sqlbuilder_search_proto_msgTypes,
	}.Build()
	File_sqlbuilder_search_proto = out.File
	file_sqlbuilder_search_proto_rawDesc = nil
	file_sqlbuilder_search_proto_goTypes = nil
	file_sqlbuilder_search_proto_depIdxs = nil
}
