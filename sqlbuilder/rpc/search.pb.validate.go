// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: sqlbuilder/search.proto

package rpc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Condition with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Condition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Condition with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ConditionMultiError, or nil
// if none found.
func (m *Condition) ValidateAll() error {
	return m.validate(true)
}

func (m *Condition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSingle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionValidationError{
					field:  "Single",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionValidationError{
					field:  "Single",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSingle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionValidationError{
				field:  "Single",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGroup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionValidationError{
					field:  "Group",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionValidationError{
					field:  "Group",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGroup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionValidationError{
				field:  "Group",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConditionMultiError(errors)
	}

	return nil
}

// ConditionMultiError is an error wrapping multiple validation errors returned
// by Condition.ValidateAll() if the designated constraints aren't met.
type ConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionMultiError) AllErrors() []error { return m }

// ConditionValidationError is the validation error returned by
// Condition.Validate if the designated constraints aren't met.
type ConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConditionValidationError) ErrorName() string { return "ConditionValidationError" }

// Error satisfies the builtin error interface
func (e ConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionValidationError{}

// Validate checks the field values on SingleCondition with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SingleCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SingleCondition with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SingleConditionMultiError, or nil if none found.
func (m *SingleCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *SingleCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetField()) < 1 {
		err := SingleConditionValidationError{
			field:  "Field",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCompare() != "" {

		if _, ok := _SingleCondition_Compare_InLookup[m.GetCompare()]; !ok {
			err := SingleConditionValidationError{
				field:  "Compare",
				reason: "value must be in list [EQ NE LT LE GT GE IN NOT_IN LIKE NOT_LIKE BETWEEN JSON_CONTAINS NOT_JSON_CONTAINS IS_NULL IS_NOT_NULL]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(m.GetIn()) > 0 {

	}

	if all {
		switch v := interface{}(m.GetBetween()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SingleConditionValidationError{
					field:  "Between",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SingleConditionValidationError{
					field:  "Between",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBetween()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SingleConditionValidationError{
				field:  "Between",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOther()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SingleConditionValidationError{
					field:  "Other",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SingleConditionValidationError{
					field:  "Other",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOther()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SingleConditionValidationError{
				field:  "Other",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SingleConditionMultiError(errors)
	}

	return nil
}

// SingleConditionMultiError is an error wrapping multiple validation errors
// returned by SingleCondition.ValidateAll() if the designated constraints
// aren't met.
type SingleConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SingleConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SingleConditionMultiError) AllErrors() []error { return m }

// SingleConditionValidationError is the validation error returned by
// SingleCondition.Validate if the designated constraints aren't met.
type SingleConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SingleConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SingleConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SingleConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SingleConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SingleConditionValidationError) ErrorName() string { return "SingleConditionValidationError" }

// Error satisfies the builtin error interface
func (e SingleConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSingleCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SingleConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SingleConditionValidationError{}

var _SingleCondition_Compare_InLookup = map[string]struct{}{
	"EQ":                {},
	"NE":                {},
	"LT":                {},
	"LE":                {},
	"GT":                {},
	"GE":                {},
	"IN":                {},
	"NOT_IN":            {},
	"LIKE":              {},
	"NOT_LIKE":          {},
	"BETWEEN":           {},
	"JSON_CONTAINS":     {},
	"NOT_JSON_CONTAINS": {},
	"IS_NULL":           {},
	"IS_NOT_NULL":       {},
}

// Validate checks the field values on GroupCondition with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GroupCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GroupCondition with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GroupConditionMultiError,
// or nil if none found.
func (m *GroupCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *GroupCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _GroupCondition_Relationship_InLookup[m.GetRelationship()]; !ok {
		err := GroupConditionValidationError{
			field:  "Relationship",
			reason: "value must be in list [AND OR]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetConditions()) > 0 {

		for idx, item := range m.GetConditions() {
			_, _ = idx, item

			if item == nil {
				err := GroupConditionValidationError{
					field:  fmt.Sprintf("Conditions[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GroupConditionValidationError{
							field:  fmt.Sprintf("Conditions[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GroupConditionValidationError{
							field:  fmt.Sprintf("Conditions[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GroupConditionValidationError{
						field:  fmt.Sprintf("Conditions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return GroupConditionMultiError(errors)
	}

	return nil
}

// GroupConditionMultiError is an error wrapping multiple validation errors
// returned by GroupCondition.ValidateAll() if the designated constraints
// aren't met.
type GroupConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GroupConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GroupConditionMultiError) AllErrors() []error { return m }

// GroupConditionValidationError is the validation error returned by
// GroupCondition.Validate if the designated constraints aren't met.
type GroupConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GroupConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GroupConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GroupConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GroupConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GroupConditionValidationError) ErrorName() string { return "GroupConditionValidationError" }

// Error satisfies the builtin error interface
func (e GroupConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGroupCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GroupConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GroupConditionValidationError{}

var _GroupCondition_Relationship_InLookup = map[string]struct{}{
	"AND": {},
	"OR":  {},
}

// Validate checks the field values on Between with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Between) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Between with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in BetweenMultiError, or nil if none found.
func (m *Between) ValidateAll() error {
	return m.validate(true)
}

func (m *Between) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Start

	// no validation rules for End

	if len(errors) > 0 {
		return BetweenMultiError(errors)
	}

	return nil
}

// BetweenMultiError is an error wrapping multiple validation errors returned
// by Between.ValidateAll() if the designated constraints aren't met.
type BetweenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BetweenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BetweenMultiError) AllErrors() []error { return m }

// BetweenValidationError is the validation error returned by Between.Validate
// if the designated constraints aren't met.
type BetweenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BetweenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BetweenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BetweenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BetweenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BetweenValidationError) ErrorName() string { return "BetweenValidationError" }

// Error satisfies the builtin error interface
func (e BetweenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBetween.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BetweenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BetweenValidationError{}

// Validate checks the field values on Other with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Other) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Other with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in OtherMultiError, or nil if none found.
func (m *Other) ValidateAll() error {
	return m.validate(true)
}

func (m *Other) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Value

	if len(errors) > 0 {
		return OtherMultiError(errors)
	}

	return nil
}

// OtherMultiError is an error wrapping multiple validation errors returned by
// Other.ValidateAll() if the designated constraints aren't met.
type OtherMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OtherMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OtherMultiError) AllErrors() []error { return m }

// OtherValidationError is the validation error returned by Other.Validate if
// the designated constraints aren't met.
type OtherValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OtherValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OtherValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OtherValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OtherValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OtherValidationError) ErrorName() string { return "OtherValidationError" }

// Error satisfies the builtin error interface
func (e OtherValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOther.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OtherValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OtherValidationError{}

// Validate checks the field values on Pagination with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Pagination) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Pagination with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PaginationMultiError, or
// nil if none found.
func (m *Pagination) ValidateAll() error {
	return m.validate(true)
}

func (m *Pagination) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCurrentPage() != 0 {

		if m.GetCurrentPage() < 1 {
			err := PaginationValidationError{
				field:  "CurrentPage",
				reason: "value must be greater than or equal to 1",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetPageSize() != 0 {

		if m.GetPageSize() < 1 {
			err := PaginationValidationError{
				field:  "PageSize",
				reason: "value must be greater than or equal to 1",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return PaginationMultiError(errors)
	}

	return nil
}

// PaginationMultiError is an error wrapping multiple validation errors
// returned by Pagination.ValidateAll() if the designated constraints aren't met.
type PaginationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaginationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaginationMultiError) AllErrors() []error { return m }

// PaginationValidationError is the validation error returned by
// Pagination.Validate if the designated constraints aren't met.
type PaginationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaginationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaginationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaginationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaginationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaginationValidationError) ErrorName() string { return "PaginationValidationError" }

// Error satisfies the builtin error interface
func (e PaginationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPagination.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaginationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaginationValidationError{}

// Validate checks the field values on SortField with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SortField) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SortField with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SortFieldMultiError, or nil
// if none found.
func (m *SortField) ValidateAll() error {
	return m.validate(true)
}

func (m *SortField) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetField()) < 1 {
		err := SortFieldValidationError{
			field:  "Field",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetOrder() != "" {

		if _, ok := _SortField_Order_InLookup[m.GetOrder()]; !ok {
			err := SortFieldValidationError{
				field:  "Order",
				reason: "value must be in list [ASC DESC]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return SortFieldMultiError(errors)
	}

	return nil
}

// SortFieldMultiError is an error wrapping multiple validation errors returned
// by SortField.ValidateAll() if the designated constraints aren't met.
type SortFieldMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SortFieldMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SortFieldMultiError) AllErrors() []error { return m }

// SortFieldValidationError is the validation error returned by
// SortField.Validate if the designated constraints aren't met.
type SortFieldValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SortFieldValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SortFieldValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SortFieldValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SortFieldValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SortFieldValidationError) ErrorName() string { return "SortFieldValidationError" }

// Error satisfies the builtin error interface
func (e SortFieldValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSortField.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SortFieldValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SortFieldValidationError{}

var _SortField_Order_InLookup = map[string]struct{}{
	"ASC":  {},
	"DESC": {},
}
