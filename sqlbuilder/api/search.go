package api

// Condition 条件
type Condition struct {
	Single *SingleCondition `json:"single,omitempty,optional"`
	Group  *GroupCondition  `json:"group,omitempty,optional"`
}

type SingleCondition struct {
	Field   string   `json:"field" validate:"required"`
	Compare string   `json:"compare,default=EQ" validate:"oneof=EQ NE LT LE GT GE IN NOT_IN LIKE NOT_LIKE BETWEEN JSON_CONTAINS NOT_JSON_CONTAINS IS_NULL IS_NOT_NULL"`
	In      []string `json:"in,omitempty,optional"`
	Between *Between `json:"between,omitempty,optional"`
	Other   *Other   `json:"other,omitempty,optional"`
}
type GroupCondition struct {
	Relationship string       `json:"relationship" validate:"oneof=AND OR"`
	Conditions   []*Condition `json:"conditions"`
}
type Between struct {
	Start string `json:"start"`
	End   string `json:"end"`
}
type Other struct {
	Value string `json:"value"`
}

// Pagination 分页
type Pagination struct {
	CurrentPage uint64 `json:"current_page,default=1" validate:"gte=1"`
	PageSize    uint64 `json:"page_size,default=10" validate:"gte=1"`
}

// Sort 排序
type Sort []*SortField

// SortField 排序字段
type SortField struct {
	Field string `json:"field" validate:"required"`
	Order string `json:"order,default=ASC"`
}
