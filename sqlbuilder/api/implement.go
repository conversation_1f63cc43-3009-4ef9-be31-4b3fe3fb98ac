package api

import "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"

var (
	_ sqlbuilder.Condition              = (*Condition)(nil)
	_ sqlbuilder.Pagination             = (*Pagination)(nil)
	_ sqlbuilder.Sort                   = (*Sort)(nil)
	_ sqlbuilder.SingleCondition        = (*SingleCondition)(nil)
	_ sqlbuilder.GroupCondition         = (*GroupCondition)(nil)
	_ sqlbuilder.SingleConditionBetween = (*Between)(nil)
	_ sqlbuilder.SingleConditionOther   = (*Other)(nil)
	_ sqlbuilder.SortField              = (*SortField)(nil)
)

func (x *Condition) GetSingleCondition() sqlbuilder.SingleCondition {
	if x != nil && x.Single != nil {
		return x.Single
	}
	return nil
}

func (x *Condition) GetGroupCondition() sqlbuilder.GroupCondition {
	if x != nil && x.Group != nil {
		return x.Group
	}
	return nil
}

func (x *SingleCondition) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *SingleCondition) GetCompare() string {
	if x != nil {
		return x.Compare
	}
	return ""
}

func (x *SingleCondition) GetIn() []string {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *SingleCondition) GetSingleConditionBetween() sqlbuilder.SingleConditionBetween {
	if x != nil && x.Between != nil {
		return x.Between
	}
	return nil
}

func (x *SingleCondition) GetSingleConditionOther() sqlbuilder.SingleConditionOther {
	if x != nil && x.Other != nil {
		return x.Other
	}
	return nil
}

func (x *SingleCondition) BackQuoteField() string {
	if x != nil {
		return sqlbuilder.BackQuoteField("", x.Field)
	}
	return ""
}

func (x *GroupCondition) GetRelationship() string {
	if x != nil {
		return x.Relationship
	}
	return ""
}

func (x *GroupCondition) GetGroupConditions() []sqlbuilder.Condition {
	var o []sqlbuilder.Condition
	if x != nil {
		o = make([]sqlbuilder.Condition, 0, len(x.Conditions))
		for _, c := range x.Conditions {
			o = append(o, c)
		}
	}
	return o
}

func (x *Between) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *Between) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

func (x *Other) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Pagination) GetCurrentPage() uint64 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *Pagination) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *Sort) GetSortSortFields() []sqlbuilder.SortField {
	if x != nil {
		return ConvertSortFields(*x)
	}
	return []sqlbuilder.SortField{}
}

func (x *SortField) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *SortField) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

func (x *SortField) BackQuoteField() string {
	if x != nil {
		return sqlbuilder.BackQuoteField("", x.Field)
	}
	return ""
}
