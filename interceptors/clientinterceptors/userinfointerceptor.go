package clientinterceptors

import (
	"context"
	"encoding/base64"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

// UserInfoUnaryClientInterceptor is an interceptor that set user info to metadata for unary method.
func UserInfoUnaryClientInterceptor(ctx context.Context, method string, req, reply any,
	cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption,
) error {
	ctx = handleUserInfoFromContext(ctx)

	return invoker(ctx, method, req, reply, cc, opts...)
}

// UserInfoStreamClientInterceptor is an interceptor that set user info to metadata for stream method.
func UserInfoStreamClientInterceptor(ctx context.Context, desc *grpc.StreamDesc, cc *grpc.ClientConn,
	method string, streamer grpc.Streamer, opts ...grpc.CallOption,
) (grpc.ClientStream, error) {
	ctx = handleUserInfoFromContext(ctx)

	return streamer(ctx, desc, cc, method, opts...)
}

// handleUserInfoFromContext get the user info from context, and set into metadata.
func handleUserInfoFromContext(ctx context.Context) context.Context {
	ui := userinfo.FromContext(ctx)
	if ui == nil {
		// set a default user info
		system := userinfo.System().TokenUserInfo
		ui = &system
	}

	md, ok := metadata.FromOutgoingContext(ctx)
	if !ok {
		md = metadata.MD{}
	}

	// cannot set a chinese into the metadata, please see
	// the method `hasNotPrintable` in internal/metadata/metadata.go for details.
	md.Set(userinfo.MetaDataKey, base64.StdEncoding.EncodeToString(jsonx.MarshalIgnoreError(ui)))
	ctx = metadata.NewOutgoingContext(ctx, md)

	return ctx
}

// UnaryUserInfoClientOption wraps the UserInfoUnaryClientInterceptor as a client option.
func UnaryUserInfoClientOption() zrpc.ClientOption {
	return zrpc.WithUnaryClientInterceptor(UserInfoUnaryClientInterceptor)
}
