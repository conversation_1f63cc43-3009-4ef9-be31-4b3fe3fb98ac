package serverinterceptors

import (
	"context"
	"encoding/base64"
	"strings"
	"sync"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/reflection/grpc_reflection_v1"
	"google.golang.org/grpc/reflection/grpc_reflection_v1alpha"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

const (
	slash = "/"
)

var noNeedUserMethods sync.Map

func init() {
	for _, desc := range []grpc.ServiceDesc{
		grpc_reflection_v1alpha.ServerReflection_ServiceDesc,
		grpc_reflection_v1.ServerReflection_ServiceDesc,
		grpc_health_v1.Health_ServiceDesc,
	} {
		for _, method := range desc.Methods {
			noNeedUserMethods.Store(
				strings.Join([]string{"", desc.ServiceName, method.MethodName}, slash), lang.Placeholder,
			)
		}
		for _, stream := range desc.Streams {
			noNeedUserMethods.Store(
				strings.Join([]string{"", desc.ServiceName, stream.StreamName}, slash), lang.Placeholder,
			)
		}
	}
}

type UserInfoStrategy interface {
	// NoNeedUserByFullMethod check whether the current method does not require user info
	NoNeedUserByFullMethod(fullMethod string) bool
}

// NoNeedUserForFullMethod disable check user for given method.
func NoNeedUserForFullMethod(fullMethod string) {
	noNeedUserMethods.Store(fullMethod, lang.Placeholder)
}

// UserInfoUnaryServerInterceptor is an interceptor that get user info from metadata for unary method.
func UserInfoUnaryServerInterceptor(
	ctx context.Context, req any, info *grpc.UnaryServerInfo,
	handler grpc.UnaryHandler,
) (any, error) {
	var noNeedUser, userExist bool
	noNeedUser = checkByUnaryServerInfo(info)
	ctx, userExist = handleUserInfoFromMetadata(ctx)
	if !noNeedUser && !userExist {
		return nil, errorx.ErrCurrentUserIsNull
	}

	return handler(ctx, req)
}

// UserInfoStreamServerInterceptor is an interceptor that get user info from metadata for stream method.
func UserInfoStreamServerInterceptor(
	svr any, ss grpc.ServerStream, info *grpc.StreamServerInfo,
	handler grpc.StreamHandler,
) error {
	noNeedUser := checkByFullMethod(info.FullMethod)
	ctx, userExist := handleUserInfoFromMetadata(ss.Context())
	if !noNeedUser && !userExist {
		return errorx.ErrCurrentUserIsNull
	}

	return handler(svr, wrapUserInfoServerStream(ctx, ss))
}

// checkByUnaryServerInfo check whether the current method does not require user info
func checkByUnaryServerInfo(info *grpc.UnaryServerInfo) bool {
	if v, ok := info.Server.(UserInfoStrategy); ok {
		return v.NoNeedUserByFullMethod(info.FullMethod)
	}

	// user info is required by default
	return checkByFullMethod(info.FullMethod)
}

// checkByFullMethod check whether the current method does not require user info
func checkByFullMethod(fullMethod string) bool {
	_, ok := noNeedUserMethods.Load(fullMethod)
	return ok
}

// handleUserInfoFromMetadata get the user info from metadata, and set into context.
func handleUserInfoFromMetadata(ctx context.Context) (context.Context, bool) {
	var exists bool

	if md, ok := metadata.FromIncomingContext(ctx); ok {
		values := md.Get(userinfo.MetaDataKey)
		if len(values) > 0 {
			var ui userinfo.UserInfo
			if b, err := base64.StdEncoding.DecodeString(values[0]); err == nil {
				if err = jsonx.Unmarshal(b, &ui); err == nil {
					ctx = userinfo.WithContext(ctx, &ui)
					exists = true
				}
			}
		}
	}

	return ctx, exists
}

// userInfoServerStream wraps around the embedded grpc.ServerStream,
// and intercepts the RecvMsg and SendMsg method call.
type userInfoServerStream struct {
	grpc.ServerStream
	ctx context.Context
}

// Context returns the wrapper's Context, overwriting the nested grpc.ServerStream.Context()
func (w *userInfoServerStream) Context() context.Context {
	return w.ctx
}

// wrapUserInfoServerStream wraps the given grpc.ServerStream with the given context.
func wrapUserInfoServerStream(ctx context.Context, ss grpc.ServerStream) *userInfoServerStream {
	return &userInfoServerStream{ServerStream: ss, ctx: ctx}
}
