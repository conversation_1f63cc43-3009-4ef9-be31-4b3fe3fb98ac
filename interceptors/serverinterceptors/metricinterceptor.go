package serverinterceptors

import (
	"context"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/zeromicro/go-zero/core/timex"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

const serverNamespace = "qet"

var (
	metricServerReqDur = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: serverNamespace,
			Subsystem: "rpc",
			Name:      "request_duration_ms",
			Help:      "http server requests duration(ms)!",
			Buckets:   prometheus.ExponentialBuckets(10, 2, 12), // [10 20 40 80 160 320 640 1280 2560 5120 10240 20480]
		}, []string{"method"},
	)

	metricServerReqCodeTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: serverNamespace,
			Subsystem: "rpc",
			Name:      "request_total",
			Help:      "rpc server requests count!",
		}, []string{"method", "code", "error_type"},
	)
)

func MetricUnaryServerInterceptor(
	ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler,
) (resp any, err error) {
	startTime := timex.Now()
	resp, err = handler(ctx, req)

	metricServerReqDur.WithLabelValues(info.FullMethod).Observe(float64(timex.Since(startTime) / time.Millisecond))

	if err != nil {
		e := errorx.Convert(errors.Cause(err))
		errorType := constants.BusinessError
		if errorx.IsSystemError(e.Code()) {
			errorType = constants.SystemError
		}
		metricServerReqCodeTotal.WithLabelValues(info.FullMethod, strconv.Itoa(int(e.Code())), errorType).Inc()
	} else {
		metricServerReqCodeTotal.WithLabelValues(
			info.FullMethod, strconv.Itoa(int(errorx.OK)), constants.EmptyError,
		).Inc()
	}

	return resp, err
}
