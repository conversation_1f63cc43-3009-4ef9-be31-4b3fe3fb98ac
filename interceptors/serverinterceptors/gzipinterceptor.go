package serverinterceptors

import (
	"context"

	"google.golang.org/grpc"
	_ "google.golang.org/grpc/encoding/gzip"
)

// GzipUnaryServerInterceptor is an interceptor that register gzip compressor for unary method.
func GzipUnaryServerInterceptor(ctx context.Context, req any, info *grpc.UnaryServerInfo,
	handler grpc.UnaryHandler,
) (any, error) {
	return handler(ctx, req)
}

// GzipStreamServerInterceptor is an interceptor that register gzip compressor for stream method.
func GzipStreamServerInterceptor(svr any, ss grpc.ServerStream, info *grpc.StreamServerInfo,
	handler grpc.StreamHandler,
) error {
	return handler(svr, ss)
}
