package serverinterceptors

import (
	"context"

	"github.com/bufbuild/protovalidate-go"
	"github.com/pkg/errors"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

var defaultValidator, _ = protovalidate.New()

// Option interface is currently empty and serves as a placeholder for potential future implementations.
// It allows adding new options without breaking existing code.
type Option interface {
	unimplemented()
}

// ValidateUnaryServerInterceptor is an interceptor that validate the request message for unary method.
func ValidateUnaryServerInterceptor(
	ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler,
) (any, error) {
	return ValidateWithCustomValidatorUnaryServerInterceptor(defaultValidator)(ctx, req, info, handler)
}

// ValidateStreamServerInterceptor is an interceptor that validate the request message for stream method.
func ValidateStreamServerInterceptor(
	srv any, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler,
) error {
	return ValidateWithCustomValidatorStreamServerInterceptor(defaultValidator)(srv, ss, info, handler)
}

// ValidateWithCustomValidatorUnaryServerInterceptor returns a new unary server interceptor that validates incoming messages.
func ValidateWithCustomValidatorUnaryServerInterceptor(
	validator protovalidate.Validator, opts ...Option,
) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (
		resp any, err error,
	) {
		if err = validate(validator, req); err != nil {
			return nil, err
		}

		return handler(ctx, req)
	}
}

// ValidateWithCustomValidatorStreamServerInterceptor returns a new streaming server interceptor that validates incoming messages.
func ValidateWithCustomValidatorStreamServerInterceptor(
	validator protovalidate.Validator, opts ...Option,
) grpc.StreamServerInterceptor {
	return func(srv any, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		return handler(srv, wrapValidateServerStream(ss.Context(), ss, validator))
	}
}

// validateServerStream wraps around the embedded grpc.ServerStream,
// and intercepts the RecvMsg and SendMsg method call.
type validateServerStream struct {
	grpc.ServerStream
	ctx context.Context

	validator protovalidate.Validator
}

// Context returns the wrapper's Context, overwriting the nested grpc.ServerStream.Context()
func (w *validateServerStream) Context() context.Context {
	return w.ctx
}

func (w *validateServerStream) RecvMsg(m any) error {
	if err := w.ServerStream.RecvMsg(m); err != nil {
		return err
	}

	return validate(w.validator, m)
}

// wrapValidateServerStream wraps the given grpc.ServerStream with the given context.
func wrapValidateServerStream(
	ctx context.Context, ss grpc.ServerStream, v protovalidate.Validator,
) *validateServerStream {
	return &validateServerStream{ServerStream: ss, ctx: ctx, validator: v}
}

func validate(v protovalidate.Validator, m any) error {
	switch msg := m.(type) {
	case proto.Message:
		if err := v.Validate(msg); err != nil {
			return status.Error(codes.Code(errorx.GrpcInvalidArgument), err.Error())
		}
	default:
		return errors.New("unsupported message type")
	}

	return nil
}
