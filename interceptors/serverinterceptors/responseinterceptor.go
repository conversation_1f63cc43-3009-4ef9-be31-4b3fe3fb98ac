package serverinterceptors

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

func ResponseUnaryServerInterceptor(
	ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler,
) (resp any, err error) {
	resp, err = handler(ctx, req)
	if err != nil {
		logx.WithContext(ctx).Errorf("[RPC-ERR]: %+v", err)

		_, ok := err.(interface {
			GRPCStatus() *status.Status
		})
		if !ok {
			e := errorx.Convert(errors.Cause(err))
			if errorx.IsSystemError(e.Code()) {
				err = status.Error(codes.Internal, e.Error())
			} else {
				err = status.Error(codes.Code(e.Code()), e.Message())
			}
		}
	}

	return resp, err
}
