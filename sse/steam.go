package sse

import (
	"sync"

	"github.com/zeromicro/go-zero/core/lang"
)

// Stream ...
type Stream struct {
	ID       string
	Event    chan *Event
	buffSize int
	quit     chan lang.PlaceholderType
	quitOnce sync.Once

	AutoReplay   bool
	isAutoStream bool
}

// NewStream returns a new stream
func NewStream(id string, buffSize int) *Stream {
	return &Stream{
		ID:       id,
		Event:    make(chan *Event, buffSize),
		buffSize: buffSize,
		// quit:     make(chan lang.PlaceholderType),
	}
}

func (s *Stream) Quit() {
	s.quitOnce.Do(
		func() {
			close(s.Event)
			// close(s.quit)
			// s.drain()
		},
	)
}

//func (s *Stream) QuitChan() <-chan lang.PlaceholderType {
//	return s.quit
//}

func (s *Stream) drain() {
	// drain the channel
	for {
		select {
		case <-s.Event:
			// read and discard the value
		default:
			// exit when the channel is empty
			return
		}
	}
}
