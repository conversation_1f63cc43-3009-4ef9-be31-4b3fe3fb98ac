package crypto

import (
	"crypto/aes"
	"crypto/cipher"
)

func newAesCipher(key []byte) (cipher.Block, error) {
	return aes.NewCipher(key)
}

func AesECBEncrypt(src, key []byte, padding PaddingType) ([]byte, error) {
	block, err := newAesCipher(key)
	if err != nil {
		return nil, err
	}

	return ECBEncrypt(block, src, padding)
}

func AesECBDecrypt(src, key []byte, padding PaddingType) ([]byte, error) {
	block, err := newAesCipher(key)
	if err != nil {
		return nil, err
	}

	return ECBDecrypt(block, src, padding)
}

func AesCBCEncrypt(src, key, iv []byte, padding PaddingType) ([]byte, error) {
	block, err := newAesCipher(key)
	if err != nil {
		return nil, err
	}

	return CBCEncrypt(block, src, iv, padding)
}

func AesCBCDecrypt(src, key, iv []byte, padding PaddingType) ([]byte, error) {
	block, err := newAesCipher(key)
	if err != nil {
		return nil, err
	}

	return CBCDecrypt(block, src, iv, padding)
}
