package crypto

import (
	"crypto/cipher"

	"github.com/pkg/errors"
)

var ErrCBCEncryptIVLen = errors.New("CBCEncrypt: IV length must equal block size")

func CBCEncrypt(block cipher.Block, src, iv []byte, padding PaddingType) ([]byte, error) {
	size := block.BlockSize()
	if len(iv) != size {
		return nil, ErrCBCEncryptIVLen
	}

	src = Padding(padding, src, size)
	dst := make([]byte, len(src))
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(dst, src)

	return dst, nil
}

func CBCDecrypt(block cipher.Block, src, iv []byte, padding PaddingType) ([]byte, error) {
	if len(iv) != block.BlockSize() {
		return nil, ErrCBCEncryptIVLen
	}

	dst := make([]byte, len(src))
	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(dst, src)

	return UnPadding(padding, dst)
}
