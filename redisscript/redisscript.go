package redisscript

import (
	"context"
	"crypto/sha1" //nolint:gosec
	"encoding/hex"
	"errors"
	"io"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/redis"
)

type Operation string

type ScriptedClient interface {
	EvalCtx(ctx context.Context, script string, keys []string, args ...any) (any, error)
	EvalShaCtx(ctx context.Context, sha1 string, keys []string, args ...any) (any, error)
	ScriptLoadCtx(ctx context.Context, script string) (string, error)
}

var _ ScriptedClient = (*redis.Redis)(nil)

type Script struct {
	src, hash string
}

func NewScript(src string) *Script {
	h := sha1.New()
	_, _ = io.WriteString(h, src)
	return &Script{
		src:  src,
		hash: hex.EncodeToString(h.Sum(nil)),
	}
}

func (s *Script) Hash() string {
	return s.hash
}

func (s *Script) Load(ctx context.Context, c ScriptedClient) (string, error) {
	return c.ScriptLoadCtx(ctx, s.src)
}

func (s *Script) Eval(ctx context.Context, c ScriptedClient, keys []string, args ...any) (any, error) {
	return c.EvalCtx(ctx, s.src, keys, args)
}

func (s *Script) EvalSha(ctx context.Context, c ScriptedClient, keys []string, args ...any) (any, error) {
	return c.EvalShaCtx(ctx, s.hash, keys, args...)
}

// Execute optimistically uses EVALSHA to run the script. If script does not exist it is retried using EVAL.
func (s *Script) Execute(ctx context.Context, c ScriptedClient, keys []string, args ...any) (ret any, err error) {
	defer func() {
		if errors.Is(err, redis.Nil) {
			err = nil
		}
	}()

	ret, err = s.EvalSha(ctx, c, keys, args...)
	if err != nil && strings.HasPrefix(err.Error(), "NOSCRIPT ") {
		return s.Eval(ctx, c, keys, args...)
	}
	return ret, err
}
