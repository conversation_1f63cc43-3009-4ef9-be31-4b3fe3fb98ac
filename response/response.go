package response

import (
	"fmt"
	"io"
	"mime"
	"net/http"
	"path/filepath"
	"strconv"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"
)

const (
	// the algorithm uses at most sniffLen bytes to make its decision
	sniffLen = 512

	constHTTPHeaderKeyOfContentDisposition = "Content-Disposition"
	constHTTPHeaderKeyOfContentType        = "Content-Type"
	constHTTPHeaderKeyOfContentLength      = "Content-Length"

	defaultSuccessMessage       = "OK"
	defaultInternalErrorMessage = "Internal errors, please contact the platform administrator to check the system" // 系统内部错误，请联系平台管理员检查系统
)

type CustomMessage interface {
	SuccessMessage() string
	WarningMessage() string
}

type CommonResponse struct {
	Code    uint32 `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data"`
}

func Success(data any) CommonResponse {
	message := defaultSuccessMessage
	if v, ok := data.(CustomMessage); ok {
		if s := v.SuccessMessage(); s != "" {
			message = s
		} else if s = v.WarningMessage(); s != "" {
			message = s
		}
	}

	return CommonResponse{
		Code:    uint32(errorx.OK),
		Message: message,
		Data:    data,
	}
}

func Failure(code errorx.Code, msg string) CommonResponse {
	return CommonResponse{
		Code:    uint32(code),
		Message: msg,
	}
}

func Error(err error) CommonResponse {
	e := errorx.Convert(err)
	return CommonResponse{
		Code:    uint32(e.Code()),
		Message: e.Message(),
	}
}

func InternalError() CommonResponse {
	return CommonResponse{
		Code:    uint32(errorx.InternalError),
		Message: defaultInternalErrorMessage,
	}
}

func PermissionInsufficient() CommonResponse {
	return CommonResponse{
		Code:    uint32(errorx.PermissionInsufficient),
		Message: "The current user has insufficient permissions, please apply to the platform administrator or the project administrator for corresponding permissions", // 当前用户权限不足，请向平台管理员或者项目管理员申请相应的权限
	}
}

// MakeParseParamErrorResponse make a response of parse parameters error.
// Deprecated: use MakeHttpResponse instead.
func MakeParseParamErrorResponse(r *http.Request, w http.ResponseWriter, err error) {
	logx.WithContext(r.Context()).Errorf("[API-ERR]: %+v", err)
	httpx.WriteJson(
		w, http.StatusBadRequest,
		Failure(errorx.ParseParamError, fmt.Sprintf("failed to parse parameters, error: %+v", err)),
	)
}

func MakeHttpResponse(r *http.Request, w http.ResponseWriter, resp any, err error) {
	var metricCode uint32

	if err == nil {
		cr := Success(resp)
		httpx.WriteJson(w, http.StatusOK, cr)

		metricCode = uint32(http.StatusOK)
	} else {
		logx.WithContext(r.Context()).Errorf("[API-ERR]: %+v", err)

		e := errorx.Convert(errors.Cause(err))
		errCode := e.Code()
		metricCode = uint32(errCode)

		statusCode := http.StatusBadRequest
		if errorx.IsSystemError(errCode) {
			statusCode = http.StatusInternalServerError
		}

		httpx.WriteJson(w, statusCode, Failure(errCode, e.Message()))
	}

	if metricObj := metrics.MetricObjectFromContext(r.Context()); metricObj != nil {
		metricObj.Code = metricCode
	}
}

func MakeHttpResponse2EventStream(
	r *http.Request, w http.ResponseWriter, stream *sse.Stream, err error,
) {
	logger := logx.WithContext(r.Context())

	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")

	var metricCode uint32
	if err == nil {
		threading.GoSafe(
			func() {
				<-r.Context().Done()
				logger.Warnf(
					"[API-WARN] event stream is disconnected, stream_id: %s, remote_addr: %s, error: %+v",
					stream.ID, r.RemoteAddr, r.Context().Err(),
				)
			},
		)

		httpx.Stream(
			r.Context(), w, func(w io.Writer) bool {
				if output, ok := <-stream.Event; ok {
					_, _ = w.Write([]byte(fmt.Sprintf("id: %s\n", output.ID)))
					_, e := w.Write([]byte(fmt.Sprintf("data: %s\n\n", output.Data)))
					return e == nil
				}

				return false
			},
		)
		metricCode = uint32(http.StatusOK)
	} else {
		logger.Errorf("[API-ERR]: %+v", err)

		e := errorx.Convert(errors.Cause(err))
		errCode := e.Code()
		metricCode = uint32(errCode)

		statusCode := http.StatusBadRequest
		if errorx.IsSystemError(errCode) {
			statusCode = http.StatusInternalServerError
		}
		httpx.WriteJson(w, statusCode, Failure(errCode, e.Message()))
	}

	if metricObj := metrics.MetricObjectFromContext(r.Context()); metricObj != nil {
		metricObj.Code = metricCode
	}
}

func MakeHTTPDownloadResponse(
	r *http.Request, w http.ResponseWriter, filename string, size int64, content io.ReadSeeker, err error,
) {
	defer func() {
		if err != nil {
			MakeHttpResponse(r, w, nil, err)
		} else {
			w.Header().Set(constHTTPHeaderKeyOfContentDisposition, "attachment; filename="+filename)
			w.Header().Set(constHTTPHeaderKeyOfContentLength, strconv.FormatInt(size, 10))
		}
	}()

	if err == nil {
		// If Content-Type isn't set, use the file's extension to find it, but
		// if the Content-Type is unset explicitly, do not sniff the type.
		_, haveType := w.Header()[constHTTPHeaderKeyOfContentType]
		if !haveType {
			contentType := mime.TypeByExtension(filepath.Ext(filename))
			if contentType == "" {
				// read a chunk to decide between utf-8 text and binary
				var buf [sniffLen]byte
				n, _ := io.ReadFull(content, buf[:])
				contentType = http.DetectContentType(buf[:n])

				// rewind to output whole file
				_, err = content.Seek(0, io.SeekStart)
				if err != nil {
					err = errorx.Errorf(
						errorx.FileOperationFailure, "seeker can't seek, filename: %s, error: %+v", filename, err,
					)
					return
				}
			}
			w.Header().Set(constHTTPHeaderKeyOfContentType, contentType)
		}

		if _, err = io.CopyN(w, content, size); err != nil {
			err = errorx.Errorf(
				errorx.FileOperationFailure, "failed to copy file, filename: %s, error: %+v", filename, err,
			)
			return
		}
	}
}
