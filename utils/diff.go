package utils

import "reflect"

// 用于比较两个列表的异同
type DiffValue interface {
	Key() string                        // 用于判断唯一性的Key
	Merge(oldValue DiffValue) DiffValue // 当key相同时,当两个值不同时,合并值
	IsSame(oldValue DiffValue) bool     // 当key相同时,两个值是否相同，相同则返回true
}

// Diff 比较异同
func Diff(s1, s2 []DiffValue) (s1Unique, merge, same, s2Unique []DiffValue) {
	s1Unique = make([]DiffValue, 0, len(s1))      // s1特有
	merge = make([]DiffValue, 0, len(s1)+len(s2)) // s1和s2都有，但值不同
	same = make([]DiffValue, 0, len(s1)+len(s2))  // s1和s2都有，且值相同
	s2Unique = make([]DiffValue, 0, len(s2))      // s2特有

	s1Map := make(map[string]DiffValue)
	for i, s := range s1 {
		s1Map[s.Key()] = s1[i]
	}

	// 比较s2和s1
	for _, s := range s2 {
		if s1v, exist := s1Map[s.Key()]; exist {
			// 如果旧值与新值不相同，则合并，如果相同，则保持原样(不返回)
			ok := s.IsSame(s1v)
			if !ok {
				merge = append(merge, s.Merge(s1v))
			} else {
				same = append(same, s)
			}
			delete(s1Map, s.Key()) // 删除s1的这个key，剩余下来的就是s1特有
		} else {
			s2Unique = append(s2Unique, s)
		}
	}

	// 比较s1中剩余的数据
	for _, s := range s1 {
		if _, exist := s1Map[s.Key()]; exist {
			s1Unique = append(s1Unique, s)
		}
	}

	return s1Unique, merge, same, s2Unique
}

func ToDiffValueList(s any) []DiffValue {
	rv := reflect.ValueOf(s)
	if rv.Kind() != reflect.Slice && rv.Kind() != reflect.Array {
		return nil
	}

	dvs := make([]DiffValue, 0, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		v := rv.Index(i).Interface()
		if dv, ok := v.(DiffValue); ok {
			dvs = append(dvs, dv)
		}
	}
	return dvs
}
