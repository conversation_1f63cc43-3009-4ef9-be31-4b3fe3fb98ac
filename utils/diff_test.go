package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

type TestDiffStruct struct {
	key   string
	value int
}

func (d *TestDiffStruct) Key() string {
	return d.key
}

func (d *TestDiffStruct) Merge(oldValue DiffValue) DiffValue {
	return &TestDiffStruct{
		key:   d.key,
		value: d.value,
	}
}

func (d *TestDiffStruct) IsSame(oldValue DiffValue) bool {
	old := oldValue.(*TestDiffStruct)
	if d.value == old.value {
		return true
	}

	return false
}

func Test_Diff(t *testing.T) {
	s1 := []*TestDiffStruct{
		{key: "a", value: 1},
		{key: "b", value: 2},
		{key: "c", value: 3},
	}

	s2 := []*TestDiffStruct{
		{key: "b", value: 2},
		{key: "c", value: 30},
		{key: "d", value: 4},
	}

	s1u, merge, same, s2u := Diff(ToDiffValueList(s1), ToDiffValueList(s2))
	assert.Equal(t, len(s1u), 1, "s1u错误")
	assert.Equal(t, s1u[0].(*TestDiffStruct).value, 1, "s1u.value错误")

	assert.Equal(t, len(s2u), 1, "s2u错误")
	assert.Equal(t, s2u[0].(*TestDiffStruct).value, 4, "s2u.value错误")

	assert.Equal(t, len(merge), 1, "merge错误")
	assert.Equal(t, merge[0].(*TestDiffStruct).value, 30, "merge.value错误")

	assert.Equal(t, len(same), 1, "same错误")
	assert.Equal(t, same[0].(*TestDiffStruct).value, 2, "same.value错误")
}
