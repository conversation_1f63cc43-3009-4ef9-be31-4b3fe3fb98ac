package utils

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
)

func DownloadFromUrl(ctx context.Context, url, targetPath string) error {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return fmt.Errorf("failed to new a http request, method: %s, url: %s, error: %+v", http.MethodGet, url, err)
	}

	resp, err := http.DefaultClient.Do(req)
	defer func() {
		if resp != nil {
			_, _ = io.Copy(io.Discard, resp.Body)
			_ = resp.Body.Close()
		}
	}()
	if err != nil {
		return fmt.Errorf("failed to call a http request, url: %s, error: %+v", url, err)
	} else if resp.StatusCode != http.StatusOK {
		return fmt.Errorf(
			"failed to call a http request, url: %s, status code: %s[%d]", url, resp.Status, resp.StatusCode,
		)
	}

	// 先检查父目录是否存在
	parentPath := filepath.Dir(targetPath)
	if _, err = os.Stat(parentPath); os.IsNotExist(err) {
		// 如果父目录不存在，则创建它
		err = os.MkdirAll(parentPath, 0o755) // 使用0755权限创建目录
		if err != nil {
			return fmt.Errorf("failed to create parent directory, path: %s, error: %+v", parentPath, err)
		}
	}

	// 创建文件
	file, err := os.Create(targetPath)
	if err != nil {
		return fmt.Errorf("failed to create target file, file: %s, error: %+v", targetPath, err)
	}
	defer func() {
		_ = file.Close()
	}()

	// 将 HTTP 响应体内容写入文件
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("failed to copy response data to file, file: %s, error: %+v", targetPath, err)
	}

	return nil
}
