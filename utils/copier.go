package utils

import (
	"database/sql"
	"reflect"
	"strconv"
	"time"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/syncx"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

type (
	TypeConverter = copier.TypeConverter
	Option        = copier.Option
	// FieldNameMapping = copier.FieldNameMapping

	TypeConverterFunc func(any) (any, error)
	optionFunc        func(*Option)
)

const (
	convertersPoolLimit = 100

	String         = copier.String
	Bool           = copier.Bool
	Int            = copier.Int
	Int8    int8   = 0
	Int16   int16  = 0
	Int32   int32  = 0
	Int64   int64  = 0
	Uint    uint   = 0
	Uint8   uint8  = 0
	Uint16  uint16 = 0
	Uint32  uint32 = 0
	Uint64  uint64 = 0
	Float32        = copier.Float32
	Float64        = copier.Float64
)

var (
	typeConverters []TypeConverter

	convertersPool = syncx.NewPool(convertersPoolLimit, create, destroy)

	Time           = time.Time{}
	SQLNullString  = sql.NullString{}
	SQLNullInt64   = sql.NullInt64{}
	SQLNullInt32   = sql.NullInt32{}
	SQLNullInt16   = sql.NullInt16{}
	SQLNullFloat64 = sql.NullFloat64{}
	SQLNullBool    = sql.NullBool{}
	SQLNullByte    = sql.NullByte{}
	SQLNullTime    = sql.NullTime{}
	Timestamp      = (*timestamppb.Timestamp)(nil)
	Struct         = (*structpb.Struct)(nil)
	MapStrAny      = make(map[string]any)
)

func init() {
	typeConverters = append(typeConverters, NumberToBool()...)
	typeConverters = append(
		typeConverters, TimeToString(), NullTimeToString(), StringToInt(), TimeToMilliseconds(),
		NullTimeToMilliseconds(), TimestampToMilliseconds(),
	)
	typeConverters = append(typeConverters, MapToStruct(), StructToMap())
	typeConverters = append(typeConverters, NumberToString()...)
}

func create() any {
	return createConverters()
}

func destroy(v any) {
	if cs, ok := v.([]TypeConverter); ok {
		destroyConverters(cs)
	}
}

func createConverters() []TypeConverter {
	converters := make([]TypeConverter, len(typeConverters))

	copy(converters, typeConverters)

	return converters
}

func destroyConverters(v []TypeConverter) {
	v = v[:0] //nolint: staticcheck
	v = nil
}

func WithTypeConverters(converters ...TypeConverter) optionFunc {
	return func(option *Option) {
		option.Converters = append(option.Converters, converters...)
	}
}

//func WithFieldNameMappings(mappings ...FieldNameMapping) optionFunc {
//	return func(option *Option) {
//		option.FieldNameMapping = append(option.FieldNameMapping, mappings...)
//	}
//}

func Copy(to, from any, converters ...TypeConverter) error {
	cs := GetConverters()
	defer PutConverters(cs)

	if len(converters) > 0 {
		cs = append(cs, converters...)
	}

	return copier.CopyWithOption(
		to, from, Option{
			IgnoreEmpty: false,
			// CaseSensitive: true,
			DeepCopy:   true,
			Converters: cs,
		},
	)
}

func CopyWithConverters(to, from any, converters []TypeConverter) error {
	return copier.CopyWithOption(
		to, from, Option{
			IgnoreEmpty: false,
			// CaseSensitive: true,
			DeepCopy:   true,
			Converters: converters,
		},
	)
}

func CopyWithOptions(to, from any, options ...optionFunc) error {
	cs := GetConverters()
	defer PutConverters(cs)

	opt := &Option{
		IgnoreEmpty: false,
		// CaseSensitive:    true,
		DeepCopy:   true,
		Converters: cs,
		// FieldNameMapping: nil,
	}

	for _, option := range options {
		option(opt)
	}

	return copier.CopyWithOption(to, from, *opt)
}

func GetConverters() []TypeConverter {
	v, ok := convertersPool.Get().([]TypeConverter)
	if !ok {
		return createConverters()
	}

	return v
}

func PutConverters(cs []TypeConverter) {
	if len(cs) != len(typeConverters) {
		cs = createConverters()
	}
	convertersPool.Put(cs)
}

func IntToBool() TypeConverter {
	return TypeConverter{
		SrcType: Int,
		DstType: Bool,
		Fn: func(src any) (any, error) {
			s, ok := src.(int)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Int)
			} else if s == 0 {
				return false, nil
			}
			return true, nil
		},
	}
}

func Int8ToBool() TypeConverter {
	return TypeConverter{
		SrcType: Int8,
		DstType: Bool,
		Fn: func(src any) (any, error) {
			s, ok := src.(int8)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Int8)
			} else if s == 0 {
				return false, nil
			}
			return true, nil
		},
	}
}

func Int16ToBool() TypeConverter {
	return TypeConverter{
		SrcType: Int16,
		DstType: Bool,
		Fn: func(src any) (any, error) {
			s, ok := src.(int16)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Int16)
			} else if s == 0 {
				return false, nil
			}
			return true, nil
		},
	}
}

func Int32ToBool() TypeConverter {
	return TypeConverter{
		SrcType: Int32,
		DstType: Bool,
		Fn: func(src any) (any, error) {
			s, ok := src.(int32)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Int32)
			} else if s == 0 {
				return false, nil
			}
			return true, nil
		},
	}
}

func Int64ToBool() TypeConverter {
	return TypeConverter{
		SrcType: Int64,
		DstType: Bool,
		Fn: func(src any) (any, error) {
			s, ok := src.(int64)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Int64)
			} else if s == 0 {
				return false, nil
			}
			return true, nil
		},
	}
}

func UintToBool() TypeConverter {
	return TypeConverter{
		SrcType: Uint,
		DstType: Bool,
		Fn: func(src any) (any, error) {
			s, ok := src.(uint)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Uint)
			} else if s == 0 {
				return false, nil
			}
			return true, nil
		},
	}
}

func Uint8ToBool() TypeConverter {
	return TypeConverter{
		SrcType: Uint8,
		DstType: Bool,
		Fn: func(src any) (any, error) {
			s, ok := src.(uint8)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Uint8)
			} else if s == 0 {
				return false, nil
			}
			return true, nil
		},
	}
}

func Uint16ToBool() TypeConverter {
	return TypeConverter{
		SrcType: Uint16,
		DstType: Bool,
		Fn: func(src any) (any, error) {
			s, ok := src.(uint16)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Uint16)
			} else if s == 0 {
				return false, nil
			}
			return true, nil
		},
	}
}

func Uint32ToBool() TypeConverter {
	return TypeConverter{
		SrcType: Uint32,
		DstType: Bool,
		Fn: func(src any) (any, error) {
			s, ok := src.(uint32)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Uint32)
			} else if s == 0 {
				return false, nil
			}
			return true, nil
		},
	}
}

func Uint64ToBool() TypeConverter {
	return TypeConverter{
		SrcType: Uint64,
		DstType: Bool,
		Fn: func(src any) (any, error) {
			s, ok := src.(uint64)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Uint64)
			} else if s == 0 {
				return false, nil
			}
			return true, nil
		},
	}
}

func Float32ToBool() TypeConverter {
	return TypeConverter{
		SrcType: Float32,
		DstType: Bool,
		Fn: func(src any) (any, error) {
			s, ok := src.(float32)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Float32)
			} else if s == 0 {
				return false, nil
			}
			return true, nil
		},
	}
}

func Float64ToBool() TypeConverter {
	return TypeConverter{
		SrcType: Float64,
		DstType: Bool,
		Fn: func(src any) (any, error) {
			s, ok := src.(float64)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Float64)
			} else if s == 0 {
				return false, nil
			}
			return true, nil
		},
	}
}

func NumberToBool() []TypeConverter {
	return []TypeConverter{
		IntToBool(),
		Int8ToBool(),
		Int16ToBool(),
		Int32ToBool(),
		Int64ToBool(),
		UintToBool(),
		Uint8ToBool(),
		Uint16ToBool(),
		Uint32ToBool(),
		Uint64ToBool(),
		Float32ToBool(),
		Float64ToBool(),
	}
}

func TimeToString() TypeConverter {
	return TypeConverter{
		SrcType: Time,
		DstType: String,
		Fn: func(src any) (any, error) {
			s, ok := src.(time.Time)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Time)
			}

			return s.Format(time.RFC3339), nil
		},
	}
}

func NullTimeToString() TypeConverter {
	return TypeConverter{
		SrcType: SQLNullTime,
		DstType: String,
		Fn: func(src any) (any, error) {
			s, ok := src.(sql.NullTime)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, SQLNullTime)
			}

			if !s.Valid {
				return "", nil
			}

			return s.Time.Format(time.RFC3339), nil
		},
	}
}

func StringToInt() TypeConverter {
	return TypeConverter{
		SrcType: String,
		DstType: Int,
		Fn: func(src any) (any, error) {
			s, ok := src.(string)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, String)
			}

			return strconv.Atoi(s)
		},
	}
}

func NumberToString() []TypeConverter {
	fn := func(src any) (any, error) {
		rv := reflect.ValueOf(src)
		for rv.Kind() == reflect.Pointer {
			rv = rv.Elem()
		}

		switch rv.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return strconv.FormatInt(rv.Int(), 10), nil
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			return strconv.FormatUint(rv.Uint(), 10), nil
		case reflect.Float32:
			return strconv.FormatFloat(rv.Float(), 'f', -1, 32), nil
		case reflect.Float64:
			return strconv.FormatFloat(rv.Float(), 'f', -1, 64), nil
		default:
			return nil, errors.Errorf("source type[%T] is not matching with [number]", src)
		}
	}
	return []TypeConverter{
		{
			SrcType: Int,
			DstType: String,
			Fn:      fn,
		},
		{
			SrcType: Int8,
			DstType: String,
			Fn:      fn,
		},
		{
			SrcType: Int16,
			DstType: String,
			Fn:      fn,
		},
		{
			SrcType: Int32,
			DstType: String,
			Fn:      fn,
		},
		{
			SrcType: Int64,
			DstType: String,
			Fn:      fn,
		},
		{
			SrcType: Uint,
			DstType: String,
			Fn:      fn,
		},
		{
			SrcType: Uint8,
			DstType: String,
			Fn:      fn,
		},
		{
			SrcType: Uint16,
			DstType: String,
			Fn:      fn,
		},
		{
			SrcType: Uint32,
			DstType: String,
			Fn:      fn,
		},
		{
			SrcType: Uint64,
			DstType: String,
			Fn:      fn,
		},
		{
			SrcType: Float32,
			DstType: String,
			Fn:      fn,
		},
		{
			SrcType: Float64,
			DstType: String,
			Fn:      fn,
		},
	}
}

func TimeToMilliseconds() TypeConverter {
	return TypeConverter{
		SrcType: Time,
		DstType: Int64,
		Fn: func(src any) (any, error) {
			s, ok := src.(time.Time)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Time)
			}

			return s.UnixMilli(), nil
		},
	}
}

func NullTimeToMilliseconds() TypeConverter {
	return TypeConverter{
		SrcType: SQLNullTime,
		DstType: Int64,
		Fn: func(src any) (any, error) {
			s, ok := src.(sql.NullTime)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, SQLNullTime)
			}

			if !s.Valid {
				return int64(0), nil
			}

			return s.Time.UnixMilli(), nil
		},
	}
}

func TimestampToMilliseconds() TypeConverter {
	return TypeConverter{
		SrcType: Timestamp,
		DstType: Int64,
		Fn: func(src any) (dst any, err error) {
			s, ok := src.(*timestamppb.Timestamp)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Timestamp)
			}

			return s.AsTime().UnixMilli(), nil
		},
	}
}

func MapToStruct() TypeConverter {
	return TypeConverter{
		SrcType: MapStrAny,
		DstType: Struct,
		Fn: func(src any) (dst any, err error) {
			s, ok := src.(map[string]any)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, MapStrAny)
			}

			return protobuf.NewStruct(s)
		},
	}
}

func StructToMap() TypeConverter {
	return TypeConverter{
		SrcType: Struct,
		DstType: MapStrAny,
		Fn: func(src any) (dst any, err error) {
			s, ok := src.(*structpb.Struct)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, Struct)
			}

			return s.AsMap(), nil
		},
	}
}

func StringToPBEnum(enum protoreflect.Enum) TypeConverter {
	return TypeConverter{
		SrcType: String,
		DstType: enum,
		Fn: func(src any) (any, error) {
			s, ok := src.(string)
			if !ok {
				return 0, errors.Errorf("source type[%T] is not matching with [%T]", src, String)
			}

			return StringConvertToPBEnum(s, enum)
		},
	}
}

func PBEnumToString(enum protoreflect.Enum) TypeConverter {
	return TypeConverter{
		SrcType: enum,
		DstType: String,
		Fn: func(src any) (any, error) {
			e, ok := src.(protoreflect.Enum)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, enum)
			}

			return PBEnumConvertToString(e)
		},
	}
}

func numberToPBEnumFunc(enum protoreflect.Enum) TypeConverterFunc {
	return func(src any) (any, error) {
		i, err := cast.ToInt32E(src)
		if err != nil {
			return enum.Type().New(0), errors.Errorf("source type[%T] is not matching with [%T]", src, Int32)
		}

		return NumberConvertToPBEnum(i, enum)
	}
}

func IntToPBEnum(enum protoreflect.Enum) TypeConverter {
	return TypeConverter{
		SrcType: Int,
		DstType: enum,
		Fn:      numberToPBEnumFunc(enum),
	}
}

func Int16ToPBEnum(enum protoreflect.Enum) TypeConverter {
	return TypeConverter{
		SrcType: Int16,
		DstType: enum,
		Fn:      numberToPBEnumFunc(enum),
	}
}

func Int32ToPBEnum(enum protoreflect.Enum) TypeConverter {
	return TypeConverter{
		SrcType: Int32,
		DstType: enum,
		Fn:      numberToPBEnumFunc(enum),
	}
}

func Int64ToPBEnum(enum protoreflect.Enum) TypeConverter {
	return TypeConverter{
		SrcType: Int64,
		DstType: enum,
		Fn:      numberToPBEnumFunc(enum),
	}
}

func UintToPBEnum(enum protoreflect.Enum) TypeConverter {
	return TypeConverter{
		SrcType: Uint,
		DstType: enum,
		Fn:      numberToPBEnumFunc(enum),
	}
}

func Uint16ToPBEnum(enum protoreflect.Enum) TypeConverter {
	return TypeConverter{
		SrcType: Uint16,
		DstType: enum,
		Fn:      numberToPBEnumFunc(enum),
	}
}

func Uint32ToPBEnum(enum protoreflect.Enum) TypeConverter {
	return TypeConverter{
		SrcType: Uint32,
		DstType: enum,
		Fn:      numberToPBEnumFunc(enum),
	}
}

func Uint64ToPBEnum(enum protoreflect.Enum) TypeConverter {
	return TypeConverter{
		SrcType: Uint64,
		DstType: enum,
		Fn:      numberToPBEnumFunc(enum),
	}
}

func NumberToPBEnum(enum protoreflect.Enum) []TypeConverter {
	return []TypeConverter{
		IntToPBEnum(enum),
		Int16ToPBEnum(enum),
		Int32ToPBEnum(enum),
		Int64ToPBEnum(enum),
		UintToPBEnum(enum),
		Uint16ToPBEnum(enum),
		Uint32ToPBEnum(enum),
		Uint64ToPBEnum(enum),
	}
}

func PBEnumToNumber(enum protoreflect.Enum) []TypeConverter {
	fn := func(dst any) TypeConverterFunc {
		return func(src any) (any, error) {
			e, ok := src.(protoreflect.Enum)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, enum)
			}

			switch dst.(type) {
			case int:
				return PBEnumConvertToNumber[int](e)
			case int8:
				return PBEnumConvertToNumber[int8](e)
			case int16:
				return PBEnumConvertToNumber[int16](e)
			case int32:
				return PBEnumConvertToNumber[int32](e)
			case int64:
				return PBEnumConvertToNumber[int64](e)
			case uint:
				return PBEnumConvertToNumber[uint](e)
			case uint8:
				return PBEnumConvertToNumber[uint8](e)
			case uint16:
				return PBEnumConvertToNumber[uint16](e)
			case uint32:
				return PBEnumConvertToNumber[uint32](e)
			case uint64:
				return PBEnumConvertToNumber[uint64](e)
			default:
				return 0, errors.Errorf("target type[%T] is not a number", dst)
			}
		}
	}
	return []TypeConverter{
		{
			SrcType: enum,
			DstType: Int,
			Fn:      fn(Int),
		},
		{
			SrcType: enum,
			DstType: Int8,
			Fn:      fn(Int8),
		},
		{
			SrcType: enum,
			DstType: Int16,
			Fn:      fn(Int16),
		},
		{
			SrcType: enum,
			DstType: Int32,
			Fn:      fn(Int32),
		},
		{
			SrcType: enum,
			DstType: Int64,
			Fn:      fn(Int64),
		},
		{
			SrcType: enum,
			DstType: Uint,
			Fn:      fn(Uint),
		},
		{
			SrcType: enum,
			DstType: Uint8,
			Fn:      fn(Uint8),
		},
		{
			SrcType: enum,
			DstType: Uint16,
			Fn:      fn(Uint16),
		},
		{
			SrcType: enum,
			DstType: Uint32,
			Fn:      fn(Uint32),
		},
		{
			SrcType: enum,
			DstType: Uint64,
			Fn:      fn(Uint64),
		},
	}
}

func StringToAny(source, target any) error {
	s, ok := source.(string)

	if !ok {
		return errors.Errorf("source type[%T] is not a string", source)
	}

	rt := reflect.TypeOf(target)
	if rt.Kind() == reflect.Pointer {
		rt = rt.Elem()
	}
	if rt.Kind() == reflect.Slice {
		if _, ok = reflect.New(rt.Elem()).Elem().Interface().(proto.Message); ok {
			return protobuf.UnmarshalJSONWithMessagesFromString(s, target)
		}
	} else if m, ok := target.(proto.Message); ok {
		return protobuf.UnmarshalJSONFromString(s, m)
	}

	return jsonx.UnmarshalFromString(s, target)
}

func StringToPBMessage(source any, target proto.Message) error {
	s, ok := source.(string)

	if !ok {
		return errors.Errorf("source type[%T] is not a string", source)
	}

	return protobuf.UnmarshalJSONFromString(s, target)
}

func StringToSlicePBMessage(source any, create func() proto.Message) ([]proto.Message, error) {
	var s []any
	if err := StringToAny(source, &s); err != nil {
		return nil, err
	}

	var o []proto.Message
	for _, v := range s {
		m := create()
		if err := StringToPBMessage(jsonx.MarshalToStringIgnoreError(v), m); err != nil {
			return o, err
		}

		o = append(o, m)
	}

	return o, nil
}

func StringToPBEnumType(source any, pbEnumValMap map[string]int32, create func(int32) any) (any, error) {
	s, ok := source.(string)
	if !ok {
		return nil, errors.Errorf("source type[%T] is not a string", source)
	}

	i, ok := pbEnumValMap[s]
	if !ok {
		return nil, errors.Errorf("source value[%s] is an invalid enumeration value", s)
	}

	return create(i), nil
}
