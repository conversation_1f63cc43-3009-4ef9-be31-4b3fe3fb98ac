package utils

import (
	"testing"
)

func TestStringToAny(t *testing.T) {
	type foo struct {
		Name string `json:"name"`
		Age  int    `json:"age"`
	}

	type args struct {
		source any
		target any
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "to struct",
			args: args{
				source: `{"name": "allen", "age": 18}`,
				target: &foo{},
			},
		},
		{
			name: "to slice of struct",
			args: args{
				source: `[{"name": "bob", "age": 19}, {"name": "charles", "age": 20}]`,
				target: &[]*foo{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := StringToAny(tt.args.source, tt.args.target)
			if err != nil {
				t.<PERSON><PERSON><PERSON>("failed to call StringToAny(%v, %v), err: %+v", tt.args.source, tt.args.target, err)
			} else {
				t.Logf("StringToAny(%v, %v)", tt.args.source, tt.args.target)
			}
		})
	}
}
