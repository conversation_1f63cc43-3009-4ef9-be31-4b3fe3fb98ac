package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMax_int64(t *testing.T) {
	type args[T Ordered] struct {
		x T
		y []T
	}
	type testCase[T Ordered] struct {
		name string
		args args[T]
		want T
	}
	tests := []testCase[int64]{
		{
			name: "int64",
			args: args[int64]{
				x: 5,
				y: []int64{1, 10, 5, 3, 5, 6},
			},
			want: 10,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				assert.Equalf(t, tt.want, Max(tt.args.x, tt.args.y...), "Max(%v, %v)", tt.args.x, tt.args.y)
			},
		)
	}
}

func TestMin_int64(t *testing.T) {
	type args[T Ordered] struct {
		x T
		y []T
	}
	type testCase[T Ordered] struct {
		name string
		args args[T]
		want T
	}
	tests := []testCase[int64]{
		{
			name: "int64",
			args: args[int64]{
				x: 5,
				y: []int64{1, 10, 5, 3, 5, 6},
			},
			want: 1,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				assert.Equalf(t, tt.want, Min(tt.args.x, tt.args.y...), "Min(%v, %v)", tt.args.x, tt.args.y)
			},
		)
	}
}
