package utils

import (
	"context"
	"testing"
)

func TestCloneWithContext(t *testing.T) {
	type args struct {
		ctx        context.Context
		gitURL     string
		targetPath string
		branch     string
		options    []GitCloneOption
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "ios_autotest - main branch",
			args: args{
				ctx:        context.Background(),
				gitURL:     "https://autotest:<EMAIL>/T4197/ios_autotest.git",
				targetPath: "../../clone_test_ios_autotest",
				branch:     "main",
			},
		},
		{
			name: "ios_autotest - develop branch",
			args: args{
				ctx:        context.Background(),
				gitURL:     "https://autotest:<EMAIL>/T4197/ios_autotest.git",
				targetPath: "../../clone_test_ios_autotest",
				branch:     "develop",
			},
		},
		{
			name: "probe-backend - default branch",
			args: args{
				ctx:        context.Background(),
				gitURL:     "https://<EMAIL>:<EMAIL>/TestDevelopment/probe-backend.git",
				targetPath: "../../clone_test_probe-backend",
				branch:     "",
			},
		},
		{
			name: "tt-protocols-app - develop branch",
			args: args{
				ctx:        context.Background(),
				gitURL:     "https://probe:<EMAIL>/tt-protocols/app.git",
				targetPath: "../../clone_test_tt-protocols-app",
				branch:     "develop",
			},
		},
		{
			name: "tt-protocols-app - develop branch - git command",
			args: args{
				ctx:        context.Background(),
				gitURL:     "https://probe:<EMAIL>/tt-protocols/app.git",
				targetPath: "../../clone_test_tt-protocols-app" + "-git",
				branch:     "develop",
				options: []GitCloneOption{
					WithCloneUseCommand(),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				commit, err := CloneWithContext(
					tt.args.ctx, tt.args.gitURL, tt.args.targetPath, tt.args.branch, tt.args.options...,
				)
				if err != nil {
					t.Fatal(err)
				}

				t.Logf("commit: %s", commit.String())
			},
		)
	}
}

func TestPullWithContext(t *testing.T) {
	type args struct {
		ctx        context.Context
		targetPath string
		options    []GitPullOption
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "tt-protocols-app - develop branch",
			args: args{
				ctx:        context.Background(),
				targetPath: "../../clone_test_tt-protocols-app",
			},
		},
		{
			name: "tt-protocols-app - develop branch - git command",
			args: args{
				ctx:        context.Background(),
				targetPath: "../../clone_test_tt-protocols-app" + "-git",
				options: []GitPullOption{
					WithPullUseCommand(),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				commit, err := PullWithContext(tt.args.ctx, tt.args.targetPath, tt.args.options...)
				if err != nil {
					t.Fatal(err)
				}

				t.Logf("commit: %s", commit.String())
			},
		)
	}
}
