package utils

import (
	"reflect"
	"testing"

	"github.com/davecgh/go-spew/spew"
)

func TestNew(t *testing.T) {
	type address struct {
		Country string
		City    string
	}
	type person struct {
		Name     string
		Age      int64
		Father   *person
		Mother   *person
		Children []*person
		Address  address
	}
	type args struct {
		typ reflect.Type
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "struct",
			args: args{
				typ: reflect.TypeOf(person{}),
			},
		},
		{
			name: "struct pointer",
			args: args{
				typ: reflect.TypeOf((*person)(nil)),
			},
		},
		{
			name: "string",
			args: args{
				typ: reflect.TypeOf(""),
			},
		},
		{
			name: "map",
			args: args{
				typ: reflect.TypeOf(map[string]string{}),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			val := New(tt.args.typ)
			t.Logf("%+v", spew.Sprint(val.Interface()))
		})
	}
}
