package utils

import (
	"fmt"
	"sync"

	"github.com/zeromicro/go-zero/core/utils"
)

const (
	defaultMaxTimes = 5
)

func GenUUID(prefix string) string {
	return fmt.Sprintf("%s%s", prefix, utils.NewUuid())
}

func GenNanoId(prefix string, l ...int) string {
	return fmt.Sprintf("%s%s", prefix, utils.NewNanoId(l...))
}

type (
	GeneratorOption func(generator *UniqueIdGenerator)
	GenerateFunc    func() string
	IsUniqueFunc    func(id string) bool
)

type UniqueIdGenerator struct {
	maxTimes   int64
	generateFn GenerateFunc
	isUniqueFn IsUniqueFunc

	mutex *sync.Mutex
}

func NewUniqueIdGenerator(options ...GeneratorOption) *UniqueIdGenerator {
	g := defaultUniqueIdGenerator()

	for _, option := range options {
		option(g)
	}

	return g
}

func defaultUniqueIdGenerator() *UniqueIdGenerator {
	return &UniqueIdGenerator{
		maxTimes: defaultMaxTimes,
		generateFn: func() string {
			return GenNanoId("")
		},
		isUniqueFn: func(id string) bool {
			return true
		},
		mutex: &sync.Mutex{},
	}
}

func WithMaxTimes(times int64) GeneratorOption {
	return func(g *UniqueIdGenerator) {
		g.maxTimes = times
	}
}

func WithGenerateFunc(fn GenerateFunc) GeneratorOption {
	return func(g *UniqueIdGenerator) {
		g.generateFn = fn
	}
}

func WithIsUniqueFunc(fn IsUniqueFunc) GeneratorOption {
	return func(g *UniqueIdGenerator) {
		g.isUniqueFn = fn
	}
}

func (g *UniqueIdGenerator) Next() string {
	g.mutex.Lock()
	defer g.mutex.Unlock()

	var times int64
	for times < g.maxTimes {
		times++

		id := g.generateFn()
		if g.isUniqueFn(id) {
			return id
		}
	}

	return ""
}
