package utils

import (
	"reflect"
	"strings"

	"github.com/go-playground/locales/en"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	v10 "github.com/go-playground/validator/v10"
	enTranslations "github.com/go-playground/validator/v10/translations/en"
	zhTranslations "github.com/go-playground/validator/v10/translations/zh"
)

const (
	zhLocale = "zh"
)

var uTranslator *ut.UniversalTranslator

func init() {
	// 设置支持语言
	enTranslator := en.New()
	zhTranslator := zh.New()

	// 设置国际化翻译器
	uTranslator = ut.New(enTranslator, zhTranslator)
}

type Validator struct {
	*v10.Validate
	ut.Translator
}

func NewValidator(locale string) *Validator {
	// 获取翻译器
	translator, _ := uTranslator.GetTranslator(locale)

	// 设置验证器
	validator := v10.New()

	switch strings.ToLower(locale) {
	case zhLocale:
		_ = zhTranslations.RegisterDefaultTranslations(validator, translator)
		validator.RegisterTagNameFunc(func(field reflect.StructField) string {
			label := field.Tag.Get(zhLocale)
			if label == "" {
				return field.Name
			}
			return label
		})
	default:
		_ = enTranslations.RegisterDefaultTranslations(validator, translator)
	}

	return &Validator{
		Validate:   validator,
		Translator: translator,
	}
}

func (v *Validator) Translate(err error) string {
	errs, ok := err.(v10.ValidationErrors)
	if !ok {
		return err.Error()
	}

	s := make([]string, 0, len(errs))
	for _, e := range errs {
		s = append(s, e.Translate(v.Translator))
	}

	return strings.Join(s, ", ")
}
