//go:build darwin || linux
// +build darwin linux

package utils

import (
	"context"
	"os/exec"
	"syscall"
)

func CommandContext(ctx context.Context, name string, args ...string) *Cmd {
	cmd := exec.CommandContext(ctx, name, args...)
	cmd.SysProcAttr = &syscall.SysProcAttr{Setpgid: true}
	cmd.Cancel = func() error {
		if cmd.Process == nil {
			return nil
		}

		return syscall.Kill(-cmd.Process.Pid, syscall.SIGKILL)
	}

	return &Cmd{Cmd: cmd}
}
