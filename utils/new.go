package utils

import (
	"reflect"

	dll "github.com/emirpasic/gods/lists/doublylinkedlist"
)

func New(typ reflect.Type) (val reflect.Value) {
	return newByRecursive(typ, dll.New())
}

func newByRecursive(typ reflect.Type, stack *dll.List) (val reflect.Value) {
	isPtr := false

	if typ.Kind() == reflect.Ptr {
		isPtr = true
		typ = typ.Elem()
	}

	switch typ.Kind() {
	case reflect.Struct:
		val = reflect.New(typ)
		se := val.Elem()

		name := typ.Name()
		if !stack.Contains(name) {
			stack.Prepend(name)

			for i := 0; i < typ.NumField(); i++ {
				sf := typ.Field(i)
				if !sf.IsExported() {
					continue
				}

				se.Field(i).Set(newByRecursive(sf.Type, stack))
			}

			stack.Remove(0)
		}

		if !isPtr {
			val = se
		}
	case reflect.Slice:
		val = reflect.MakeSlice(typ, 0, 0)
	case reflect.Map:
		val = reflect.MakeMap(typ)
	default:
		val = reflect.New(typ)
		if !isPtr {
			val = val.Elem()
		}
	}

	return val
}
