package utils

import (
	"encoding/json"
	"strconv"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"golang.org/x/exp/constraints"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

func StringConvertToPBEnum(src string, enum protoreflect.Enum) (protoreflect.Enum, error) {
	values := enum.Descriptor().Values()
	ev := values.ByName(protoreflect.Name(src))
	if ev != nil {
		return enum.Type().New(ev.Number()), nil
	}

	for i := 0; i < values.Len(); i++ {
		ev = values.Get(i)
		opts := ev.Options()
		if !proto.HasExtension(opts, protobuf.E_EnumValueAlias) {
			continue
		}

		v := proto.GetExtension(opts, protobuf.E_EnumValueAlias)
		if sv, ok := v.(string); ok && src == sv {
			return enum.Type().New(ev.Number()), nil
		}
	}

	return enum.Type().New(0), errors.Errorf(
		"source value[%s] is not a valid enumeration value of %T", src, enum,
	)
}

func PBEnumConvertToString(enum protoreflect.Enum) (string, error) {
	n := enum.Number()
	ev := enum.Descriptor().Values().ByNumber(n)
	if ev != nil {
		opts := ev.Options()
		if proto.HasExtension(opts, protobuf.E_EnumValueAlias) {
			v := proto.GetExtension(opts, protobuf.E_EnumValueAlias)
			if sv, ok := v.(string); ok {
				return sv, nil
			}
		}

		return string(ev.Name()), nil
	}

	return strconv.Itoa(int(n)), nil
}

func NumberConvertToPBEnum[N constraints.Integer | constraints.Float | json.Number](
	src N, enum protoreflect.Enum,
) (protoreflect.Enum, error) {
	i, err := cast.ToInt32E(src)
	if err != nil {
		return enum.Type().New(0), errors.Errorf("source type[%T] is not matching with [%T]", src, Int32)
	}

	ev := enum.Descriptor().Values().ByNumber(protoreflect.EnumNumber(i))
	if ev != nil {
		return enum.Type().New(ev.Number()), nil
	}

	return enum.Type().New(0), errors.Errorf("source value[%d] is not a valid enumeration number of %T", i, enum)
}

func PBEnumConvertToNumber[N constraints.Integer | constraints.Float](enum protoreflect.Enum) (n N, err error) {
	num := enum.Number()
	ev := enum.Descriptor().Values().ByNumber(num)
	if ev != nil {
		num = ev.Number()
	}

	defer func() {
		if r := recover(); r != nil {
			n = N(0)
			err = errors.Errorf("source value[%d] is not a valid number of %T", num, n)
		}
	}()

	return N(num), err
}
