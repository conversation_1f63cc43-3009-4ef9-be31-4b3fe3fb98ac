package utils

import (
	"context"
	"testing"
)

func TestCloneOrUpdateWithContext(t *testing.T) {
	type args struct {
		ctx        context.Context
		gitURL     string
		targetPath string
		options    []CloneOption
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "ios_autotest",
			args: args{
				ctx:        context.Background(),
				gitURL:     "https://autotest:<EMAIL>/T4197/ios_autotest.git",
				targetPath: "../../clone_test_ios_autotest",
				options:    []CloneOption{WithBranch("main"), WithAlwaysClone()},
			},
		},
		{
			name: "probe-backend",
			args: args{
				ctx:        context.Background(),
				gitURL:     "https://<EMAIL>:<EMAIL>/TestDevelopment/probe-backend.git",
				targetPath: "../../clone_test_probe-backend",
				options:    []CloneOption{WithBranch("main")},
			},
		},
		{
			name: "tt-protocols-app",
			args: args{
				ctx:        context.Background(),
				gitURL:     "https://autotest:<EMAIL>/tt-protocols/app.git",
				targetPath: "../../clone_test_tt-protocols-app",
				options:    []CloneOption{WithBranch("develop")},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				err := CloneOrUpdateWithContext(tt.args.ctx, tt.args.gitURL, tt.args.targetPath, tt.args.options...)
				if err != nil {
					t.Fatal(err)
				}
			},
		)
	}
}
