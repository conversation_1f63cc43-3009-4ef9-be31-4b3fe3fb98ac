//go:build windows
// +build windows

package utils

import (
	"context"
	"os"
	"os/exec"
)

func CommandContext(ctx context.Context, name string, args ...string) *Cmd {
	cmd := exec.CommandContext(ctx, name, args...)
	cmd.Cancel = func() error {
		if cmd.Process == nil {
			return nil
		}

		p, err := os.FindProcess(cmd.Process.Pid)
		if err != nil {
			return err
		}

		return p.Kill()
	}

	return &Cmd{Cmd: cmd}
}
