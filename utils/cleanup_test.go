package utils

import (
	"testing"

	"github.com/davecgh/go-spew/spew"
	"github.com/stretchr/testify/assert"
)

func TestCleanUp(t *testing.T) {
	type inner struct {
		Name string
		Age  int64
	}
	type outer struct {
		Inner *inner
		City  string
	}

	type args struct {
		v any
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "clean up struct",
			args: args{
				v: outer{
					Inner: &inner{
						Name: "allen",
						Age:  18,
					},
					City: "guangzhou",
				},
			},
			wantErr: true,
		},
		{
			name: "clean up struct pointer",
			args: args{
				v: &outer{
					Inner: &inner{
						Name: "bob",
						Age:  19,
					},
					City: "shenzhen",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Log(spew.Sprintf("1: v: %+v", tt.args.v))

			err := CleanUp(tt.args.v)
			if tt.wantErr {
				assert.NotNil(t, err)
			} else {
				assert.Nil(t, err)
			}

			t.Log(spew.Sprintf("2: v: %+v", tt.args.v))
		})
	}
}
