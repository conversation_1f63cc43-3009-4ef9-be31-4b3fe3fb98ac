package utils

import (
	"context"
	"fmt"
	"os"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/config"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/transport"
	"github.com/pkg/errors"
)

type cloneOptions struct {
	branch      string
	alwaysClone bool
}

func (c *cloneOptions) getReferenceName() plumbing.ReferenceName {
	if c.branch != "" {
		return plumbing.NewBranchReferenceName(c.branch)
	}

	return plumbing.HEAD
}

func (c *cloneOptions) getName() string {
	if c.branch != "" {
		return c.branch
	}

	return plumbing.HEAD.String()
}

type CloneOption func(o *cloneOptions)

func WithBranch(branch string) CloneOption {
	return func(o *cloneOptions) {
		o.branch = branch
	}
}

func WithAlwaysClone() CloneOption {
	return func(o *cloneOptions) {
		o.alwaysClone = true
	}
}

// CloneOrUpdateWithContext clone or update a git repository with context
// Deprecated: use CloneWithContext instead.
func CloneOrUpdateWithContext(ctx context.Context, gitURL, targetPath string, options ...CloneOption) (err error) {
	co := new(cloneOptions)
	for _, option := range options {
		option(co)
	}

	if !Exists(targetPath) || co.alwaysClone {
		// 目录不存在 或者 `alwaysClone` == true
		if err = os.RemoveAll(targetPath); err != nil {
			return err
		}

		_, err = git.PlainCloneContext(
			ctx, targetPath, false, &git.CloneOptions{
				URL:           gitURL,
				ReferenceName: co.getReferenceName(),
				SingleBranch:  true,
				Depth:         1,
				Progress:      os.Stdout,
			},
		)

		return err
	} else {
		var (
			r *git.Repository
			w *git.Worktree
		)

		r, err = git.PlainOpen(targetPath)
		if err != nil {
			return err
		}

		w, err = r.Worktree()
		if err != nil {
			return err
		}

		if err = r.FetchContext(
			ctx, &git.FetchOptions{
				RefSpecs: []config.RefSpec{"refs/*:refs/*"},
				Progress: os.Stdout,
			},
		); err != nil && !errors.Is(err, git.NoErrAlreadyUpToDate) && !errors.Is(
			err, transport.ErrEmptyUploadPackRequest,
		) {
			return err
		}

		hash, err := r.ResolveRevision(plumbing.Revision(co.getName()))
		if err != nil {
			return err
		}

		referenceName := co.getReferenceName()
		mirrorRemoteRefSpec := fmt.Sprintf("%s:%s", referenceName, referenceName)
		fetchOptions := &git.FetchOptions{
			RefSpecs: []config.RefSpec{
				config.RefSpec(mirrorRemoteRefSpec),
			},
			Progress: os.Stdout,
		}
		checkoutOptions := &git.CheckoutOptions{
			Hash: *hash,
			// Branch: referenceName,
			Force: true,
		}

		if err = w.Checkout(checkoutOptions); err != nil {
			if err = r.FetchContext(
				ctx, fetchOptions,
			); err != nil && !errors.Is(err, git.NoErrAlreadyUpToDate) && !errors.Is(
				err, transport.ErrEmptyUploadPackRequest,
			) {
				return err
			}

			if err = w.Checkout(checkoutOptions); err != nil {
				return err
			}
		}
	}

	return nil
}

// CloneOrUpdate clone or update a git repository
// Deprecated: use Clone instead.
func CloneOrUpdate(gitURL, targetPath string, options ...CloneOption) error {
	return CloneOrUpdateWithContext(context.Background(), gitURL, targetPath, options...)
}
