package utils

import (
	"context"
	"errors"
	"io"
	"net/url"
	"os"
	"os/exec"
	"strconv"
	"strings"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/object"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

const (
	gitCommand      = "git"
	alreadyUpToDate = "Already up to date."
)

type (
	CloneOptions struct {
		*git.CloneOptions

		UseCommand bool
	}

	GitCloneOption func(o *CloneOptions)
)

func WithCloneURL(url_ string) GitCloneOption {
	return func(o *CloneOptions) {
		o.URL = url_
	}
}

func WithCloneReferenceName(name plumbing.ReferenceName) GitCloneOption {
	return func(o *CloneOptions) {
		o.ReferenceName = name
	}
}

func WithCloneSingleBranch() GitCloneOption {
	return func(o *CloneOptions) {
		o.SingleBranch = true
	}
}

func WithCloneDepth(depth int) GitCloneOption {
	return func(o *CloneOptions) {
		o.Depth = depth
	}
}

func WithCloneProgress(writer io.Writer) GitCloneOption {
	return func(o *CloneOptions) {
		o.Progress = writer
	}
}

func WithCloneUseCommand() GitCloneOption {
	return func(o *CloneOptions) {
		o.UseCommand = true
	}
}

func Clone(gitURL, targetPath, branch string, options ...GitCloneOption) (*object.Commit, error) {
	return CloneWithContext(context.Background(), gitURL, targetPath, branch, options...)
}

func CloneWithContext(
	ctx context.Context, gitURL, targetPath, branch string, options ...GitCloneOption,
) (*object.Commit, error) {
	// remove the target path
	err := os.RemoveAll(targetPath)
	if err != nil {
		return nil, err
	}

	var referenceName plumbing.ReferenceName
	if branch != "" {
		referenceName = plumbing.NewBranchReferenceName(branch)
	}

	co := &CloneOptions{
		CloneOptions: &git.CloneOptions{
			URL:           gitURL,
			ReferenceName: referenceName,
			SingleBranch:  true,
			Depth:         1,
			Progress:      os.Stdout,
		},
	}
	for _, option := range options {
		option(co)
	}

	var r *git.Repository
	if HasGitCommand() && co.UseCommand {
		err = cloneByCommand(ctx, gitURL, targetPath, branch, co)
	} else {
		r, err = git.PlainCloneContext(ctx, targetPath, false, co.CloneOptions)
		if err != nil {
			return nil, err
		}
	}
	if err != nil {
		return nil, err
	}

	if r == nil {
		r, err = git.PlainOpen(targetPath)
		if err != nil {
			return nil, err
		}
	}

	// get the latest commit that was just pulled
	ref, err := r.Head()
	if err != nil {
		return nil, err
	}

	return r.CommitObject(ref.Hash())
}

func cloneByCommand(ctx context.Context, gitURL, targetPath, branch string, co *CloneOptions) error {
	args := []string{"clone", "-v"}
	if branch != "" {
		args = append(args, "--branch", branch)
	}
	if co.SingleBranch {
		args = append(args, "--single-branch")
	}
	if co.Depth > 0 {
		args = append(args, "--depth", strconv.Itoa(co.Depth))
	}
	if co.Progress != nil {
		args = append(args, "--progress")
	}
	args = append(args, gitURL, targetPath)

	cmd := CommandContext(ctx, gitCommand, args...)
	if co.Progress != nil {
		cmd.Stdout = co.Progress
		cmd.Stderr = co.Progress
	}

	return cmd.Run()
}

type (
	PullOptions struct {
		*git.PullOptions

		UseCommand bool
	}

	GitPullOption func(o *PullOptions)
)

func WithPullRemoteName(name string) GitPullOption {
	return func(o *PullOptions) {
		o.RemoteName = name
	}
}

func WithPullReferenceName(name plumbing.ReferenceName) GitPullOption {
	return func(o *PullOptions) {
		o.ReferenceName = name
	}
}

func WithPullSingleBranch() GitPullOption {
	return func(o *PullOptions) {
		o.SingleBranch = true
	}
}

func WithPullDepth(depth int) GitPullOption {
	return func(o *PullOptions) {
		o.Depth = depth
	}
}

func WithPullProgress(writer io.Writer) GitPullOption {
	return func(o *PullOptions) {
		o.Progress = writer
	}
}

func WithPullUseCommand() GitPullOption {
	return func(o *PullOptions) {
		o.UseCommand = true
	}
}

func Pull(targetPath string, options ...GitPullOption) (*object.Commit, error) {
	return PullWithContext(context.Background(), targetPath, options...)
}

func PullWithContext(ctx context.Context, targetPath string, options ...GitPullOption) (*object.Commit, error) {
	// we instantiate a new repository targeting the given path (the .git folder)
	r, err := git.PlainOpen(targetPath)
	if err != nil {
		return nil, err
	}

	// get the current commit
	ref, err := r.Head()
	if err != nil {
		return nil, err
	}

	// get the working directory for the repository
	w, err := r.Worktree()
	if err != nil {
		return nil, err
	}

	po := &PullOptions{
		PullOptions: &git.PullOptions{
			RemoteName:    git.DefaultRemoteName,
			ReferenceName: ref.Name(),
			Progress:      os.Stdout,
		},
	}
	for _, option := range options {
		option(po)
	}

	// pull the latest changes from the origin remote and merge into the current branch
	if HasGitCommand() && po.UseCommand {
		err = pullByCommand(ctx, targetPath, po)
	} else {
		err = w.PullContext(ctx, po.PullOptions)
	}
	if err != nil && !errors.Is(err, git.NoErrAlreadyUpToDate) && !strings.Contains(err.Error(), alreadyUpToDate) {
		return nil, err
	}

	// get the latest commit that was just pulled
	ref, err = r.Head()
	if err != nil {
		return nil, err
	}

	return r.CommitObject(ref.Hash())
}

func pullByCommand(ctx context.Context, targetPath string, po *PullOptions) error {
	args := []string{"pull", "--no-stat", "-v"}
	//if po.SingleBranch {
	//	args = append(args, "--single-branch")
	//}
	if po.Depth > 0 {
		args = append(args, "--depth", strconv.Itoa(po.Depth))
	}
	if po.Progress != nil {
		args = append(args, "--progress")
	}
	args = append(args, po.RemoteName, po.ReferenceName.String())

	cmd := CommandContext(ctx, gitCommand, args...)
	cmd.Dir = targetPath
	if po.Progress != nil {
		cmd.Stdout = po.Progress
		cmd.Stderr = po.Progress
	}

	return cmd.Run()
}

func GetLastCommit(targetPath string) (*object.Commit, error) {
	// we instantiate a new repository targeting the given path (the .git folder)
	r, err := git.PlainOpen(targetPath)
	if err != nil {
		return nil, err
	}

	// get the current commit
	ref, err := r.Head()
	if err != nil {
		return nil, err
	}

	return r.CommitObject(ref.Hash())
}

func IsValidGitUrl(gitURL string) bool {
	u, err := url.Parse(gitURL)
	if err != nil {
		return false
	}

	if u.Scheme != string(constants.GIT) && u.Scheme != string(constants.HTTPS) && u.Scheme != string(constants.HTTP) && u.Scheme != string(constants.SSH) {
		return false
	} else if u.Path == "" {
		return false
	}

	return true
}

func HasGitCommand() bool {
	_, err := exec.LookPath(gitCommand)
	return err == nil
}

func GetGitCommandVersion() string {
	output, err := CommandContext(context.Background(), gitCommand, "--version").CombinedOutput()
	if err != nil {
		return "Unknown"
	}

	return string(output)
}
