package utils

import (
	"reflect"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mapping"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

func CleanUp(v any) (err error) {
	defer func() {
		r := recover()
		if e, ok := r.(error); ok {
			err = e
		}
		if err != nil {
			err = errors.WithStack(errorx.Err(errorx.ResetObjectFailure, err.Error()))
		}
	}()

	rv := reflect.ValueOf(v)
	if err = mapping.ValidatePtr(rv); err != nil {
		return
	}

	rve := rv.Elem()
	rve.Set(reflect.Zero(rve.Type()))

	return nil
}

func ClearMap[M ~map[K]V, K comparable, V any](m M) {
	// Relies on the compiler optimization introduced in Go v1.11
	// https://go.dev/doc/go1.11#performance-compiler
	for k := range m {
		delete(m, k)
	}
}
