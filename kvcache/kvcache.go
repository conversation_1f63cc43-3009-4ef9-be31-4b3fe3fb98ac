package kvcache

import (
	"context"
	"fmt"
	"time"

	redis "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/logx"
	red "github.com/zeromicro/go-zero/core/stores/redis"
)

const (
	CONST_SCAN_COUNT = 100
)

type CacheHandler func(ctx context.Context, key string) (value string, err error)

type Option struct {
	TTL         time.Duration // 最大缓存时间
	AutoRefresh time.Duration // 自动刷新时间, 默认为0则不刷新， 间隔不能低于5min
	Refresh     bool          // 是否马上刷新缓存
}

// KvCache key-value 缓存
type KvCache struct {
	redis redis.UniversalClient

	refresh *Refresh
}

func NewKvCache(redis redis.UniversalClient, redislock *red.Redis) *KvCache {
	cache := &KvCache{
		redis: redis,
	}
	cache.refresh = NewRefresh(cache, redis, redislock)

	return cache
}

func (cache *KvCache) Set(ctx context.Context, key, value string, opt Option) (err error) {
	err = cache.redis.Set(ctx, key, value, opt.TTL).Err()
	if err != nil {
		return fmt.Errorf("KvCache Set error, key: %s, err: %s", key, err)
	}

	return nil
}

func (cache *KvCache) setWithRefresh(
	ctx context.Context, key, value string, opt Option, do CacheHandler,
) (err error) {
	err = cache.Set(ctx, key, value, opt)
	if err != nil {
		return
	}

	if opt.AutoRefresh > 0 {
		return cache.refresh.RegisterTask(key, opt.AutoRefresh, do)
	}
	return nil
}

func (cache *KvCache) Get(ctx context.Context, key string) (value string, err error) {
	value, err = cache.redis.Get(ctx, key).Result()
	if err != nil {
		return "", fmt.Errorf("CacheControl.Get 缓存失败, err:%s", err)
	}

	return value, nil
}

func (cache *KvCache) CacheDo(ctx context.Context, key string, do CacheHandler, opt Option) (value string, err error) {
	if !opt.Refresh {
		// 从缓存获取
		value, err = cache.Get(ctx, key)
		if err == nil {
			return value, nil
		}
	}

	// 获取真实数据
	value, err = do(ctx, key)
	if err != nil {
		return "", err
	}

	// 写缓存
	err = cache.setWithRefresh(ctx, key, value, opt, do)
	if err != nil {
		logx.Warnf("%s", err)
	}

	return value, nil
}

// GetAllCacheKey 获取所有缓存key
func (cache *KvCache) GetAllCacheKey(ctx context.Context, keyword string) (keys []string, err error) {
	cursor := uint64(0)
	keys = make([]string, 0, CONST_SCAN_COUNT*3)
	for {
		regex := fmt.Sprintf("*%s*", keyword)
		ckeys, nextCursor, err := cache.redis.Scan(ctx, cursor, regex, CONST_SCAN_COUNT).Result()
		if err != nil {
			return nil, err
		}

		keys = append(keys, ckeys...)
		if nextCursor == 0 {
			break
		}

		cursor = nextCursor
	}

	return keys, nil
}

// Clear 获取所有缓存key
func (cache *KvCache) Clear(ctx context.Context, keys []string) (err error) {
	return cache.redis.Del(ctx, keys...).Err()
}
