package kvcache

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	redis "github.com/redis/go-redis/v9"
	red "github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
)

const (
	CONST_REFRESH_INTERNAL      = 5 * time.Minute    // 扫描时间间隔
	CONST_REFRESH_LOCK_INTERNAL = 30 * time.Second   // lock间隔
	CONST_MAX_REFRESH_KEY_TTL   = 3 * time.Hour * 24 // refresh key 最大缓存时间
	CONST_REFRESH_COUNT         = 100
	CONST_REFRESH_DO_COUNT      = 10

	CONST_REFRESH_KEY_PREFIX      = "kvcache::refresh::"       // 记录最后刷新时间
	CONST_REFRESH_KEY_LOCK_PREFIX = "lock::kvcache::refresh::" // 记录lock，一次只能有一个在操作
)

func GenRefreshKey(key string) string {
	return fmt.Sprintf("%s%s", CONST_REFRESH_KEY_PREFIX, key)
}

func GenRefreshLockKey(key string) string {
	return fmt.Sprintf("%s%s", CONST_REFRESH_KEY_LOCK_PREFIX, key)
}

type Refresh struct {
	redis     redis.UniversalClient
	redislock *red.Redis
	kvcache   *KvCache

	lock  sync.Mutex
	tasks map[string]*RefreshTask
	stop  chan struct{}
}

type RefreshTask struct {
	Key        string        `zh:"缓存的key"`
	RefreshTTL time.Duration `zh:"刷新时间间隔"`
	Do         CacheHandler  `zh:"获取最新结果的处理逻辑"`
}

func NewRefresh(kvcache *KvCache, redis redis.UniversalClient, redislock *red.Redis) *Refresh {
	r := &Refresh{
		redis:     redis,
		redislock: redislock,
		kvcache:   kvcache,
		tasks:     make(map[string]*RefreshTask),
		stop:      make(chan struct{}),
	}

	go r.start()

	return r
}

func (r *Refresh) Close() {
	r.stop <- struct{}{}
}

func (r *Refresh) RegisterTask(key string, rttl time.Duration, do CacheHandler) (err error) {
	r.lock.Lock()
	defer r.lock.Unlock()

	r.tasks[key] = &RefreshTask{
		Key:        key,
		RefreshTTL: rttl,
		Do:         do,
	}

	err = r.redis.Set(context.Background(), GenRefreshKey(key), time.Now().Unix(), CONST_MAX_REFRESH_KEY_TTL).Err()
	if err != nil {
		return err
	}

	return
}

func (r *Refresh) StopTask(key string) {
	r.lock.Lock()
	defer r.lock.Unlock()

	delete(r.tasks, key)
}

func (r *Refresh) GetTask(key string) (task *RefreshTask, exist bool) {
	r.lock.Lock()
	defer r.lock.Unlock()

	task, exist = r.tasks[key]
	return task, exist
}

func (r *Refresh) start() {
	ticker := time.NewTicker(CONST_REFRESH_INTERNAL)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			r.scan()
		case <-r.stop:
			return
		}
	}
}

func (r *Refresh) scan() {
	_ = caller.MultiDo(
		context.Background(), r.AllTaskKey(), func(item any) error {
			key := item.(string)
			task, exist := r.GetTask(key)
			if !exist {
				return nil
			}

			// 避免并发操作
			err := caller.LockDo(
				r.redislock, GenRefreshLockKey(task.Key), func() error {
					// 获取最后执行的时间戳
					unix, err := r.redis.Get(context.Background(), GenRefreshKey(task.Key)).Int64()
					if err != nil {
						return err
					}

					// 未到刷新时间
					if time.Unix(unix, 0).After(time.Now().Add(-task.RefreshTTL)) {
						return nil
					}

					// 获取TTL
					ttl, err := r.redis.TTL(context.Background(), task.Key).Result()
					if err != nil {
						return err
					}

					if ttl == 0 {
						r.redis.Del(context.Background(), GenRefreshKey(task.Key))
						r.StopTask(task.Key)
						return nil
					}

					// 刷新数据
					value, err := task.Do(context.Background(), task.Key)
					if err != nil {
						return err
					}

					// 记录时间
					err = r.kvcache.Set(context.Background(), key, value, Option{TTL: ttl})
					if err != nil {
						return err
					}

					// 继续最后refresh的时间
					err = r.redis.Set(
						context.Background(), GenRefreshKey(key), time.Now().Unix(), CONST_MAX_REFRESH_KEY_TTL,
					).Err()
					if err != nil {
						return err
					}

					return nil
				}, int(CONST_REFRESH_LOCK_INTERNAL),
			)
			if err != nil {
				return err
			}
			return nil
		}, CONST_REFRESH_DO_COUNT,
	)
}

func (r *Refresh) AllTaskKey() []string {
	r.lock.Lock()
	defer r.lock.Unlock()

	keys := make([]string, 0, len(r.tasks))
	for k := range r.tasks {
		keys = append(keys, k)
	}
	return keys
}

// GetAllRefreshCacheKey 返回所有保持refresh的cachekey
func (r *Refresh) GetAllRefreshCacheKey(ctx context.Context, prefix string) (keys []string, err error) {
	cursor := uint64(0)
	keys = make([]string, 0, CONST_REFRESH_COUNT*3)
	for {
		keyprefix := GenRefreshKey(prefix)
		ckeys, nextCursor, err := r.redis.Scan(ctx, cursor, keyprefix, CONST_REFRESH_COUNT).Result()
		if err != nil {
			return nil, err
		}

		for _, refreshkey := range ckeys {
			// 如果key已经不存在，则删除
			cachekey := strings.TrimPrefix(refreshkey, CONST_REFRESH_KEY_PREFIX)

			ok, err := r.redis.Exists(ctx, cachekey).Uint64()
			if err != nil {
				return nil, err
			}

			// 不存在，则删除refresh
			if ok == 0 {
				r.redis.Del(ctx, refreshkey)
				r.StopTask(cachekey)
			}

			keys = append(keys, cachekey)
		}

		if nextCursor == 0 {
			break
		}

		cursor = nextCursor
	}

	return keys, nil
}
