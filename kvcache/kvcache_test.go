package kvcache

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/stores/redis"

	credis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"
)

func TestCacheDo(t *testing.T) {
	redisconf := redis.RedisConf{
		Host: "*************:6379",
		Type: "node",
		Pass: "Quwan@2020",
		DB:   11,
	}

	cache := NewKvCache(credis.NewClient(redisconf), redis.MustNewRedis(redisconf, redis.WithDB(redisconf.DB)))

	key := "cache_test_key"

	cvalue, err := cache.CacheDo(context.Background(), key, func(ctx context.Context, key string) (value string, err error) {
		value = fmt.Sprintf("%s_%d", "cache_test_value", time.Now().Unix())
		return value, nil
	}, Option{TTL: 10 * time.Minute, AutoRefresh: 10 * time.Second})
	if err != nil {
		t.<PERSON><PERSON><PERSON>("err: %s", err)
		t.<PERSON><PERSON>ow()
	}

	t.Logf("cvalue = %s", cvalue)
}
