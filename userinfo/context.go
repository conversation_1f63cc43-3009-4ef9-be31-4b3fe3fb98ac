package userinfo

import "context"

type userInfoContextKeyType int

const userInfoKey userInfoContextKeyType = iota

func WithContext(parent context.Context, ui *UserInfo) context.Context {
	if v, ok := parent.Value(userInfoKey).(*UserInfo); ok {
		*v = *ui
		return parent
	}

	return context.WithValue(parent, userInfoKey, ui)
}

func FromContext(ctx context.Context) *UserInfo {
	if v, ok := ctx.Value(userInfoKey).(*UserInfo); ok {
		return v
	}

	return nil
}
