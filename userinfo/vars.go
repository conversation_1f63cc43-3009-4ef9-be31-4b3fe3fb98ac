package userinfo

import "github.com/zeromicro/go-zero/core/stringx"

var (
	builtinUserIds = []string{adminUserId, systemUserId}

	adminUserInfo = FullUserInfo{
		TokenUserInfo: TokenUserInfo{
			Account:  adminUserId,
			Fullname: "系统管理员",
			DeptName: "质量平台组",
			Email:    "<EMAIL>",
			Mobile:   "",
		},
		DeptId:       "-5190184670346721174",
		FullDeptName: "研发中心-研发效能部-质量平台组",
		Photo:        "",
		Enabled:      true,
	}

	systemUserInfo = FullUserInfo{
		TokenUserInfo: TokenUserInfo{
			Account:  systemUserId,
			Fullname: "内部系统用户",
			DeptName: "质量平台组",
			Email:    "",
			Mobile:   "",
		},
		DeptId:       "-5190184670346721174",
		FullDeptName: "研发中心-研发效能部-质量平台组",
		Photo:        "",
		Enabled:      true,
	}
)

func Admin() FullUserInfo {
	return adminUserInfo
}

func System() FullUserInfo {
	return systemUserInfo
}

func IsBuiltinUser(account string) bool {
	return stringx.Contains(builtinUserIds, account)
}
