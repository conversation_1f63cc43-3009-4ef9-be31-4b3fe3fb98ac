package userinfo

import (
	"encoding/base64"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
)

func TestAdmin(t *testing.T) {
	// hasNotPrintable return true if msg contains any characters which are not in %x20-%x7E
	b0 := jsonx.MarshalIgnoreError(adminUserInfo)
	s0 := string(b0)
	t.Logf("json: %s\n", s0)
	for i := 0; i < len(s0); i++ {
		t.Logf("%d: %c: %x\n", i, s0[i], s0[i])
	}

	// base64 encode
	s1 := base64.StdEncoding.EncodeToString(b0)
	t.Logf("base64 encode: %s\n", s1)

	// base64 decode
	b2, err := base64.StdEncoding.DecodeString(s1)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("base64 decode: %s\n", string(b2))
}
