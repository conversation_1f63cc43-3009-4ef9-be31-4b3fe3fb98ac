package userinfo

// TokenUserInfo for token
type TokenUserInfo struct {
	Account  string `json:"account"`
	Fullname string `json:"fullname"`
	DeptName string `json:"dept_name"`
	Email    string `json:"email"`
	Mobile   string `json:"mobile"`
}

// ApiUserInfo for api response
type ApiUserInfo struct {
	TokenUserInfo

	DeptId       string `json:"dept_id"`
	FullDeptName string `json:"full_dept_name"`
	PostId       string `json:"post_id"`
	PostName     string `json:"post_name"`
	UserType     string `json:"user_type"`
	Sex          string `json:"sex"`
	Photo        string `json:"photo"`
	Enabled      bool   `json:"enabled"`
}

type (
	// UserInfo is an alias of TokenUserInfo
	UserInfo = TokenUserInfo

	// FullUserInfo is an alias of ApiUserInfo
	FullUserInfo = ApiUserInfo
)
