package tcp

import (
	"context"
	"crypto/tls"
	"io"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
)

const (
	queueLen   = 128
	bufferSize = 1024 * 4

	ProtoTCP = string(constants.TCP)
	ProtoSSL = "ssl"
)

const (
	// PackageLess shows is not a completed package.
	PackageLess = iota
	// PackageFull shows is a completed package.
	PackageFull
	// PackageError shows is an error package.
	PackageError
)

type (
	// ClientProtocol interface for handling tars client package.
	ClientProtocol interface {
		Recv(pkg []byte)
		ParsePackage(buff []byte) (int, int)
	}

	// ClientConf is tcp client side config
	ClientConf struct {
		Proto        string
		QueueLen     int
		IdleTimeout  time.Duration
		ReadTimeout  time.Duration
		WriteTimeout time.Duration
		DialTimeout  time.Duration
		TlsConfig    *tls.Config
	}

	// Client is a tcp client
	Client struct {
		ctx     context.Context
		address string
		cp      ClientProtocol
		conf    ClientConf

		conn          net.Conn
		connLock      sync.Mutex
		connDone      chan lang.PlaceholderType
		sendQueue     chan sendMsg
		sendFailQueue chan sendMsg
		isClosed      atomic.Bool
		idleTime      time.Time
		invokeNum     int32
	}

	// sendMsg is a struct of sending message
	sendMsg struct {
		req   []byte
		retry uint8
	}
)

// NewClient create tcp client
func NewClient(address string, cp ClientProtocol, conf ClientConf) *Client {
	return NewClientWithContext(context.Background(), address, cp, conf)
}

// NewClientWithContext create tcp client with context
func NewClientWithContext(ctx context.Context, address string, cp ClientProtocol, conf ClientConf) *Client {
	if ctx == nil {
		ctx = context.Background()
	}
	if conf.Proto == "" {
		if conf.TlsConfig == nil {
			conf.Proto = ProtoTCP
		} else {
			conf.Proto = ProtoSSL
		}
	}
	if conf.QueueLen <= 0 {
		conf.QueueLen = queueLen
	}

	c := &Client{
		ctx:     ctx,
		address: address,
		cp:      cp,
		conf:    conf,

		connDone:      make(chan lang.PlaceholderType, 1),
		sendQueue:     make(chan sendMsg, conf.QueueLen),
		sendFailQueue: make(chan sendMsg, 1),
	}
	c.isClosed.Store(true)

	return c
}

// ReConnect established the client connection with the server.
func (c *Client) ReConnect() (err error) {
	c.connLock.Lock()
	defer c.connLock.Unlock()

	if c.isClosed.Load() {
		logx.Debugf("Connect: %s, Proto: %s", c.address, c.conf.Proto)

		if c.conf.Proto == ProtoSSL {
			dialer := &net.Dialer{Timeout: c.conf.DialTimeout}
			c.conn, err = tls.DialWithDialer(dialer, ProtoTCP, c.address, c.conf.TlsConfig)
		} else {
			c.conn, err = net.DialTimeout(c.conf.Proto, c.address, c.conf.DialTimeout)
		}
		if err != nil {
			return err
		}

		if c.conf.Proto == ProtoTCP && c.conn != nil {
			if conn, ok := c.conn.(*net.TCPConn); ok {
				if err = conn.SetKeepAlive(true); err != nil {
					return err
				}
			}
		}

		c.idleTime = time.Now()
		c.isClosed.Store(false)
		threading.GoSafe(c.recv)
		threading.GoSafe(c.send)
	}

	return nil
}

// Send sends the request to the server as []byte.
func (c *Client) Send(req []byte) error {
	if err := c.ReConnect(); err != nil {
		return err
	}

	// avoid full sendQueue that cause sending block
	var timerC <-chan time.Time
	if c.conf.WriteTimeout > 0 {
		timerC = timewheel.After(c.conf.WriteTimeout)
	}

	select {
	case <-timerC:
		return errors.Errorf("tcp client write timeout[%s]", c.conf.WriteTimeout.String())
	case c.sendQueue <- sendMsg{req: req}:
	}

	return nil
}

// Close the client connection with the server.
func (c *Client) Close() error {
	return c.close()
}

// CloseGracefully close client gracefully.
func (c *Client) CloseGracefully(ctx context.Context) {
	t := timewheel.NewTicker(time.Millisecond * 500)
	defer t.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-t.C:
			logx.Debugf("wait grace invoke %d", c.invokeNum)
			if atomic.LoadInt32(&c.invokeNum) <= 0 {
				if err := c.Close(); err != nil {
					logx.Errorf("failed to close the connection of tcp client, error: %v", err)
				}
				return
			}
		}
	}
}

func (c *Client) send() {
	t := timewheel.NewTicker(time.Second)
	defer t.Stop()

	var m sendMsg
	for {
		select {
		case <-c.connDone: // connection closed
			return
		case m = <-c.sendFailQueue: // fetch a message from failure queue
		case m = <-c.sendQueue: // fetch a message from normal queue
		case <-t.C:
			if c.isClosed.Load() {
				return
			} else if c.invokeNum == 0 && c.idleTime.Add(c.conf.IdleTimeout).Before(time.Now()) {
				_ = c.close()
				return
			}
			continue
		}

		atomic.AddInt32(&c.invokeNum, 1)
		if c.conf.WriteTimeout != 0 {
			if err := c.conn.SetWriteDeadline(time.Now().Add(c.conf.WriteTimeout)); err != nil {
				logx.Errorf("failed to set write deadline, error: %v", err)
			}
		}
		c.idleTime = time.Now()
		_, err := c.conn.Write(m.req)
		if err != nil {
			m.retry++
			c.sendFailQueue <- m
			logx.Errorf("failed to send message to tcp connection, retry: %d, error: %v", m.retry, err)

			_ = c.close()

			if !errors.Is(err, net.ErrClosed) {
				return
			}

			// connection closed, try to reconnect once
			if err = c.ReConnect(); err != nil {
				logx.Errorf("failed to reconnect the server: %v, error: %v", c.conn.RemoteAddr(), err)
			}
			return
		}
	}
}

func (c *Client) recv() {
	defer func() {
		_ = c.close()
		c.connDone <- lang.Placeholder
	}()

	var (
		buffer = make([]byte, bufferSize)

		currBuffer []byte
		n          int
		err        error
	)

	for {
		if c.isClosed.Load() || c.conn == nil {
			logx.Debug("the connection has been closed")
			return
		}

		if c.conf.ReadTimeout != 0 {
			if err = c.conn.SetReadDeadline(time.Now().Add(c.conf.ReadTimeout)); err != nil {
				logx.Errorf("failed to set read deadline, error: %v", err)
			}
		}

		n, err = c.conn.Read(buffer)
		if err != nil {
			var netErr net.Error
			if errors.As(err, &netErr) && netErr.Timeout() {
				continue // no data, not error
			}

			var opError *net.OpError
			if errors.As(err, &opError) {
				if !c.isClosed.Load() && c.conn != nil {
					logx.Errorf("got an operation error from remote: %v, error: %v", c.conn.RemoteAddr(), err)
				}

				return // connection is closed
			}

			if err == io.EOF {
				logx.Debugf("connection closed by remote: %v, error: %v", c.conn.RemoteAddr(), err)
			} else {
				logx.Errorf("failed to receive message from tcp connection, error: %v", err)
			}
			return
		}

		currBuffer = append(currBuffer, buffer[:n]...)
		for {
			pkgLen, status := c.cp.ParsePackage(currBuffer)
			if status == PackageLess {
				break
			} else if status == PackageFull {
				atomic.AddInt32(&c.invokeNum, -1)

				pkg := make([]byte, pkgLen)
				copy(pkg, currBuffer[0:pkgLen])
				currBuffer = currBuffer[pkgLen:]

				threading.GoSafe(
					func() {
						c.cp.Recv(pkg)
					},
				)

				if len(currBuffer) > 0 {
					continue
				}
				currBuffer = nil
				break
			}

			logx.Errorf("failed to parse package, pkgLen: %d, status: %d", pkgLen, status)
			return
		}
	}
}

func (c *Client) close() (err error) {
	c.connLock.Lock()
	defer c.connLock.Unlock()

	c.isClosed.Store(true)
	if c.conn != nil {
		err = c.conn.Close()
		// c.conn = nil
	}

	return err
}
