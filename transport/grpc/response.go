package grpc

import (
	"fmt"
	"io"

	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

type (
	ResponseHandler interface {
		// OnReceiveHeaders is called when response headers have been received.
		OnReceiveHeaders(metadata.MD)
		// OnReceiveResponse is called for each response message received.
		OnReceiveResponse(proto.Message)
		// OnReceiveTrailers is called when response trailers and final RPC status have been received.
		OnReceiveTrailers(*status.Status, metadata.MD)
	}

	Response struct {
		md      protoreflect.MethodDescriptor
		handler ResponseHandler
		header  metadata.MD
		trailer metadata.MD
		status  *status.Status
	}
)

var (
	_noopHandler ResponseHandler = noopHandler{}
	_            ResponseHandler = (*outputHandler)(nil)
)

func NewResponse(method protoreflect.MethodDescriptor, handler ResponseHandler) *Response {
	if handler == nil {
		handler = _noopHandler
	}

	return &Response{
		md:      method,
		handler: handler,
	}
}

func (r *Response) Header() metadata.MD {
	return r.header
}

func (r *Response) Trailer() metadata.MD {
	return r.trailer
}

func (r *Response) Status() *status.Status {
	return r.status
}

type noopHandler struct{}

func (h noopHandler) OnReceiveHeaders(metadata.MD)                  {}
func (h noopHandler) OnReceiveResponse(proto.Message)               {}
func (h noopHandler) OnReceiveTrailers(*status.Status, metadata.MD) {}

type outputHandler struct {
	Out io.Writer
}

func (h *outputHandler) OnReceiveHeaders(md metadata.MD) {
	_, _ = fmt.Fprintf(h.Out, "headers: %s\n", MetadataToString(md))
}

func (h *outputHandler) OnReceiveResponse(m proto.Message) {
	_, _ = fmt.Fprintf(h.Out, "body: %s\n", protobuf.MarshalJSONIgnoreError(m))
}

func (h *outputHandler) OnReceiveTrailers(s *status.Status, md metadata.MD) {
	_, _ = fmt.Fprintf(h.Out, "status: %s\ntrailers: %s\n", s, MetadataToString(md))
}
