package grpc

import (
	"context"
	"fmt"
	"io"

	"github.com/pkg/errors"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/dynamicpb"
)

const (
	UnaryMethodType        = "unary"
	serverStreamMethodType = "server-streaming"
	clientStreamMethodType = "client-streaming"
	bidiStreamMethodType   = "bidi-streaming"
)

type (
	Stub struct {
		conn *grpc.ClientConn
	}

	RequestMethod struct {
		md     protoreflect.MethodDescriptor
		method string
	}
)

func NewStub(conn *grpc.ClientConn) *Stub {
	return &Stub{conn: conn}
}

// InvokeRpcUnary sends a unary RPC and returns the response. Use this for unary methods.
func (s *Stub) InvokeRpcUnary(
	ctx context.Context, rm RequestMethod, req, resp *dynamicpb.Message,
	opts ...grpc.CallOption,
) (err error) {
	md := rm.md
	method := rm.method
	if method == "" {
		method = requestMethod(md)
	}

	if md == nil {
		return errors.New("the method descriptor of InvokeRpcUnary is null")
	} else if md.IsStreamingClient() || md.IsStreamingServer() {
		return errors.Errorf("InvokeRpcUnary is for unary methods; %q is %s", md.FullName(), methodType(md))
	} else if req == nil {
		return errors.New("the request message of InvokeRpcUnary is null")
	} else if resp == nil {
		return errors.New("the response message of InvokeRpcUnary is null")
	}

	if err = checkMessageType(md.Input(), req); err != nil {
		return err
	} else if err = checkMessageType(md.Output(), resp); err != nil {
		return err
	}

	if err = s.conn.Invoke(ctx, method, req, resp, opts...); err != nil {
		return err
	}
	return nil
}

// InvokeRpcServerStream sends a unary RPC and returns the response stream. Use this for server-streaming methods.
func (s *Stub) InvokeRpcServerStream(
	ctx context.Context, rm RequestMethod, req *dynamicpb.Message, opts ...grpc.CallOption,
) (*ServerStream, error) {
	md := rm.md
	method := rm.method
	if method == "" {
		method = requestMethod(md)
	}

	if md == nil {
		return nil, errors.New("the method descriptor of InvokeRpcServerStream is null")
	} else if md.IsStreamingClient() || !md.IsStreamingServer() {
		return nil, fmt.Errorf(
			"InvokeRpcServerStream is for server-streaming methods; %q is %s", md.FullName(), methodType(md),
		)
	} else if req == nil {
		return nil, errors.New("the request message of InvokeRpcServerStream is null")
	}

	if err := checkMessageType(md.Input(), req); err != nil {
		return nil, err
	}

	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	sd := grpc.StreamDesc{
		StreamName:    string(md.Name()),
		ServerStreams: md.IsStreamingServer(),
		ClientStreams: md.IsStreamingClient(),
	}
	if cs, err := s.conn.NewStream(ctx, &sd, method, opts...); err != nil {
		return nil, err
	} else {
		err = cs.SendMsg(req)
		if err != nil {
			return nil, err
		}

		err = cs.CloseSend()
		if err != nil {
			return nil, err
		}

		return &ServerStream{cs, md.Output()}, nil
	}
}

// InvokeRpcClientStream creates a new stream that is used to send request messages and, at the end, receive the response message. Use this for client-streaming methods.
func (s *Stub) InvokeRpcClientStream(
	ctx context.Context, rm RequestMethod, opts ...grpc.CallOption,
) (*ClientStream, error) {
	md := rm.md
	method := rm.method
	if method == "" {
		method = requestMethod(md)
	}

	if md == nil {
		return nil, errors.New("the method descriptor of InvokeRpcClientStream is null")
	} else if !md.IsStreamingClient() || md.IsStreamingServer() {
		return nil, fmt.Errorf(
			"InvokeRpcClientStream is for client-streaming methods; %q is %s", md.FullName(), methodType(md),
		)
	}

	ctx, cancel := context.WithCancel(ctx)
	sd := grpc.StreamDesc{
		StreamName:    string(md.Name()),
		ServerStreams: md.IsStreamingServer(),
		ClientStreams: md.IsStreamingClient(),
	}
	if cs, err := s.conn.NewStream(ctx, &sd, method, opts...); err != nil {
		cancel()

		return nil, err
	} else {
		return &ClientStream{cs, md, cancel}, nil
	}
}

// InvokeRpcBidiStream creates a new stream that is used to both send request messages and receive response messages. Use this for bidi-streaming methods.
func (s *Stub) InvokeRpcBidiStream(
	ctx context.Context, rm RequestMethod, opts ...grpc.CallOption,
) (*BidiStream, error) {
	md := rm.md
	method := rm.method
	if method == "" {
		method = requestMethod(md)
	}

	if md == nil {
		return nil, errors.New("the method descriptor of InvokeRpcBidiStream null")
	} else if !md.IsStreamingClient() || !md.IsStreamingServer() {
		return nil, errors.Errorf(
			"InvokeRpcBidiStream is for bidi-streaming methods; %q is %s", md.FullName(), methodType(md),
		)
	}
	sd := grpc.StreamDesc{
		StreamName:    string(md.Name()),
		ServerStreams: md.IsStreamingServer(),
		ClientStreams: md.IsStreamingClient(),
	}
	if cs, err := s.conn.NewStream(ctx, &sd, method, opts...); err != nil {
		return nil, err
	} else {
		return &BidiStream{cs, md.Input(), md.Output()}, nil
	}
}

func requestMethod(md protoreflect.MethodDescriptor) string {
	service, _ := md.Parent().(protoreflect.ServiceDescriptor)
	return fmt.Sprintf("/%s/%s", service.FullName(), md.Name())
}

func methodType(md protoreflect.MethodDescriptor) string {
	if md.IsStreamingClient() && md.IsStreamingServer() {
		return bidiStreamMethodType
	} else if md.IsStreamingClient() {
		return clientStreamMethodType
	} else if md.IsStreamingServer() {
		return serverStreamMethodType
	} else {
		return UnaryMethodType
	}
}

func checkMessageType(md protoreflect.MessageDescriptor, msg *dynamicpb.Message) error {
	expected := md.FullName()
	actual := msg.Descriptor().FullName()
	if actual != expected {
		return errors.Errorf("expecting message of type %s; got %s", expected, actual)
	}

	return nil
}

// ServerStream represents a response stream from a server. Messages in the stream can be queried
// as can header and trailer metadata sent by the server.
type ServerStream struct {
	stream grpc.ClientStream
	output protoreflect.MessageDescriptor
}

// Header returns any header metadata sent by the server (blocks if necessary until headers are received).
func (s *ServerStream) Header() (metadata.MD, error) {
	return s.stream.Header()
}

// Trailer returns the trailer metadata sent by the server. It must only be called after
// RecvMsg returns a non-nil error (which may be EOF for normal completion of stream).
func (s *ServerStream) Trailer() metadata.MD {
	return s.stream.Trailer()
}

// Context returns the context associated with this streaming operation.
func (s *ServerStream) Context() context.Context {
	return s.stream.Context()
}

// RecvMsg returns the next message in the response stream or an error. If the stream
// has completed normally, the error is io.EOF. Otherwise, the error indicates the
// nature of the abnormal termination of the stream.
func (s *ServerStream) RecvMsg(m *dynamicpb.Message) error {
	if err := checkMessageType(s.output, m); err != nil {
		return err
	}

	return s.stream.RecvMsg(m)
}

// ClientStream represents a response stream from a client. Messages in the stream can be sent
// and, when done, the unary server message and header and trailer metadata can be queried.
type ClientStream struct {
	stream grpc.ClientStream
	method protoreflect.MethodDescriptor
	cancel context.CancelFunc
}

// Header returns any header metadata sent by the server (blocks if necessary until headers are received).
func (s *ClientStream) Header() (metadata.MD, error) {
	return s.stream.Header()
}

// Trailer returns the trailer metadata sent by the server. It must only be called after
// RecvMsg returns a non-nil error (which may be EOF for normal completion of stream).
func (s *ClientStream) Trailer() metadata.MD {
	return s.stream.Trailer()
}

// Context returns the context associated with this streaming operation.
func (s *ClientStream) Context() context.Context {
	return s.stream.Context()
}

// SendMsg sends a request message to the server.
func (s *ClientStream) SendMsg(m *dynamicpb.Message) error {
	if err := checkMessageType(s.method.Input(), m); err != nil {
		return err
	}

	return s.stream.SendMsg(m)
}

// CloseAndReceive closes the outgoing request stream and then blocks for the server's response.
func (s *ClientStream) CloseAndReceive(m *dynamicpb.Message) (err error) {
	if err = s.stream.CloseSend(); err != nil {
		return
	}

	if err = checkMessageType(s.method.Output(), m); err != nil {
		return
	}

	if err = s.stream.RecvMsg(m); err != nil {
		return
	}
	// make sure we get EOF for a second message
	if err = s.stream.RecvMsg(m); err != io.EOF {
		if err == nil {
			s.cancel()

			return errors.Errorf(
				"method[%s] is a client-stream RPC, but returned more than one response message", s.method.FullName(),
			)
		} else {
			return err
		}
	}

	return nil
}

// BidiStream represents a bidirectional stream for sending messages to and receiving messages from a server.
// The header and trailer metadata sent by the server can also be queried.
type BidiStream struct {
	stream grpc.ClientStream
	input  protoreflect.MessageDescriptor
	output protoreflect.MessageDescriptor
}

// Header returns any header metadata sent by the server (blocks if necessary until headers are received).
func (s *BidiStream) Header() (metadata.MD, error) {
	return s.stream.Header()
}

// Trailer returns the trailer metadata sent by the server. It must only be called after
// RecvMsg returns a non-nil error (which may be EOF for normal completion of stream).
func (s *BidiStream) Trailer() metadata.MD {
	return s.stream.Trailer()
}

// Context returns the context associated with this streaming operation.
func (s *BidiStream) Context() context.Context {
	return s.stream.Context()
}

// SendMsg sends a request message to the server.
func (s *BidiStream) SendMsg(m *dynamicpb.Message) error {
	if err := checkMessageType(s.input, m); err != nil {
		return err
	}

	return s.stream.SendMsg(m)
}

// CloseSend indicates the request stream has ended. Invoke this after all request messages
// are sent (even if there are zero such messages).
func (s *BidiStream) CloseSend() error {
	return s.stream.CloseSend()
}

// RecvMsg returns the next message in the response stream or an error. If the stream
// has completed normally, the error is io.EOF. Otherwise, the error indicates the
// nature of the abnormal termination of the stream.
func (s *BidiStream) RecvMsg(m *dynamicpb.Message) error {
	if err := checkMessageType(s.output, m); err != nil {
		return err
	}

	return s.stream.RecvMsg(m)
}
