package grpc

import (
	"context"
	"io"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/dynamicpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

func TestClient_InvokeRPC(t *testing.T) {
	ctx := context.Background()
	c := NewClient(
		"testing-lfg-apiv2.ttyuyin.com:443", ClientConf{
			NoTLS:              false,
			InsecureSkipVerify: false,
		},
	)

	pm, err := protobuf.NewProtoManager(
		protobuf.WithProducts(
			protobuf.Product{
				Projects: []protobuf.Project{
					{
						Name:   "stellaris-api",
						Branch: "main",
						Path:   "../../../../TTProjects/stellaris-api",
						ImportPaths: []string{
							"../../../../GitHubProjects/googleapis",
						},
					},
				},
			},
		),
	)
	if !assert.NoError(t, err) {
		t.<PERSON>(err)
	}

	type args struct {
		method  string
		headers metadata.MD
		body    string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "quwan.stellaris.authentication.v1.AuthenticationService.SignIn",
			args: args{
				method: "quwan.stellaris.authentication.v1.AuthenticationService.SignIn",
				headers: metadata.MD{
					"x-tt-client-bundle-id": []string{"com.stellaris.flutter.app"},
					"x-tt-market":           []string{"0"},
					"x-tt-client-type":      []string{"9"},
					"x-tt-terminal-type":    []string{"3473427"},
					"x-tt-client-version":   []string{"104595461"},
					"x-tt-device-id":        []string{"d3ee3fa1d0e072dd2c64aecc65af5834"},
					//"x-qw-traffic-mark":     []string{"auth"},
				},
				body: `{"password_credential": {"identifier": "110258177", "password": "19de26d178969e755b8fc0782208127d"}}`,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				md, err := pm.FindMethodDescriptorByName(tt.args.method)
				if err != nil {
					t.Fatal(err)
				}

				req := &Request{
					md: md,
					supplier: func(m *dynamicpb.Message) error {
						if err := pm.UnmarshalMessage(m, []byte(tt.args.body)); err != nil {
							return err
						}
						return io.EOF
					},
				}
				resp := &Response{
					md: md,
					handler: &outputHandler{
						Out: os.Stdout,
					},
				}

				invokeCtx, invokeCancel := context.WithTimeout(ctx, 5*time.Second)
				defer invokeCancel()

				if err := c.InvokeRPC(invokeCtx, req, tt.args.headers, resp); (err != nil) != tt.wantErr {
					t.Errorf("InvokeRPC() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
