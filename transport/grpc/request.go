package grpc

import (
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/dynamicpb"
)

type (
	// RequestSupplier is a function that is called to populate messages for a gRPC operation.
	// The function should populate the given message or return a non-nil error.
	// If the supplier has no more messages, it should return io.EOF.
	// When it returns io.EOF, it should not in any way modify the given message argument.
	RequestSupplier func(*dynamicpb.Message) error

	Request struct {
		md       protoreflect.MethodDescriptor
		method   string
		headers  metadata.MD
		supplier RequestSupplier
		opts     []grpc.CallOption
	}

	Option func(*Request)
)

func WithMethod(method string) Option {
	return func(r *Request) {
		r.method = method
	}
}

func WithHeaders(headers metadata.MD) Option {
	return func(r *Request) {
		r.headers = headers
	}
}

func WithCallOptions(callOptions ...grpc.CallOption) Option {
	return func(r *Request) {
		r.opts = callOptions
	}
}

func NewRequest(method protoreflect.MethodDescriptor, supplier RequestSupplier, options ...Option) *Request {
	r := &Request{
		md:       method,
		supplier: supplier,
	}

	for _, option := range options {
		option(r)
	}

	return r
}
