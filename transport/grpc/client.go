package grpc

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"io"
	"net"
	"os"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
	"go.uber.org/atomic"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/dynamicpb"
)

const (
	defaultDialTimeout    = 10 * time.Second
	defaultKeepaliveTime  = 60 * time.Second
	defaultMaxMessageSize = 4 * 1024 * 1024
)

type (
	// ClientConf is grpc client side config
	ClientConf struct {
		DialTimeout        time.Duration
		KeepaliveTime      time.Duration
		MaxMessageSize     int
		Authority          string
		UserAgent          string
		NoTLS              bool
		InsecureSkipVerify bool
		RootCAFile         string
		ClientCertFile     string
		ClientKeyFile      string
	}

	// Client is a grpc client which wraps a *grpc.ClientConn
	Client struct {
		ctx     context.Context
		address string
		conf    ClientConf

		conn     *grpc.ClientConn
		connLock sync.Mutex
		isClosed bool
	}
)

// NewClient create grpc client
func NewClient(address string, conf ClientConf) *Client {
	return NewClientWithContext(context.Background(), address, conf)
}

// NewClientWithContext create grpc client with context
func NewClientWithContext(ctx context.Context, address string, conf ClientConf) *Client {
	if ctx == nil {
		ctx = context.Background()
	}

	return &Client{
		ctx:     ctx,
		address: address,
		conf:    conf,

		isClosed: true,
	}
}

// ClientTLSConfig builds transport-layer config for a gRPC client using the given properties.
func (c *Client) ClientTLSConfig() (conf *tls.Config, err error) {
	if !c.conf.NoTLS {
		conf = &tls.Config{
			MinVersion: tls.VersionTLS12,
		}

		if c.conf.ClientCertFile != "" {
			// Load the client certificates from disk
			certificate, err := tls.LoadX509KeyPair(c.conf.ClientCertFile, c.conf.ClientKeyFile)
			if err != nil {
				return nil, errors.Errorf("could not load client key pair: %v", err)
			}
			conf.Certificates = []tls.Certificate{certificate}
		}

		if c.conf.InsecureSkipVerify {
			conf.InsecureSkipVerify = true
		} else if c.conf.RootCAFile != "" {
			// Create a certificate pool from the certificate authority
			certPool := x509.NewCertPool()
			ca, err := os.ReadFile(c.conf.RootCAFile)
			if err != nil {
				return nil, errors.Errorf("could not read ca certificate: %v", err)
			}

			// Append the certificates from the CA
			if ok := certPool.AppendCertsFromPEM(ca); !ok {
				return nil, errors.New("failed to append ca certs")
			}

			conf.RootCAs = certPool
		}
	}

	return conf, nil
}

// ReConnect established the client connection with the server.
func (c *Client) ReConnect() (err error) {
	c.connLock.Lock()
	defer c.connLock.Unlock()

	if c.isClosed {
		logx.Debugf("Connect: %s", c.address)

		if err = c.dial(); err != nil {
			return err
		}

		c.isClosed = false
	}

	return nil
}

func (c *Client) dial() (err error) {
	var opts []grpc.DialOption

	keepaliveTime := defaultKeepaliveTime
	if c.conf.KeepaliveTime >= time.Second {
		keepaliveTime = c.conf.KeepaliveTime
	}
	opts = append(
		opts, grpc.WithKeepaliveParams(
			keepalive.ClientParameters{
				Time:    keepaliveTime,
				Timeout: keepaliveTime,
			},
		),
	)

	maxSize := defaultMaxMessageSize
	if c.conf.MaxMessageSize > 0 {
		maxSize = c.conf.MaxMessageSize
	}
	opts = append(opts, grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(maxSize)))

	tlsConfig, err := c.ClientTLSConfig()
	if err != nil {
		logx.Errorf("failed to create client tls config, err: %+v", err)
	}
	if tlsConfig == nil {
		opts = append(opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	} else {
		opts = append(opts, grpc.WithTransportCredentials(credentials.NewTLS(tlsConfig)))
	}

	if c.conf.Authority != "" {
		opts = append(opts, grpc.WithAuthority(c.conf.Authority))
	}

	if c.conf.UserAgent != "" {
		opts = append(opts, grpc.WithUserAgent(c.conf.UserAgent))
	}

	return c.BlockingDial(opts)
}

// BlockingDial is a helper method to dial the given address, and blocking until the returned connection is ready.
func (c *Client) BlockingDial(opts []grpc.DialOption) error {
	dialTime := defaultDialTimeout
	if c.conf.DialTimeout >= time.Second {
		dialTime = c.conf.DialTimeout
	}

	ctx, cancel := context.WithTimeout(c.ctx, dialTime)
	defer cancel()

	retCh := make(chan *grpc.ClientConn, 1)
	errCh := make(chan error, 1)

	dialer := func(ctx context.Context, address string) (net.Conn, error) {
		conn, err := (&net.Dialer{}).DialContext(ctx, "tcp", address)
		if err != nil {
			errCh <- err
		}

		return conn, err
	}

	threading.GoSafe(
		func() {
			// We put grpc.FailOnNonTempDialError *before* the explicitly provided
			// options so that it could be overridden.
			opts = append([]grpc.DialOption{grpc.FailOnNonTempDialError(true)}, opts...)
			// But we don't want caller to be able to override these two, so we put
			// them *after* the explicitly provided options.
			opts = append(opts, grpc.WithBlock(), grpc.WithContextDialer(dialer))

			conn, err := grpc.DialContext(ctx, c.address, opts...)
			if err != nil {
				errCh <- err
			} else {
				retCh <- conn
			}
		},
	)

	select {
	case err := <-errCh:
		return err
	case c.conn = <-retCh:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// InvokeRPC uses the gRPC channel of client to invoke the given method.
func (c *Client) InvokeRPC(ctx context.Context, req *Request, headers metadata.MD, resp *Response) error {
	if err := c.ReConnect(); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, headers)

	if req.md.IsStreamingClient() && req.md.IsStreamingServer() {
		return c.invokeBidiStream(ctx, req, resp)
	} else if req.md.IsStreamingClient() {
		return c.invokeClientStream(ctx, req, resp)
	} else if req.md.IsStreamingServer() {
		return c.invokeServerStream(ctx, req, resp)
	} else {
		return c.invokeUnary(ctx, req, resp)
	}
}

func (c *Client) invokeUnary(ctx context.Context, req *Request, resp *Response) error {
	if req.md == nil {
		return errors.New("the method descriptor of invokeUnary is null")
	}
	md := req.md
	in := req.md.Input()
	out := req.md.Output()

	// create dynamic message of request and response
	reqMsg := dynamicpb.NewMessage(in)
	respMsg := dynamicpb.NewMessage(out)

	// get the request data
	err := req.supplier(reqMsg)
	if err != nil && err != io.EOF {
		return errors.Errorf(
			"method[%s] is a unary RPC, but failed to get the request data, error: %v", md.FullName(), err,
		)
	}
	if err != io.EOF {
		// verify there is no second message, which is a usage error
		err = req.supplier(reqMsg)
		if err == nil {
			return errors.Errorf(
				"method[%s] is a unary RPC, but request data contained more than 1 message", md.FullName(),
			)
		} else if err != io.EOF {
			return errors.Errorf(
				"method[%s] is a unary RPC, but failed to get the request data, error: %v", md.FullName(), err,
			)
		}
	}

	// append the header and trailer to the []grpc.CallOption
	resp.header = make(metadata.MD)
	resp.trailer = make(metadata.MD)
	opts := append(req.opts, grpc.Trailer(&resp.trailer), grpc.Header(&resp.header))

	// invoke the unary RPC
	rm := RequestMethod{
		md:     md,
		method: req.method,
	}
	err = NewStub(c.conn).InvokeRpcUnary(ctx, rm, reqMsg, respMsg, opts...)

	var ok bool
	resp.status, ok = status.FromError(err)
	if !ok {
		return errors.Errorf("failed to call grpc method[%s], error: %v", req.md.FullName(), err)
	}

	resp.handler.OnReceiveHeaders(resp.header)
	if resp.status.Code() == codes.OK {
		resp.handler.OnReceiveResponse(respMsg)
	}
	resp.handler.OnReceiveTrailers(resp.status, resp.trailer)

	return err
}

func (c *Client) invokeClientStream(ctx context.Context, req *Request, resp *Response) error {
	if req.md == nil {
		return errors.New("the method descriptor of invokeClientStream is null")
	}
	md := req.md
	in := md.Input()
	out := md.Output()

	// create dynamic message of request and response
	reqMsg := dynamicpb.NewMessage(in)
	respMsg := dynamicpb.NewMessage(out)

	// invoke the client-stream RPC
	rm := RequestMethod{
		md:     md,
		method: req.method,
	}
	stream, err := NewStub(c.conn).InvokeRpcClientStream(ctx, rm)
	defer func() {
		if stream != nil && stream.cancel != nil {
			stream.cancel()
		}
	}()

	for err == nil && stream != nil {
		err = req.supplier(reqMsg)
		if err != nil && err != io.EOF {
			return errors.Errorf(
				"method[%s] is a client-stream RPC, but failed to get the request data, error: %v", md.FullName(), err,
			)
		} else if err == io.EOF {
			err = stream.CloseAndReceive(respMsg)
			break
		}

		// send message by the client-stream
		err = stream.SendMsg(reqMsg)
		if err == io.EOF {
			// we get EOF on send if the server says "go away"
			// we have to use CloseAndReceive to get the actual code
			err = stream.CloseAndReceive(respMsg)
			break
		}

		reqMsg.Reset()
	}

	var ok bool
	resp.status, ok = status.FromError(err)
	if !ok {
		return errors.Errorf("failed to call grpc method[%s], error: %v", req.md.FullName(), err)
	}

	if stream != nil {
		if resp.header, err = stream.Header(); err == nil {
			resp.handler.OnReceiveHeaders(resp.header)
		}
	}

	if resp.status.Code() == codes.OK {
		resp.handler.OnReceiveResponse(respMsg)
	}

	if stream != nil {
		resp.trailer = stream.Trailer()
		resp.handler.OnReceiveTrailers(resp.status, resp.trailer)
	}

	return nil
}

func (c *Client) invokeServerStream(ctx context.Context, req *Request, resp *Response) error {
	if req.md == nil {
		return errors.New("the method descriptor of invokeServerStream is null")
	}
	md := req.md
	in := md.Input()
	out := md.Output()

	// create dynamic message of request and response
	reqMsg := dynamicpb.NewMessage(in)
	respMsg := dynamicpb.NewMessage(out)

	// get the request data
	err := req.supplier(reqMsg)
	if err != nil && err != io.EOF {
		return errors.Errorf(
			"method[%s] is a server-stream RPC, but failed to get the request data, error: %v", md.FullName(), err,
		)
	}
	if err != io.EOF {
		// verify there is no second message, which is a usage error
		err = req.supplier(reqMsg)
		if err == nil {
			return errors.Errorf(
				"method[%s] is a server-stream RPC, but request data contained more than 1 message", md.FullName(),
			)
		} else if err != io.EOF {
			return errors.Errorf(
				"method[%s] is a server-stream RPC, but failed to get the request data, error: %v", md.FullName(), err,
			)
		}
	}

	// invoke the server-stream RPC
	rm := RequestMethod{
		md:     md,
		method: req.method,
	}
	stream, err := NewStub(c.conn).InvokeRpcServerStream(ctx, rm, reqMsg)

	if stream != nil {
		if resp.header, err = stream.Header(); err == nil {
			resp.handler.OnReceiveHeaders(resp.header)
		}
	}

	for err == nil && stream != nil {
		// receive message from the server-stream
		err = stream.RecvMsg(respMsg)
		if err != nil {
			if err == io.EOF {
				err = nil
			}
			break
		}

		resp.handler.OnReceiveResponse(respMsg)
		respMsg.Reset()
	}

	var ok bool
	resp.status, ok = status.FromError(err)
	if !ok {
		return errors.Errorf("failed to call grpc method[%s], error: %v", req.md.FullName(), err)
	}

	if stream != nil {
		resp.trailer = stream.Trailer()
		resp.handler.OnReceiveTrailers(resp.status, resp.trailer)
	}

	return nil
}

func (c *Client) invokeBidiStream(ctx context.Context, req *Request, resp *Response) error {
	if req.md == nil {
		return errors.New("the method descriptor of invokeBidiStream is null")
	}
	md := req.md
	in := md.Input()
	out := md.Output()

	// create dynamic message of request and response
	reqMsg := dynamicpb.NewMessage(in)
	respMsg := dynamicpb.NewMessage(out)

	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// invoke the bidi-stream
	rm := RequestMethod{
		md:     md,
		method: req.method,
	}
	stream, err := NewStub(c.conn).InvokeRpcBidiStream(ctx, rm)
	//if err != nil {
	//	return err
	//} else if stream == nil {
	//	return errors.Errorf("grpc call for %q failed: bidi stream is null", req.md.FullName())
	//}

	var wg sync.WaitGroup
	var sendErr atomic.Error

	defer wg.Wait()

	if err == nil && stream != nil {
		wg.Add(1)
		threading.GoSafe(
			func() {
				defer wg.Done()

				for err == nil {
					err = req.supplier(reqMsg)
					if err != nil && err != io.EOF {
						err = errors.Errorf(
							"method[%s] is a bidi-stream RPC, but failed to get the request data, error: %v",
							md.FullName(), err,
						)
						cancel()
						break
					} else if err == io.EOF {
						err = stream.CloseSend()
						break
					}

					// send message by the bidi-stream
					err = stream.SendMsg(reqMsg)
					if err == io.EOF {
						// We get EOF on send if the server says "go away"
						// We have to use CloseSend to get the actual code
						err = stream.CloseSend()
						break
					}

					reqMsg.Reset()
				}

				if err != nil {
					sendErr.Store(err)
				}
			},
		)
	}

	if stream != nil {
		if resp.header, err = stream.Header(); err == nil {
			resp.handler.OnReceiveHeaders(resp.header)
		}
	}

	for err == nil && stream != nil {
		// receive message from the bidi-stream
		err = stream.RecvMsg(respMsg)
		if err != nil {
			if err == io.EOF {
				err = nil
			}
			break
		}

		resp.handler.OnReceiveResponse(respMsg)
		respMsg.Reset()
	}

	if se := sendErr.Load(); se != io.EOF {
		err = se
	}

	var ok bool
	resp.status, ok = status.FromError(err)
	if !ok {
		return errors.Errorf("failed to call grpc method[%s], error: %v", req.md.FullName(), err)
	}

	if stream != nil {
		resp.trailer = stream.Trailer()
		resp.handler.OnReceiveTrailers(resp.status, resp.trailer)
	}

	return nil
}

// Close the client connection with the server.
func (c *Client) Close() error {
	return c.close()
}

func (c *Client) close() (err error) {
	c.connLock.Lock()
	defer c.connLock.Unlock()

	c.isClosed = true
	if c.conn != nil {
		err = c.conn.Close()
		// c.conn = nil
	}

	return err
}
