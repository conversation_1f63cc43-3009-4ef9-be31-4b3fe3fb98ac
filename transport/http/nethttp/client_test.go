package nethttp

import (
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

func TestClient_Send(t *testing.T) {
	ts := httptest.NewServer(
		http.HandlerFunc(
			func(w http.ResponseWriter, r *http.Request) {
				result := "Hello, client"

				if r.Method == http.MethodPost {
					body, _ := io.ReadAll(r.Body)
					result = "Hello, " + string(body)
				}

				_, _ = fmt.Fprintln(w, result)
			},
		),
	)
	defer ts.Close()

	c := NewClient(ClientConf{})

	type args struct {
		req     *Request
		timeout time.Duration
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "Method Get",
			args: args{
				req: func() *Request {
					return NewRequest(SetURL(ts.URL), SetMethod(http.MethodGet))
				}(),
				timeout: time.Second,
			},
			wantErr: false,
		},
		{
			name: "Method Post",
			args: args{
				req: func() *Request {
					return NewRequest(SetURL(ts.URL), SetMethod(http.MethodPost), SetBody([]byte("MethodPost")))
				}(),
				timeout: time.Second,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := c.Send(tt.args.req, tt.args.timeout)
				if (err != nil) != tt.wantErr {
					t.Errorf("Send() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				body, err := io.ReadAll(got.Body)
				if err != nil {
					t.Fatalf("failed to read body from response, error: %+v", err)
					return
				}
				defer got.Body.Close()

				t.Logf("Response: [%s] %s", got.Status, body)
			},
		)
	}
}
