package nethttp

import (
	"context"
	"crypto/tls"
	"net/http"
	"time"

	"github.com/zeromicro/go-zero/rest/httpc"

	h "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http"
)

const (
	defaultClientName = "httpc"
)

type (
	// ClientConf is http client side config
	ClientConf struct {
		BaseURL   string
		Transport *http.Transport
		TlsConfig *tls.Config // Deprecated: use `Transport.TLSClientConfig` instead.
	}

	// Client is a http client which wraps a *resty.Client
	Client struct {
		ctx  context.Context
		conf ClientConf

		c *http.Client
		s httpc.Service
	}

	Header   = http.Header
	Request  = http.Request
	Response = http.Response
)

// NewClient create http client
func NewClient(conf ClientConf) *Client {
	return NewClientWithContext(context.Background(), conf)
}

// NewClientWithContext create http client with context
func NewClientWithContext(ctx context.Context, conf ClientConf) *Client {
	if ctx == nil {
		ctx = context.Background()
	}

	var transport http.RoundTripper
	if conf.Transport != nil {
		transport = conf.Transport
	} else {
		transport = http.DefaultTransport
	}

	c := &http.Client{
		Transport: transport,
	}
	if conf.TlsConfig != nil {
		if t, ok := c.Transport.(*http.Transport); ok {
			t.TLSClientConfig = conf.TlsConfig
		}
	}

	return &Client{
		ctx:  ctx,
		conf: conf,
		c:    c,
		s:    httpc.NewServiceWithClient(defaultClientName, c),
	}
}

// BuildURL try to build a URL with base URL and path
func (c *Client) BuildURL(path string) string {
	return h.BuildURL(c.conf.BaseURL, path)
}

// Send sends the request to the server
func (c *Client) Send(req *Request, timeout time.Duration) (*Response, error) {
	if timeout <= 0 {
		timeout = h.DefaultTimeout
	}

	ctx, cancel := context.WithTimeout(req.Context(), timeout)
	defer cancel()

	return c.s.DoRequest(req.WithContext(ctx))
}

func (c *Client) Close() error {
	if c.c != nil {
		c.c.CloseIdleConnections()
	}

	return nil
}
