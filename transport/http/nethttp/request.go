package nethttp

import (
	"bytes"
	"context"
	"net/http"
)

type (
	request struct {
		ctx    context.Context
		url    string
		method string
		header http.Header
		body   []byte
	}

	Option func(*request)
)

func SetContext(ctx context.Context) Option {
	return func(r *request) {
		r.ctx = ctx
	}
}

func SetURL(url string) Option {
	return func(r *request) {
		r.url = url
	}
}

func SetMethod(method string) Option {
	return func(r *request) {
		r.method = method
	}
}

func SetHeaders(header http.Header) Option {
	return func(r *request) {
		r.header = header
	}
}

func SetBody(body []byte) Option {
	return func(r *request) {
		r.body = body
	}
}

func NewRequest(options ...Option) *Request {
	r := &request{
		ctx: context.Background(),
	}
	for _, option := range options {
		option(r)
	}

	req, err := http.NewRequestWithContext(r.ctx, r.method, r.url, bytes.NewReader(r.body))
	if err != nil {
		return nil
	}

	for k, vs := range r.header {
		for _, v := range vs {
			req.Header.Add(k, v)
		}
	}

	return req
}
