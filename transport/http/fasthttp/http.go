package fasthttp

import (
	"net/http"

	"github.com/valyala/fasthttp"
)

type Option func(*Request)

func AcquireRequest() *Request {
	return fasthttp.AcquireRequest()
}

func ReleaseRequest(req *Request) {
	fasthttp.ReleaseRequest(req)
}

func AcquireResponse() *Response {
	return fasthttp.AcquireResponse()
}

func ReleaseResponse(resp *Response) {
	fasthttp.ReleaseResponse(resp)
}

func SetURI(uri *URI) Option {
	return func(r *Request) {
		r.SetURI(uri)
	}
}

func SetURL(url string) Option {
	return func(r *Request) {
		r.SetRequestURI(url)
	}
}

func SetMethod(method string) Option {
	return func(r *Request) {
		r.Header.SetMethod(method)
	}
}

func SetHeader(header http.Header) Option {
	return func(r *Request) {
		for k, vs := range header {
			for _, v := range vs {
				r.Header.Set(k, v)
			}
		}
	}
}

func SetBody(body []byte) Option {
	return func(r *Request) {
		r.SetBody(body)
	}
}

func NewRequest(options ...Option) *Request {
	r := AcquireRequest()
	for _, option := range options {
		option(r)
	}

	return r
}
