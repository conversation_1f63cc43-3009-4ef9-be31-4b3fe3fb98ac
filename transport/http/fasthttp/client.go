package fasthttp

import (
	"context"
	"crypto/tls"
	"time"

	"github.com/valyala/fasthttp"

	h "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http"
)

type (
	// ClientConf is http client side config
	ClientConf struct {
		BaseURL      string
		IdleTimeout  time.Duration // MaxIdleConnDuration
		ReadTimeout  time.Duration
		WriteTimeout time.Duration
		TlsConfig    *tls.Config
	}

	// Client is a http client which wraps a *fasthttp.Client
	Client struct {
		ctx  context.Context
		conf ClientConf

		*fasthttp.Client
	}

	URI      = fasthttp.URI
	Header   = fasthttp.RequestHeader
	Request  = fasthttp.Request
	Response = fasthttp.Response
)

// NewClient create http client
func NewClient(conf ClientConf) *Client {
	return NewClientWithContext(context.Background(), conf)
}

// NewClientWithContext create http client with context
func NewClientWithContext(ctx context.Context, conf ClientConf) *Client {
	if ctx == nil {
		ctx = context.Background()
	}

	return &Client{
		ctx:  ctx,
		conf: conf,
		Client: &fasthttp.Client{
			TLSConfig:           conf.TlsConfig,
			MaxIdleConnDuration: conf.IdleTimeout,
			ReadTimeout:         conf.ReadTimeout,
			WriteTimeout:        conf.WriteTimeout,
		},
	}
}

// BuildURL try to build a URL with base URL and path
func (c *Client) BuildURL(path string) string {
	return h.BuildURL(c.conf.BaseURL, path)
}

// Send sends the request to the server
func (c *Client) Send(req *Request, resp *Response, timeout time.Duration) error {
	if timeout <= 0 {
		timeout = h.DefaultTimeout
	}

	return c.DoTimeout(req, resp, timeout)
}

func (c *Client) Close() error {
	if c.Client != nil {
		c.Client.CloseIdleConnections()
	}

	return nil
}
