package resty

import (
	"context"
	"crypto/tls"
	"net/http"
	"time"

	"github.com/go-resty/resty/v2"

	h "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http"
)

type (
	// ClientConf is http client side config
	ClientConf struct {
		BaseURL   string
		TlsConfig *tls.Config
	}

	// Client is a http client which wraps a *resty.Client
	Client struct {
		ctx  context.Context
		conf ClientConf

		*resty.Client
	}

	Header   = http.Header
	Request  = resty.Request
	Response = resty.Response
)

// NewClient create http client
func NewClient(conf ClientConf) *Client {
	return NewClientWithContext(context.Background(), conf)
}

// NewClientWithContext create http client with context
func NewClientWithContext(ctx context.Context, conf ClientConf) *Client {
	if ctx == nil {
		ctx = context.Background()
	}

	c := resty.New()
	if conf.BaseURL != "" {
		c = c.SetBaseURL(conf.BaseURL)
	}
	if conf.TlsConfig != nil {
		c = c.SetTLSClientConfig(conf.TlsConfig)
	}

	return &Client{
		ctx:    ctx,
		conf:   conf,
		Client: c,
	}
}

// BuildURL try to build a URL with base URL and path
func (c *Client) BuildURL(path string) string {
	return h.BuildURL(c.conf.BaseURL, path)
}

// Send sends the request to the server
func (c *Client) Send(req *Request, timeout time.Duration) (*Response, error) {
	if timeout <= 0 {
		timeout = h.DefaultTimeout
	}

	ctx, cancel := context.WithTimeout(req.Context(), timeout)
	defer cancel()

	return req.SetContext(ctx).EnableTrace().Send()
}

func (c *Client) Close() error {
	if c.Client != nil {
		transport, err := c.Client.Transport()
		if err != nil {
			return err
		}

		transport.CloseIdleConnections()
	}

	return nil
}
