package resty

import (
	"context"
	"net/http"
)

type Option func(*Request)

func SetContext(ctx context.Context) Option {
	return func(r *Request) {
		r.SetContext(ctx)
	}
}

func SetURL(url string) Option {
	return func(r *Request) {
		r.URL = url
	}
}

func SetMethod(method string) Option {
	return func(r *Request) {
		r.Method = method
	}
}

func SetHeaders(header http.Header) Option {
	return func(r *Request) {
		r.Header = header
	}
}

func SetBody(body []byte) Option {
	return func(r *Request) {
		r.Body = body
	}
}

func (c *Client) NewRequest(options ...Option) *Request {
	r := c.R()
	for _, option := range options {
		option(r)
	}

	return r
}
