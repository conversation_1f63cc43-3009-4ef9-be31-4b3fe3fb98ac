package http

import "testing"

func TestBuildURL(t *testing.T) {
	type args struct {
		baseURL string
		path    string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "base url without a slash at the end",
			args: args{
				baseURL: "https://dev-quality.ttyuyin.com",
				path:    "/manager/v1/project/search",
			},
			want: "https://dev-quality.ttyuyin.com/manager/v1/project/search",
		},
		{
			name: "base url with a slash at the end",
			args: args{
				baseURL: "https://dev-quality.ttyuyin.com/",
				path:    "/manager/v1/project/search",
			},
			want: "https://dev-quality.ttyuyin.com/manager/v1/project/search",
		},
		{
			name: "without base url",
			args: args{
				baseURL: "",
				path:    "https://dev-quality.ttyuyin.com/manager/v1/project/search",
			},
			want: "https://dev-quality.ttyuyin.com/manager/v1/project/search",
		},
		{
			name: "path with query",
			args: args{
				baseURL: "https://httpbin.org",
				path:    "/get?name=allen&age=18",
			},
			want: "https://httpbin.org/get?name=allen&age=18",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := BuildURL(tt.args.baseURL, tt.args.path); got != tt.want {
					t.Errorf("BuildURL() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestIsHTTPMethod(t *testing.T) {
	type args struct {
		method string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "Method: GET",
			args: args{
				method: "GET",
			},
			want: true,
		},
		{
			name: "Method: Post",
			args: args{
				method: "Post",
			},
			want: true,
		},
		{
			name: "Method: put",
			args: args{
				method: "put",
			},
			want: true,
		},
		{
			name: "Method: CREATE",
			args: args{
				method: "CREATE",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := IsHTTPMethod(tt.args.method); got != tt.want {
					t.Errorf("IsHTTPMethod() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
