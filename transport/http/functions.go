package http

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"golang.org/x/exp/slices"
)

var Methods = []string{
	http.MethodGet,     // RFC 7231, 4.3.1
	http.MethodHead,    // RFC 7231, 4.3.2
	http.MethodPost,    // RFC 7231, 4.3.3
	http.MethodPut,     // RFC 7231, 4.3.4
	http.MethodDelete,  // RFC 7231, 4.3.5
	http.MethodPatch,   // RFC 5789
	http.MethodOptions, // RFC 7231, 4.3.7
}

func BuildURL(baseURL, endpoint string) string {
	u, _ := url.Parse(endpoint)
	if u != nil && u.IsAbs() {
		return endpoint
	}

	return fmt.Sprintf("%s/%s", strings.TrimSuffix(baseURL, "/"), strings.TrimPrefix(endpoint, "/"))
}

func RequestMethod(method string) string {
	if IsHTTPMethod(method) {
		return strings.ToUpper(method)
	}

	return http.MethodGet
}

func IsHTTPMethod(method string) bool {
	return slices.ContainsFunc[[]string, string](
		Methods, func(s string) bool {
			return strings.EqualFold(s, method)
		},
	)
}

func NewHeader(m map[string]string) http.Header {
	h := make(http.Header, len(m))
	for k, v := range m {
		h.Add(k, v)
	}

	return h
}
