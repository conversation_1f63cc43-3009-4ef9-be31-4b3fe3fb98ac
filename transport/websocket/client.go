package websocket

import (
	"context"
	"crypto/tls"
	"io"
	"net"
	"net/http"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
)

const (
	queueLen         = 128
	handshakeTimeout = 45 * time.Second
)

type (
	// ClientProtocol interface for handling websocket client package.
	ClientProtocol interface {
		Recv(pkg []byte)
	}

	// ClientConf is websocket client side config
	ClientConf struct {
		QueueLen         int
		HandshakeTimeout time.Duration
		IdleTimeout      time.Duration
		ReadTimeout      time.Duration
		WriteTimeout     time.Duration
		DialTimeout      time.Duration
		TlsConfig        *tls.Config
	}

	// Client is a websocket client
	Client struct {
		ctx     context.Context
		address string
		cp      ClientProtocol
		conf    ClientConf

		conn          *websocket.Conn
		connLock      sync.Mutex
		connDone      chan lang.PlaceholderType
		sendQueue     chan sendMsg
		sendFailQueue chan sendMsg
		isClosed      atomic.Bool
		idleTime      time.Time
		invokeNum     int32
	}

	// sendMsg is a struct of sending message
	sendMsg struct {
		req   []byte
		retry uint8
	}
)

// NewClient create websocket client
func NewClient(address string, cp ClientProtocol, conf ClientConf) *Client {
	return NewClientWithContext(context.Background(), address, cp, conf)
}

// NewClientWithContext create websocket client with context
func NewClientWithContext(ctx context.Context, address string, cp ClientProtocol, conf ClientConf) *Client {
	if ctx == nil {
		ctx = context.Background()
	}
	if conf.QueueLen <= 0 {
		conf.QueueLen = queueLen
	}
	if conf.HandshakeTimeout <= 0 {
		conf.HandshakeTimeout = handshakeTimeout
	}

	c := &Client{
		ctx:     ctx,
		address: address,
		cp:      cp,
		conf:    conf,

		connDone:      make(chan lang.PlaceholderType, 1),
		sendQueue:     make(chan sendMsg, conf.QueueLen),
		sendFailQueue: make(chan sendMsg, 1),
	}
	c.isClosed.Store(true)

	return c
}

// ReConnect established the client connection with the server.
func (c *Client) ReConnect() (err error) {
	c.connLock.Lock()
	defer c.connLock.Unlock()

	if c.isClosed.Load() {
		logx.Debugf("Connect: %s", c.address)

		dialer := &websocket.Dialer{
			NetDial: func(network, addr string) (net.Conn, error) {
				d := &net.Dialer{Timeout: c.conf.DialTimeout}
				return d.Dial(network, addr)
			},
			Proxy:            http.ProxyFromEnvironment,
			TLSClientConfig:  c.conf.TlsConfig,
			HandshakeTimeout: c.conf.HandshakeTimeout,
			Subprotocols:     []string{"lws-minimal"},
		}

		var resp *http.Response
		c.conn, resp, err = dialer.DialContext(c.ctx, c.address, nil)
		if err != nil {
			if resp != nil {
				_ = resp.Body.Close()
			}
			c.connLock.Unlock()
			return err
		}
		if resp != nil {
			_ = resp.Body.Close()
		}

		c.idleTime = time.Now()
		c.isClosed.Store(false)
		threading.GoSafe(c.recv)
		threading.GoSafe(c.send)
	}

	return nil
}

// Send sends the request to the server as []byte.
func (c *Client) Send(req []byte) error {
	if err := c.ReConnect(); err != nil {
		return err
	}

	// avoid full sendQueue that cause sending block
	var timerC <-chan time.Time
	if c.conf.WriteTimeout > 0 {
		timerC = timewheel.After(c.conf.WriteTimeout)
	}

	select {
	case <-timerC:
		return errors.Errorf("websocket client write timeout[%s]", c.conf.WriteTimeout.String())
	case c.sendQueue <- sendMsg{req: req}:
	}

	return nil
}

// Close closes the client connection with the server.
func (c *Client) Close() error {
	return c.close()
}

// CloseGracefully close client gracefully.
func (c *Client) CloseGracefully(ctx context.Context) {
	t := timewheel.NewTicker(time.Millisecond * 500)
	defer t.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-t.C:
			logx.Debugf("wait grace invoke %d", c.invokeNum)
			if atomic.LoadInt32(&c.invokeNum) <= 0 {
				if err := c.Close(); err != nil {
					logx.Errorf("failed to close the connection of websocket client, error: %v", err)
				}
				return
			}
		}
	}
}

func (c *Client) send() {
	t := timewheel.NewTicker(time.Second)
	defer t.Stop()

	var m sendMsg
	for {
		select {
		case <-c.connDone: // connection closed
			return
		case m = <-c.sendFailQueue: // fetch a message from failure queue
		case m = <-c.sendQueue: // fetch a message from normal queue
		case <-t.C:
			if c.isClosed.Load() {
				return
			} else if c.invokeNum == 0 && c.idleTime.Add(c.conf.IdleTimeout).Before(time.Now()) {
				_ = c.close()
				return
			}
			continue
		}

		atomic.AddInt32(&c.invokeNum, 1)
		if c.conf.WriteTimeout > 0 {
			_ = c.conn.SetWriteDeadline(time.Now().Add(c.conf.WriteTimeout))
		}
		c.idleTime = time.Now()
		err := c.conn.WriteMessage(websocket.TextMessage, m.req)
		if err != nil {
			m.retry++
			c.sendQueue <- m
			logx.Errorf("failed to send message to websocket connection, retry: %d, error: %v", m.retry, err)

			_ = c.close()
			return
		}
	}
}

func (c *Client) recv() {
	defer func() {
		_ = c.close()
		c.connDone <- lang.Placeholder
	}()

	var (
		resp []byte
		err  error
	)

	for {
		if c.isClosed.Load() || c.conn == nil {
			logx.Debug("the connection has been closed")
			return
		}

		if c.conf.ReadTimeout > 0 {
			_ = c.conn.SetReadDeadline(time.Now().Add(c.conf.ReadTimeout))
		}

		_, resp, err = c.conn.ReadMessage()
		if err != nil {
			var netErr net.Error
			if errors.As(err, &netErr) && netErr.Timeout() {
				continue // no data, not error
			}

			var opError *net.OpError
			if errors.As(err, &opError) {
				if !c.isClosed.Load() && c.conn != nil {
					logx.Errorf("got an operation error from remote: %v, error: %v", c.conn.RemoteAddr(), err)
				}

				return // connection is closed
			}

			if err == io.EOF {
				logx.Debugf("connection closed by remote: %v, error: %v", c.conn.RemoteAddr(), err)
			} else {
				logx.Errorf("failed to read message from websocket connection, error: %v", err)
			}
			return
		}

		threading.GoSafe(
			func() {
				c.cp.Recv(resp)
			},
		)
	}
}

func (c *Client) close() (err error) {
	c.connLock.Lock()
	defer c.connLock.Unlock()

	c.isClosed.Store(true)
	if c.conn != nil {
		err = c.conn.Close()
		// c.conn = nil
	}

	return err
}
