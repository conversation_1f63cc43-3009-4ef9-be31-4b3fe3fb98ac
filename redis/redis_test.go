package redis

import (
	"strings"
	"testing"
)

func TestGetRedisVersionFromInfo(t *testing.T) {
	info := "# Server\nredis_version:5.2.3\nredis_git_sha1:f260bf7e\nredis_git_dirty:1\nredis_build_id:f318e37bdbdec2dc\nredis_mode:standalone\nos:Linux 3.10.107-1-tlinux2-0053 x86_64\narch_bits:64\nmultiplexing_api:epoll\natomicvar_api:sync-builtin\ngcc_version:4.4.6\nprocess_id:12736\nrun_id:f307d6a3aa02aae2bea4afc7ec393de389721aa5\ntcp_port:4278\nuptime_in_seconds:5969475\nuptime_in_days:69\nhz:10\nconfigured_hz:10\nlru_clock:10469360\nexecutable:/data/redis/app/redis-server-ignore-100055329-4278-1-ignore/./redis-server-ignore-100055329-4278-1-ignore\nconfig_file:/data/redis/app/redis-server-ignore-100055329-4278-1-ignore/redis-server-ignore-100055329-4278-1-ignore_redis.conf\n\n# Clients\nconnected_clients:16\nclient_recent_max_input_buffer:2\nclient_recent_max_output_buffer:0\nblocked_clients:0\n\n# Memory\nused_memory:36917120\nused_memory_human:35.21M\nused_memory_rss:43556864\nused_memory_rss_human:41.54M\nused_memory_peak:151437872\nused_memory_peak_human:144.42M\nused_memory_peak_perc:24.38%\nused_memory_overhead:36748854\nused_memory_startup:35370240\nused_memory_dataset:168266\nused_memory_dataset_perc:10.88%\nallocator_allocated:37441912\nallocator_active:39030784\nallocator_resident:49369088\nused_memory_lua:57344\nused_memory_lua_human:56.00K\nused_memory_scripts:8224\nused_memory_scripts_human:8.03K\nnumber_of_cached_scripts:14\nmaxmemory:1073741824\nmaxmemory_human:1.00G\nmaxmemory_policy:noeviction\nallocator_frag_ratio:1.04\nallocator_frag_bytes:1588872\nallocator_rss_ratio:1.26\nallocator_rss_bytes:10338304\nrss_overhead_ratio:0.88\nrss_overhead_bytes:-5812224\nmem_fragmentation_ratio:1.18\nmem_fragmentation_bytes:6682200\nmem_not_counted_for_evict:0\nmem_replication_backlog:1048576\nmem_clients_slaves:16970\nmem_clients_normal:304292\nmem_aof_buffer:0\nmem_allocator:jemalloc-5.1.0\nactive_defrag_running:0\nlazyfree_pending_objects:0\n\n# Persistence\nloading:0\nrdb_changes_since_last_save:42086852\nrdb_bgsave_in_progress:0\nrdb_last_save_time:1698998708\nrdb_last_bgsave_status:ok\nrdb_last_bgsave_time_sec:0\nrdb_current_bgsave_time_sec:-1\nrdb_last_cow_size:405504\naof_enabled:0\naof_rewrite_in_progress:0\naof_rewrite_scheduled:0\naof_last_rewrite_time_sec:-1\naof_current_rewrite_time_sec:-1\naof_last_bgrewrite_status:ok\naof_last_write_status:ok\naof_last_cow_size:0\n\n# Stats\ntotal_connections_received:4661641\ntotal_commands_processed:903591717\ninstantaneous_ops_per_sec:22\ntotal_net_input_bytes:1188895577166\ntotal_net_output_bytes:218575224406\ninstantaneous_input_kbps:1.61\ninstantaneous_output_kbps:25.25\nrejected_connections:0\nsync_full:1\nsync_partial_ok:0\nsync_partial_err:1\nexpired_keys:12269136\nexpired_stale_perc:0.00\nexpired_time_cap_reached_count:0\nevicted_keys:0\nkeyspace_hits:512617654\nkeyspace_misses:12879198\npubsub_channels:3\npubsub_patterns:0\nlatest_fork_usec:1030\nmigrate_cached_sockets:0\nslave_expires_tracked_keys:0\nactive_defrag_hits:0\nactive_defrag_misses:0\nactive_defrag_key_hits:0\nactive_defrag_key_misses:0\n\n# Replication\nrole:master\nconnected_slaves:1\nslave0:ip=*************,port=2038,state=online,offset=136663193794,lag=1\nmaster_replid:1872b0530eb7765b27769ebee3ae14d033d7f8de\nmaster_replid2:0000000000000000000000000000000000000000\nmaster_repl_offset:136663193794\nsecond_repl_offset:-1\nrepl_backlog_active:1\nrepl_backlog_size:1048576\nrepl_backlog_first_byte_offset:136662145219\nrepl_backlog_histlen:1048576\n\n# CPU\nused_cpu_sys:4772.212000\nused_cpu_user:14658.300000\nused_cpu_sys_children:0.000000\nused_cpu_user_children:0.000000\n\n# Cluster\ncluster_enabled:0\n\n# Keyspace\ndb0:keys=5,expires=4,avg_ttl=5801\n"
	match := redisVersionRE.FindAllStringSubmatch(info, -1)
	if len(match) < 1 {
		t.Fatalf("version not found, info: %s", info)
	}

	version := strings.TrimSpace(match[0][1])
	t.Logf("version: %s", version)
}
