package redis

import (
	"context"
	"crypto/tls"
	"io"
	"regexp"
	"strings"
	"time"

	"github.com/pkg/errors"
	redis "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/lang"
	red "github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/syncx"
)

const (
	maxRetries = 3
	idleConns  = 8

	addrSep = ","

	operationTimeout = 500 * time.Millisecond
	serverSection    = "server"
)

var (
	clientManager = syncx.NewResourceManager()

	redisVersionRE = regexp.MustCompile(`redis_version:(.+)`)
)

func NewClient(c red.RedisConf) redis.UniversalClient {
	var tlsConfig *tls.Config
	if c.Tls {
		tlsConfig = &tls.Config{
			InsecureSkipVerify: true, //nolint:gosec
		}
	}

	if c.Type == red.ClusterType {
		return redis.NewClusterClient(
			&redis.ClusterOptions{
				Addrs:        splitClusterAddresses(c.Host),
				Password:     c.Pass,
				MaxRetries:   maxRetries,
				MinIdleConns: idleConns,
				TLSConfig:    tlsConfig,
			},
		)
	}

	return redis.NewClient(
		&redis.Options{
			Addr:         c.Host,
			Password:     c.Pass,
			DB:           c.DB,
			MaxRetries:   maxRetries,
			MinIdleConns: idleConns,
			TLSConfig:    tlsConfig,
		},
	)
}

func GetClient(c red.RedisConf) (redis.UniversalClient, error) {
	val, err := clientManager.GetResource(
		c.Host, func() (io.Closer, error) {
			client := NewClient(c)
			return client, nil
		},
	)
	if err != nil {
		return nil, err
	}

	return val.(redis.UniversalClient), nil
}

func GetRedisVersion(rdb redis.UniversalClient) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), operationTimeout)
	defer cancel()

	info, err := rdb.Info(ctx, serverSection).Result()
	if err != nil {
		return "", err
	}

	match := redisVersionRE.FindAllStringSubmatch(info, -1)
	if len(match) < 1 {
		return "", errors.Errorf("could not extract redis version, %s section: %s", serverSection, info)
	}

	version := strings.TrimSpace(match[0][1])
	return version, nil
}

func splitClusterAddresses(address string) []string {
	addresses := strings.Split(address, addrSep)
	unique := make(map[string]lang.PlaceholderType)
	for _, each := range addresses {
		unique[strings.TrimSpace(each)] = lang.Placeholder
	}

	addresses = addresses[:0]
	for k := range unique {
		addresses = append(addresses, k)
	}

	return addresses
}
